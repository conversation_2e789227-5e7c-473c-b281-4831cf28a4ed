version: '3.8'

services:
  # Development service with hot reload
  proman-fe-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=development
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # Add your development environment variables here
      # - NEXTAUTH_SECRET=dev-secret
      # - NEXTAUTH_URL=http://localhost:3000
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    command: npm run dev

networks:
  default:
    name: proman-dev-network
