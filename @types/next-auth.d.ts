import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string;
      email?: string;
      image?: string;
      accessToken?: string;
      refreshToken?: string;
      sessionToken?: string;
      idToken?: string;
      picture?: string;
      locale?: string;
      given_name?: string;
      family_name?: string;
      provider?: string;
    } & DefaultSession['user'];
  }

  interface User {
    id: string;
    accessToken?: string;
    refreshToken?: string;
    sessionToken?: string;
    idToken?: string;
    picture?: string;
    locale?: string;
    given_name?: string;
    family_name?: string;
    provider?: string;
  }
}
