# Docker Setup for ProMan Frontend

This document provides instructions for running the ProMan Frontend application using Docker.

## Files Overview

- `Dockerfile` - Production-optimized multi-stage build
- `Dockerfile.dev` - Development build with hot reload
- `docker-compose.yml` - Production deployment
- `docker-compose.dev.yml` - Development environment
- `.dockerignore` - Files to exclude from Docker context

## Quick Start

### Production Build

```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build and run manually
docker build -t proman-fe .
docker run -p 3000:3000 proman-fe
```

### Development Build

```bash
# Run development environment with hot reload
docker-compose -f docker-compose.dev.yml up --build

# Or build and run manually
docker build -f Dockerfile.dev -t proman-fe-dev .
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules proman-fe-dev
```

## Environment Variables

Create a `.env.local` file or set environment variables:

```bash
# Authentication (if using NextAuth)
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# Database (if needed)
DATABASE_URL=your-database-connection-string

# API URLs
API_BASE_URL=http://localhost:8000

# Other environment variables
NODE_ENV=production
```

## Docker Commands

### Build Commands

```bash
# Build production image
docker build -t proman-fe .

# Build development image
docker build -f Dockerfile.dev -t proman-fe-dev .

# Build with specific tag
docker build -t proman-fe:v1.0.0 .
```

### Run Commands

```bash
# Run production container
docker run -p 3000:3000 proman-fe

# Run with environment variables
docker run -p 3000:3000 -e NODE_ENV=production proman-fe

# Run development container with volume mounting
docker run -p 3000:3000 -v $(pwd):/app -v /app/node_modules proman-fe-dev
```

### Docker Compose Commands

```bash
# Start production environment
docker-compose up -d

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build
```

## Production Deployment

### Using Docker Compose

1. Update environment variables in `docker-compose.yml`
2. Run: `docker-compose up -d`

### Using Docker Swarm

```bash
# Initialize swarm (if not already done)
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml proman-stack
```

### Using Kubernetes

Create deployment and service files:

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proman-fe
spec:
  replicas: 3
  selector:
    matchLabels:
      app: proman-fe
  template:
    metadata:
      labels:
        app: proman-fe
    spec:
      containers:
        - name: proman-fe
          image: proman-fe:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: 'production'
---
apiVersion: v1
kind: Service
metadata:
  name: proman-fe-service
spec:
  selector:
    app: proman-fe
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
```

## Optimization Features

### Multi-stage Build

- Separate build and runtime stages
- Smaller final image size
- Security improvements

### Standalone Output

- Next.js standalone output enabled
- Minimal runtime dependencies
- Faster container startup

### Non-root User

- Security best practice
- Runs as `nextjs` user (UID 1001)

## Troubleshooting

### Common Issues

1. **Port already in use**

   ```bash
   # Check what's using port 3000
   lsof -i :3000
   # Use different port
   docker run -p 3001:3000 proman-fe
   ```

2. **Permission issues**

   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

3. **Build failures**

   ```bash
   # Clear Docker cache
   docker system prune -a
   # Rebuild without cache
   docker build --no-cache -t proman-fe .
   ```

4. **Memory issues**
   ```bash
   # Increase Docker memory limit
   # Or build with limited parallelism
   docker build --build-arg NODE_OPTIONS="--max-old-space-size=4096" -t proman-fe .
   ```

### Debugging

```bash
# Access container shell
docker exec -it <container-id> sh

# View container logs
docker logs <container-id>

# Inspect container
docker inspect <container-id>
```

## Performance Tips

1. **Use .dockerignore** - Exclude unnecessary files
2. **Layer caching** - Order Dockerfile commands efficiently
3. **Multi-stage builds** - Separate build and runtime
4. **Health checks** - Add health check endpoints
5. **Resource limits** - Set memory and CPU limits

## Security Considerations

1. **Non-root user** - Already implemented
2. **Minimal base image** - Using Alpine Linux
3. **No secrets in image** - Use environment variables
4. **Regular updates** - Keep base images updated
5. **Scan images** - Use `docker scan` or similar tools

```bash
# Scan for vulnerabilities
docker scan proman-fe
```
