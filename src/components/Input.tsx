import { eyeoff, eyeon } from '@/utils/icon';
import { useState } from 'react';

interface InputProps {
  type?: string;
  value?: string | number; //controlled
  defaultValue?: string | number; //uncontrolled
  name?: string;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  onChangeFunc?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errorMessage?: string;
  maxLength?: number;
  minLength?: number;
  list?: string;
}
const Input = (props: InputProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordHandler = () => {
    setShowPassword(!showPassword);
  };
  let inputType = 'text';
  if (props.type) {
    inputType = props.type;
  }
  if (inputType === 'password') {
    inputType = showPassword ? 'text' : 'password';
  }

  return (
    <div className={`flex flex-col gap-2 ${props.className}`}>
      {props.label && (
        <label
          className={`text-[#0A0A0A] text-sm font-medium font-sans ${props.labelClassName}`}
        >
          {props.label}{' '}
          {props.required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          value={props.value}
          defaultValue={props.defaultValue}
          name={props.name}
          className={`w-full px-4 py-3 rounded-lg font-medium bg-[#FFFFFF] border border-gray-200 placeholder-gray-500 text-sm focus:border-2 focus:outline-none focus:border-[#EAB308] focus:bg-white ${props.inputClassName} ${props.errorMessage && 'border-red-500'}`}
          type={inputType}
          placeholder={props.placeholder || ''}
          onChange={props.onChangeFunc}
          disabled={props.disabled}
          maxLength={props.maxLength}
          minLength={props.minLength}
          list={props.list}
        />
        {props.type === 'password' && (
          <button
            onClick={togglePasswordHandler}
            className="absolute inset-y-0 right-3"
          >
            {showPassword ? eyeoff : eyeon}
          </button>
        )}
      </div>
      {props.errorMessage && (
        <p className="text-red-500 text-sm font-sans font-normal">
          {props.errorMessage}
        </p>
      )}
    </div>
  );
};
export default Input;
