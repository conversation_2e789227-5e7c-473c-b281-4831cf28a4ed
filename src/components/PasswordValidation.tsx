interface Validation {
  length: boolean;
  uppercase: boolean;
  lowercase: boolean;
  noSpace: boolean;
  specialChar: boolean;
}
const PasswordValidation = ({ validation }: { validation: Validation }) => {
  return (
    <div className="mt-5 bg-[#F1F5F9] px-4 py-2 rounded-lg">
      <p className="mb-2 text-sm text-[#0F172A]">Your password must include:</p>
      <ul>
        <li className={`text-sm`}>
          <div className="flex gap-1 items-center">
            <span
              className={`w-4 h-4 flex items-center justify-center ${validation.length ? 'text-[#16A34A]' : 'text-[#030712]'}`}
            >
              {validation.length ? '✓' : '•'}
            </span>
            <span className={`${validation.length && 'text-[#94A3B8]'}`}>
              At least 8 characters
            </span>
          </div>
        </li>
        <li className={`text-sm`}>
          <div className="flex gap-1 items-center">
            <span
              className={`w-4 h-4 flex items-center justify-center ${validation.uppercase ? 'text-[#16A34A]' : 'text-[#030712]'}`}
            >
              {validation.uppercase ? '✓' : '•'}
            </span>
            <span className={`${validation.length && 'text-[#94A3B8]'}`}>
              At least 1 uppercase letter
            </span>
          </div>
        </li>
        <li className={`text-sm`}>
          <div className="flex gap-1 items-center">
            <span
              className={`w-4 h-4 flex items-center justify-center ${validation.lowercase ? 'text-[#16A34A]' : 'text-[#030712]'}`}
            >
              {validation.lowercase ? '✓' : '•'}
            </span>
            <span className={`${validation.length && 'text-[#94A3B8]'}`}>
              At least 1 lowercase letter
            </span>
          </div>
        </li>

        <li className={`text-sm`}>
          <div className="flex gap-1 items-center">
            <span
              className={`w-4 h-4 flex items-center justify-center ${validation.specialChar ? 'text-[#16A34A]' : 'text-[#030712]'}`}
            >
              {validation.specialChar ? '✓' : '•'}
            </span>
            <span className={`${validation.length && 'text-[#94A3B8]'}`}>
              At least 1 special character
            </span>
          </div>
        </li>
        <li className={`text-sm`}>
          <div className="flex gap-1 items-center">
            <span
              className={`w-4 h-4 flex items-center justify-center ${validation.noSpace ? 'text-[#16A34A]' : 'text-[#030712]'}`}
            >
              {validation.noSpace ? '✓' : '•'}
            </span>
            <span className={`${validation.length && 'text-[#94A3B8]'}`}>
              No spaces
            </span>
          </div>
        </li>
      </ul>
    </div>
  );
};
export default PasswordValidation;
