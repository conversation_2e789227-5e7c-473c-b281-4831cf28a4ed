'use client';

import { useEffect, useRef, useState } from 'react';

interface DropDownMenuProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  dropdownClassName?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const DropDownMenu = ({
  trigger,
  children,
  className = '',
  dropdownClassName = '',
  position = 'top-right',
}: DropDownMenuProps) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Toggle dropdown
  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'bottom-full left-0 mb-2';
      case 'top-right':
        return 'top-full right-0 mt-2';
      case 'bottom-left':
        return 'top-full left-0 mt-2';
      case 'bottom-right':
        return 'top-full right-0 mt-2';
      default:
        return 'top-full right-0 mt-2';
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Trigger button */}
      <button
        ref={buttonRef}
        onClick={toggleDropdown}
        className="w-full h-full"
      >
        {trigger}
      </button>

      {/* Dropdown menu */}
      {showDropdown && (
        <div
          ref={dropdownRef}
          className={`absolute ${getPositionClasses()} bg-white rounded-lg shadow-lg z-10 min-w-40 ${dropdownClassName}`}
        >
          <div className="py-1">{children}</div>
        </div>
      )}
    </div>
  );
};

export default DropDownMenu;
