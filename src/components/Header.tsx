'use client';
import { useAppDispatch, useAppSelector } from '@/app/hook';
import { showHomeSidebarHandler } from '@/app/store/ui';
import { hamburger, ringBell } from '@/utils/icon';
import Image from 'next/image';
import Link from 'next/link';
const Header = () => {
  const dispatch = useAppDispatch();
  const showHomeSidebar = useAppSelector((state) => state.ui.showHomeSidebar);
  const toggleHomeSidebarHandler = () => {
    dispatch(showHomeSidebarHandler(!showHomeSidebar));
  };
  return (
    <header className="fixed w-full flex items-center justify-between p-4 bg-white shadow-sm">
      <div className="flex gap-4 items-center">
        <span className="cursor-pointer" onClick={toggleHomeSidebarHandler}>
          {hamburger}
        </span>
        <Link href={'/home'} className="flex items-center gap-2">
          <Image src="/logo.png" alt="logo" width={120} height={32} />
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <div className="relative">
          {ringBell}
          <div className="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-[#B91C1C] rounded-full -top-2 -end-2">
            3
          </div>
        </div>
        <span className="text-sm font-sans font-medium text-[#0A0A0A]">
          Nguyen Huu Tam
        </span>
      </div>
    </header>
  );
};
export default Header;
