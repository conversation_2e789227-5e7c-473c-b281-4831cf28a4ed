interface TooltipProps {
  children: React.ReactNode;
  className?: string;
}
const Tooltip = ({ children, className }: TooltipProps) => {
  return (
    <div
      role="tooltip"
      className={`absolute -translate-y-1/2 ml-2
              w-max bg-gray-800 text-white text-sm rounded px-3 py-1
              opacity-0 group-hover:opacity-100 transition-opacity duration-200
              z-10 tooltip ${className}`}
    >
      {children}
    </div>
  );
};

export default Tooltip;
