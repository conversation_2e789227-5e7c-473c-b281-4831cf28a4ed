'use client';
import { useAppSelector } from '@/app/hook';
import { spinner } from '@/utils/icon';

interface ButtonProps {
  className?: string;
  onClickFunc?: () => void;
  isValid?: boolean;
  children?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
}

export const Button = ({
  className,
  onClickFunc,
  isValid = true,
  children,
  type = 'button',
}: ButtonProps) => {
  const isLoading = useAppSelector((state) => state.ui.isLoading);

  let buttonText = children;
  if (isLoading) {
    buttonText = 'Loading';
  }

  return (
    <button
      className={`mt-5 tracking-wide font-semibold ${isValid && !isLoading ? 'bg-[#FACC15] hover:bg-[#FACC15]' : 'bg-[#E5E7EB] hover:bg-[#E5E7EB]'} text-gray-100 w-full py-3 rounded-lg transition-all duration-300 ease-in-out flex items-center justify-center focus:shadow-outline focus:outline-none ${className}`}
      onClick={onClickFunc}
      disabled={isLoading || !isValid}
      type={type}
    >
      {isLoading && (
        <div role="status" className="mr-3">
          {spinner}
          <span className="sr-only">Loading...</span>
        </div>
      )}
      <span
        className={`${isValid && !isLoading ? 'text-[#0F172A]' : 'text-[#94A3B8]'}`}
      >
        {buttonText}
      </span>
    </button>
  );
};

Button.defaultProps = {
  isValid: true,
};
