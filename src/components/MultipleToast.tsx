import { Toast } from '@shopify/polaris';
import '../app/polaris.css';

interface Props {
  content: string;
  duration?: number;
  action?: {
    content: string;
    onAction: () => void;
  };
  onDismiss: () => void;
  error?: boolean;
}

const MultipleToast = ({
  content,
  duration = 5000,
  action,
  onDismiss,
  error,
}: Props) => {
  return (
    <Toast
      content={content}
      duration={duration}
      action={action}
      onDismiss={onDismiss}
      {...(error ? { error: true } : {})}
    />
  );
};

export default MultipleToast;
