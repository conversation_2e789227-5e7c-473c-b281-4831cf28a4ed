import { EnumOption } from '@/types';
import { dropdown } from '@/utils/icon';

interface CustomSelectProps {
  value?: string; // controlled
  defaultValue?: string; // uncontrolled
  onChange: (value: string) => void;
  options: EnumOption[];
  placeholder?: string;
  multiple?: boolean;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  defaultValue,
  onChange,
  options,
  placeholder,
  multiple = false,
}) => (
  <div className="relative w-full">
    <select
      {...(defaultValue !== undefined ? { defaultValue } : { value })}
      onChange={(e) => onChange(e.target.value)}
      className="w-full text-sm font-medium border border-[#CBD5E1] rounded-lg py-3 px-4 pr-10 bg-white appearance-none focus:border-2 focus:outline-none focus:border-[#EAB308]"
      multiple={multiple}
    >
      <option value="">{placeholder}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    <span className="pointer-events-none absolute right-5 top-1/2 -translate-y-1/2 text-gray-400">
      {dropdown}
    </span>
  </div>
);
