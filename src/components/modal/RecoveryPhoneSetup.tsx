import { useState } from 'react';
import Input from '../Input';
import Modal from './Modal';

const RecoveryPhoneSetup = ({
  onClose,
  onConfirm,
}: {
  onClose: () => void;
  onConfirm: () => void;
}) => {
  const [isNext, setIsNext] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [vertificationCode, setVertificationCode] = useState('');
  const onConfirmHandler = () => {
    if (!isNext) {
      setIsNext(true);
    } else {
      onConfirm();
    }
  };
  let context = (
    <>
      <div className="mt-3 text-center sm:mt-0 sm:text-left">
        <p
          className="text-lg font-sans font-medium text-[#0F172A]"
          id="modal-title"
        >
          Add phone number
        </p>
        <p className="mt-2 text-sm font-normal text-[#1E293B]">
          For 2-step authentication
        </p>
      </div>
      <Input
        value={phoneNumber}
        onChangeFunc={(event) => {
          setPhoneNumber(event.target.value);
        }}
        label="Phone number"
        className="w-full mt-6"
      />
    </>
  );
  if (isNext) {
    context = (
      <>
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <p
            className="text-lg font-sans font-medium text-[#0F172A]"
            id="modal-title"
          >
            Confirm this phone number
          </p>
          <p className="mt-2 text-sm font-normal text-[#1E293B]">
            Get a verification code from {phoneNumber}
          </p>
        </div>
        <Input
          value={vertificationCode}
          onChangeFunc={(event) => {
            setVertificationCode(event.target.value);
          }}
          className="mt-6 w-full"
        />
      </>
    );
  }
  return (
    <Modal className="sm:w-full sm:max-w-lg" onClose={onClose}>
      <div className="bg-white">
        <div className="sm:flex sm:items-start sm:flex-col">{context}</div>
        <div
          className={`mt-6 flex items-center ${isNext ? 'justify-between' : 'justify-end'}`}
        >
          {isNext && (
            <p
              onClick={() => setIsNext(false)}
              className="text-sm font-normal text-[#64748B] cursor-pointer"
            >
              Back
            </p>
          )}
          <div className="sm:flex sm:flex-row-reverse">
            <button
              onClick={onConfirmHandler}
              type="button"
              className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
            >
              {isNext ? 'Confirm' : 'Next'}
            </button>
            <button
              onClick={onClose}
              type="button"
              className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default RecoveryPhoneSetup;
