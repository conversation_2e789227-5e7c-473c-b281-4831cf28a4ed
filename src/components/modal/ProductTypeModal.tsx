import React, { useEffect, useState } from 'react';
import { getProductTypesByNodeId } from '../../utils/api';
import { search as searchIc } from '../../utils/icon';
interface ProductTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (category: string) => void;
}

const ProductTypeModal: React.FC<ProductTypeModalProps> = ({
  isOpen,
  onClose,
  onSelect,
}) => {
  const [search, setSearch] = useState('');
  const [columns, setColumns] = useState<any[][]>([[]]); // Each column: array of categories
  const [selectedPath, setSelectedPath] = useState<any[]>([]); // Array of selected categories
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isOpen) return;
    getProductTypesByNodeId()
      .then((data) => {
        setColumns([data || []]);
        setSelectedPath([]);
      })
      .catch(() => {
        setError('Failed to load categories');
      })
      .finally(() => setLoading(false));
  }, [isOpen]);

  const filtered = search
    ? columns[0]?.filter((cat: any) =>
        cat.category_name.toLowerCase().includes(search.toLowerCase())
      )
    : columns[0] || []; // Only filter root categories

  if (!isOpen) return null;

  const selectCategoryHandler = async (category: any, level: number) => {
    setLoading(true);
    setError('');
    try {
      const children = await getProductTypesByNodeId(category.node_id);
      const newPath = [...selectedPath.slice(0, level), category];
      // Always set columns up to this node, and add children (even if empty)
      const newColumns = [...columns.slice(0, level + 1), children || []];
      setSelectedPath(newPath);
      setColumns(newColumns);
    } catch (err) {
      // On error, still set the selected path and clear further columns
      const newPath = [...selectedPath.slice(0, level), category];
      setSelectedPath(newPath);
      setColumns(columns.slice(0, level + 1));
      setError('Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    if (selectedPath.length > 0) {
      onSelect(selectedPath[selectedPath.length - 1].category_name);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-6xl p-6 relative min-h-[70vh] max-h-[80vh] flex flex-col gap-6 overflow-y-auto">
        {/* Close button */}
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-2xl"
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="22"
            height="22"
            viewBox="0 0 22 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11C20 6.02944 15.9706 2 11 2ZM0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 17.0751 17.0751 22 11 22C4.92487 22 0 17.0751 0 11ZM7.29289 7.29289C7.68342 6.90237 8.31658 6.90237 8.70711 7.29289L11 9.58579L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L12.4142 11L14.7071 13.2929C15.0976 13.6834 15.0976 14.3166 14.7071 14.7071C14.3166 15.0976 13.6834 15.0976 13.2929 14.7071L11 12.4142L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071C6.90237 14.3166 6.90237 13.6834 7.29289 13.2929L9.58579 11L7.29289 8.70711C6.90237 8.31658 6.90237 7.68342 7.29289 7.29289Z"
              fill="#475569"
            />
          </svg>
        </button>
        <h2 className="text-xl font-semibold">Select a product type</h2>
        {/* Search input */}
        <div className="relative">
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            {searchIc}
          </span>
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search product type"
            className="w-full rounded-lg border-2 border-[#CBD5E1] focus:border-yellow-500 outline-none px-4 py-2 text-base pl-10"
            autoFocus
          />
        </div>

        {/* Multi-column navigation */}
        <div className="overflow-x-auto">
          <h3 className="text-sm font-semibold mb-2">Browse</h3>
          <div className="flex-1 flex flex-row gap-2">
            {search ? (
              <div className="flex-1 min-w-[220px] bg-white rounded-lg border border-[#CBD5E1] overflow-y-auto">
                <div className="text-sm font-semibold text-[#0A0A0A] mb-1 px-4 pt-2">
                  {filtered.length} matching categories found
                </div>
                {filtered.map((cat: any) => (
                  <div
                    key={cat.node_id}
                    className="flex items-center justify-between px-4 py-2 border-b border-[#E2E8F0] last:border-b-0 cursor-pointer hover:bg-gray-100"
                  >
                    <span>{cat.category_name}</span>
                    <button
                      className="text-blue-600 font-medium hover:underline px-2"
                      onClick={() => selectCategoryHandler(cat, 0)}
                    >
                      Select
                    </button>
                  </div>
                ))}
                {filtered.length === 0 && (
                  <div className="text-gray-400 py-2 px-4">
                    No categories found
                  </div>
                )}
              </div>
            ) : (
              [...Array(4)].map((_, colIdx) => (
                <div
                  key={colIdx}
                  className="flex-1 min-w-[220px] bg-white rounded-lg border border-[#CBD5E1] overflow-y-auto"
                >
                  {columns[colIdx] && columns[colIdx].length > 0 ? (
                    columns[colIdx].map((cat: any) => {
                      const isSelected =
                        selectedPath[colIdx]?.node_id === cat.node_id;
                      return (
                        <div
                          key={cat.node_id}
                          className={`flex items-center justify-between px-4 py-2 border-b border-[#E2E8F0] last:border-b-0 cursor-pointer hover:bg-gray-100 ${isSelected ? 'text-yellow-600 font-bold bg-yellow-50' : ''}`}
                          onClick={() => selectCategoryHandler(cat, colIdx)}
                        >
                          <span>{cat.category_name}</span>
                          {cat.has_children && (
                            <span className="text-gray-400 text-lg font-bold">
                              <svg
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                  d="M8.29289 5.29289C8.68342 4.90237 9.31658 4.90237 9.70711 5.29289L15.7071 11.2929C16.0976 11.6834 16.0976 12.3166 15.7071 12.7071L9.70711 18.7071C9.31658 19.0976 8.68342 19.0976 8.29289 18.7071C7.90237 18.3166 7.90237 17.6834 8.29289 17.2929L13.5858 12L8.29289 6.70711C7.90237 6.31658 7.90237 5.68342 8.29289 5.29289Z"
                                  fill="#030712"
                                />
                              </svg>
                            </span>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-gray-400 py-2 px-4">
                      {colIdx === 0 ? 'Browse' : 'Select a category'}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex justify-between items-center">
          {/* Breadcrumb */}
          <div className="text-sm font-normal">
            <span className="text-[#94A3B8]">Currently selected: </span>
            {selectedPath.length === 0 ? (
              <span className="text-[#020617]">No type has been chosen</span>
            ) : (
              selectedPath.map((cat, idx) => (
                <span key={cat.node_id}>
                  {cat.category_name}
                  {idx < selectedPath.length - 1 && ' > '}
                </span>
              ))
            )}
          </div>
          <div className="flex gap-2">
            <button
              onClick={onClose}
              className="px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 font-medium"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 rounded-lg bg-[#FACC15] text-dark font-medium hover:bg-yellow-600 disabled:bg-gray-300"
              disabled={selectedPath.length === 0}
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductTypeModal;
