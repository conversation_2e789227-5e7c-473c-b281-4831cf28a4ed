import Input from '../Input';
import Modal from './Modal';

const DeleteAccount = ({ onClose }: { onClose: () => void }) => {
  const deleteAccountHandler = () => {
    alert('Delete account successfully');
  };
  return (
    <Modal onClose={onClose} className="sm:w-full sm:max-w-lg">
      <div className="bg-white">
        <div className="sm:flex sm:items-start sm:flex-col">
          <div className="mt-3 text-center sm:mt-0 sm:text-left">
            <p
              className="text-lg font-sans font-medium text-[#0F172A]"
              id="modal-title"
            >
              Delete account
            </p>
            <p className="mt-2 text-sm font-normal text-[#1E293B]">
              Are you sure you want to delete account? Your information will be
              permanently removed after 30 days.
            </p>
          </div>
          <Input
            value={''}
            onChangeFunc={() => {}}
            label="Enter 'delete my account' to confirm action"
            className="mt-6 w-full"
          />
        </div>
        <div className="mt-6 flex items-center justify-end">
          <div className="sm:flex sm:flex-row-reverse">
            <button
              onClick={deleteAccountHandler}
              type="button"
              className="text-white inline-flex w-full justify-center rounded-lg bg-[#DC2626] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#DC2626] sm:ml-3 sm:w-auto"
            >
              Delete
            </button>
            <button
              onClick={onClose}
              type="button"
              className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
            >
              Not Now
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteAccount;
