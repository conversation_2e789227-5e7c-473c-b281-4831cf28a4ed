import { close } from '@/utils/icon';
import Image from 'next/image';

interface ProductPreviewProps {
  product: {
    title: string;
    image: string;
    price?: number;
    sku?: string;
    brand?: string;
    color?: string;
    material?: string;
    dimensions?: string;
    features?: string[];
    description?: string;
  };
  onClose: () => void;
  onEdit: () => void;
}

const ProductPreview = ({ product, onClose, onEdit }: ProductPreviewProps) => {
  return (
    <div
      className="fixed inset-0 bg-black/60 bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 border-b">
          <div>
            <Image
              src="/assets/amazon.png"
              alt="Amazon"
              width={100}
              height={30}
              className="h-8 w-auto"
            />
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            {close}
          </button>
        </div>

        <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="flex flex-col items-center">
            <div className="w-full aspect-square relative mb-4">
              <Image
                src={'/assets/products/' + product.image}
                alt={product.title}
                fill
                className="object-contain"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto w-full">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="w-16 h-16 relative flex-shrink-0 border rounded"
                >
                  <Image
                    src={'/assets/products/' + product.image}
                    alt={`Thumbnail ${i + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          <div>
            <h1 className="text-xl font-medium mb-4">{product.title}</h1>

            <div className="mb-6">
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">$</span>
                <span className="text-4xl font-bold">32</span>
                <span className="text-sm text-gray-500 ml-2">
                  ($0.64 / Count)
                </span>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div className="grid grid-cols-3 gap-2">
                <div className="text-sm text-gray-600 font-semibold">Color</div>
                <div className="col-span-2 text-sm">White</div>

                <div className="text-sm text-gray-600 font-semibold">Brand</div>
                <div className="col-span-2 text-sm">Utopia Home</div>

                <div className="text-sm text-gray-600 font-semibold">
                  Material
                </div>
                <div className="col-span-2 text-sm">Plastic</div>

                <div className="text-sm text-gray-600 font-semibold">
                  Product Dimensions
                </div>
                <div className="col-span-2 text-sm">
                  16.1&quot;W x 3.2&quot;H
                </div>

                <div className="text-sm text-gray-600 font-semibold">
                  Special Features
                </div>
                <div className="col-span-2 text-sm">
                  Clothes hanger, Plastic hangers, Heavy Duty, Space Saving
                  Hangers, Closet Organizers and Storage
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="font-medium mb-2">About this item</h2>
              <ul className="list-disc pl-5 space-y-2 text-sm">
                <li>
                  Plastic Hangers 50 Pack - Perfect for clothes hangers, closet
                  organizers and storage with each hanger measuring 16.14 x 0.4
                  x 9.25 inches
                </li>
                <li>
                  Durable Coat Hanger - Made of strong and durable plastic that
                  is Perfect for your closet organizers and storage
                </li>
                <li>
                  Slim and Sleek Clothes hangers - Sleek and stark design
                  assists in maximizing closet space with a good number of
                  clothes at a time while giving a neat and tidy look to it
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex justify-end p-4 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 mr-2"
          >
            Close
          </button>
          <button
            onClick={onEdit}
            className="px-4 py-2 bg-yellow-400 rounded-lg text-gray-900"
          >
            Edit
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductPreview;
