import { close } from '@/utils/icon';

export const TermAndPolicy = ({
  setIsTermAndPolicy,
}: {
  setIsTermAndPolicy: (value: boolean) => void;
}) => {
  return (
    <div
      className="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:w-full sm:max-w-2xl px-6 pb-8 pt-3">
            <div
              className="flex justify-end cursor-pointer"
              onClick={() => setIsTermAndPolicy(false)}
            >
              {close}
            </div>
            <div className="bg-white">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left">
                  <p
                    className="text-lg font-sans font-medium text-[#0F172A]"
                    id="modal-title"
                  >
                    Term and Privacy Policy
                  </p>
                  <div className="mt-4">
                    <div className="flex flex-col gap-4">
                      <p className="text-sm font-sans font-normal text-[#1E293B]">
                        By proceeding to create an account, you confirm that you
                        have read, understood, and agreed to our{' '}
                        <a className="underline">Terms of Service</a> and{' '}
                        <a className="underline">Privacy Policy</a>.
                      </p>
                      <p className="text-sm font-sans font-normal text-[#1E293B]">
                        We collect and process certain basic personal
                        information for identity verification, account
                        protection, service provision, and to improve your
                        overall user experience. All data is securely stored and
                        handled in accordance with applicable laws and
                        regulations.
                      </p>
                      <p className="text-sm font-sans font-normal text-[#1E293B]">
                        We are committed to not sharing your personal
                        information with any third party without your consent,
                        except when required by law or regulatory authorities.
                        You may access, modify, or request the deletion of your
                        information at any time through your account settings.
                      </p>
                      <p className="text-sm font-sans font-normal text-[#1E293B]">
                        If you do not agree with any part of our Terms or
                        Privacy Policy, please do not proceed with the
                        registration process.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="sm:flex sm:flex-row-reverse mt-6">
              <button
                onClick={() => {
                  setIsTermAndPolicy(false);
                }}
                type="button"
                className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
              >
                I understand
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
