'use client';
import Modal from '@/components/modal/Modal';

interface PermissionMatrixModalProps {
  onClose: () => void;
}

const permissions = [
  'Create Listings',
  'Edit Listings',
  'Publish Listings',
  'Approve Listings',
  'Delete Listings',
  'Manage A+ Content',
  'View Analytics',
  'View Audit Logs',
  'Manage Billing',
  'Invite Users',
];

const roles = [
  {
    name: 'Super Admin',
    permissions: [true, true, true, true, true, true, true, true, true, true],
  },
  {
    name: 'Admin',
    permissions: [true, true, false, true, true, true, true, true, false, true],
  },
  {
    name: 'Regional Admin',
    permissions: [true, true, false, true, true, true, true, true, false, true],
  },
  {
    name: 'Support Admin',
    permissions: [
      false,
      false,
      false,
      false,
      false,
      false,
      true,
      true,
      false,
      false,
    ],
  },
  {
    name: 'Team Member',
    permissions: [
      true,
      true,
      false,
      false,
      false,
      true,
      true,
      false,
      false,
      false,
    ],
  },
];

export default function PermissionMatrixModal({
  onClose,
}: PermissionMatrixModalProps) {
  const renderPermissionIcon = (hasPermission: boolean) => {
    if (hasPermission) {
      return (
        <div className="w-4 h-4 flex items-center justify-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M20.7071 5.29289C21.0976 5.68342 21.0976 6.31658 20.7071 6.70711L9.70711 17.7071C9.31658 18.0976 8.68342 18.0976 8.29289 17.7071L3.29289 12.7071C2.90237 12.3166 2.90237 11.6834 3.29289 11.2929C3.68342 10.9024 4.31658 10.9024 4.70711 11.2929L9 15.5858L19.2929 5.29289C19.6834 4.90237 20.3166 4.90237 20.7071 5.29289Z"
              fill="#030712"
            />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="w-4 h-4 flex items-center justify-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21 12C21 7.02944 16.9706 3 12 3C9.87498 3 7.92249 3.73712 6.38281 4.96875L19.0303 17.6162C20.2619 16.0766 21 14.125 21 12ZM3 12C3 16.9706 7.02944 21 12 21C14.125 21 16.0766 20.2619 17.6162 19.0303L4.96875 6.38281C3.73712 7.92249 3 9.87498 3 12ZM23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12Z"
              fill="#030712"
            />
          </svg>
        </div>
      );
    }
  };

  return (
    <Modal onClose={onClose} className="sm:max-w-4xl">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Permission/Role</h3>
      </div>

      <div className="overflow-auto max-h-[60vh]">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900 min-w-[200px]"></th>
                {roles.map((role) => (
                  <th
                    key={role.name}
                    className="text-center py-3 px-4 font-medium text-gray-900 min-w-[120px]"
                  >
                    {role.name}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {permissions.map((permission, permissionIndex) => (
                <tr
                  key={permission}
                  className="border-b border-gray-100 hover:bg-gray-50"
                >
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">
                    {permission}
                  </td>
                  {roles.map((role, roleIndex) => (
                    <td
                      key={`${permission}-${role.name}`}
                      className="py-3 px-4 text-center"
                    >
                      {renderPermissionIcon(role.permissions[permissionIndex])}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex justify-end mt-6">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
        >
          Close
        </button>
      </div>
    </Modal>
  );
}
