import { close } from '@/utils/icon';
import { CustomSelect } from '../CustomSelect';
import Input from '../Input';

const AddEditPaymentMethod = ({
  onClose,
  onConfirm,
}: {
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <div
      className="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div className="fixed inset-0 z-10 w-screen">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl p-6">
            <div className="flex justify-end cursor-pointer" onClick={onClose}>
              {close}
            </div>
            <div className="bg-white">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left">
                  <p
                    className="text-lg font-sans font-medium text-[#0F172A]"
                    id="modal-title"
                  >
                    Add credit or debit card
                  </p>
                  <div className="max-w-3xl mx-auto mt-6">
                    <form className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Input
                            label="Card number"
                            value={''}
                            onChangeFunc={() => {}}
                          />
                        </div>
                        <div>
                          <Input
                            label="Expires"
                            value={''}
                            placeholder="MM / YY"
                            onChangeFunc={() => {}}
                          />
                        </div>
                        <div>
                          <Input
                            label="CVV"
                            value={''}
                            onChangeFunc={() => {}}
                          />
                        </div>
                      </div>

                      <div>
                        <label className="font-medium block text-sm mb-1">
                          Country/Region
                        </label>
                        <CustomSelect
                          value={''}
                          onChange={() => {}}
                          options={[
                            { value: 'Germany', label: 'Germany' },
                            { value: 'United States', label: 'United States' },
                            { value: 'France', label: 'France' },
                          ]}
                          placeholder="Country/Region"
                          multiple={false}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                          label="First name"
                          value={'Tam'}
                          onChangeFunc={() => {}}
                        />

                        <Input
                          label="Last name"
                          value={'Nguyen'}
                          onChangeFunc={() => {}}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                          label="Address"
                          value={'123 West 9th Street'}
                          onChangeFunc={() => {}}
                        />
                        <Input
                          label="Apartment, suite, etc"
                          value={'12th floor'}
                          onChangeFunc={() => {}}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                          label="City"
                          value={'Coffeyville'}
                          onChangeFunc={() => {}}
                        />
                        <Input
                          label="Zip Code"
                          value={'67337'}
                          onChangeFunc={() => {}}
                        />
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
            <div className="sm:flex sm:flex-row-reverse mt-6">
              <button
                onClick={onConfirm}
                type="button"
                className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
              >
                Confirm
              </button>
              <button
                onClick={onClose}
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AddEditPaymentMethod;
