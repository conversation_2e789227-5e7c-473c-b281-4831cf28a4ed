'use client';
import Modal from '@/components/modal/Modal';
import { useState } from 'react';

interface InviteUserModalProps {
  onClose: () => void;
  onViewPermissionMatrix: () => void;
}

const roles = ['Admin', 'Regional Admin', 'Support Admin', 'Team Member'];

export default function InviteUserModal({
  onClose,
  onViewPermissionMatrix,
}: InviteUserModalProps) {
  const [email, setEmail] = useState('<EMAIL>');
  const [selectedRole, setSelectedRole] = useState('');
  const [showRoleDropdown, setShowRoleDropdown] = useState(false);

  const handleSendInvite = () => {
    // Handle send invite logic
    console.log('Sending invite to:', email, 'with role:', selectedRole);
    onClose();
  };

  const handleDiscard = () => {
    onClose();
  };

  const handleAssignRole = (role: string) => {
    setSelectedRole(role);
    setShowRoleDropdown(false);
  };

  return (
    <Modal onClose={onClose} className="sm:max-w-md">
      <div className="flex items-center mb-4">
        <button
          onClick={onClose}
          className="mr-3 p-1 hover:bg-gray-100 rounded"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h3 className="text-lg font-semibold text-gray-900">Invite user</h3>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Email *
        </label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter email address"
        />
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            Roles
          </label>
          <button
            onClick={onViewPermissionMatrix}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            View Permission/Role
          </button>
        </div>
        <p className="text-sm text-gray-500 mb-3">
          Assign roles to grant permissions.
        </p>

        <div className="relative">
          <button
            onClick={() => setShowRoleDropdown(!showRoleDropdown)}
            className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-700">
                {selectedRole || 'Assign role'}
              </span>
            </div>
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {showRoleDropdown && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              {roles.map((role) => (
                <button
                  key={role}
                  onClick={() => handleAssignRole(role)}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-md last:rounded-b-md"
                >
                  {role}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          onClick={handleDiscard}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Discard
        </button>
        <button
          onClick={handleSendInvite}
          className="px-4 py-2 bg-yellow-500 text-white rounded-md text-sm font-medium hover:bg-yellow-600"
          disabled={!email || !selectedRole}
        >
          Send invite
        </button>
      </div>
    </Modal>
  );
}
