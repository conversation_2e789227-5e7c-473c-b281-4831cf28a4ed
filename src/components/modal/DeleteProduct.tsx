import { close } from '@/utils/icon';
import Image from 'next/image';
import { useState } from 'react';

interface DeleteProductModalProps {
  product: {
    title: string;
    image: string;
    sku?: string;
  };
  onClose: () => void;
  onDelete: () => void;
}

const DeleteProductModal = ({
  product,
  onClose,
  onDelete,
}: DeleteProductModalProps) => {
  const [asin, setAsin] = useState('');
  const [error, setError] = useState(false);

  const handleDelete = () => {
    // In a real app, you might want to validate the ASIN
    // For now, we'll just call the onDelete function
    onDelete();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAsin(e.target.value);
    setError(false);
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">Delete product listing</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              {close}
            </button>
          </div>

          <p className="text-lg mb-6">
            Are you sure you want to delete this product listing? This action
            cannot be undone.
          </p>

          <div className="border border-[#CBD5E1] rounded-lg p-4 mb-6">
            <div className="flex gap-4">
              <div className="w-16 h-16 relative flex-shrink-0">
                <Image
                  src={'/assets/products/' + product.image}
                  alt={product.title}
                  fill
                  className="object-cover rounded"
                />
              </div>
              <div className="flex-grow">
                <h3 className="font-medium line-clamp-2">{product.title}</h3>
                <div className="mt-2 bg-gray-100 inline-block px-3 py-1 rounded-md">
                  <span className="text-gray-500 mr-2">ASIN:</span>
                  <span className="font-medium">
                    {product.sku || 'B0BYX1XT24'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-lg font-medium mb-2">
              Enter your product listing ASIN
            </label>
            <input
              type="text"
              className={`w-full p-3 border ${error ? 'border-red-500' : 'border-gray-300'} rounded-lg`}
              value={asin}
              onChange={handleInputChange}
              placeholder="Enter ASIN to confirm deletion"
            />
            {error && <p className="text-red-600 mt-1">Incorrect ASIN</p>}
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-6 py-3 bg-white border border-gray-300 rounded-lg text-gray-700 font-medium"
            >
              Not now
            </button>
            <button
              onClick={handleDelete}
              className="px-6 py-3 bg-red-600 rounded-lg text-white font-medium"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteProductModal;
