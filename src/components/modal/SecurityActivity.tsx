import { close } from '@/utils/icon';
import Image from 'next/image';

const SecurityActivity = ({
  <PERSON><PERSON><PERSON><PERSON>,
  yes<PERSON><PERSON><PERSON>,
  closeHand<PERSON>,
}: {
  noHandler: () => void;
  yesHandler: () => void;
  closeHandler: () => void;
}) => {
  return (
    <div
      className="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:w-full sm:max-w-lg px-6 pb-8 pt-3">
            <div
              className="flex justify-end cursor-pointer"
              onClick={closeHandler}
            >
              {close}
            </div>
            <div className="bg-white">
              <div className="mt-3 text-center sm:mt-0 sm:text-left">
                <p className="text-sm font-normal text-[#0F172A]">
                  March 10, 8:40 AM
                </p>
                <p
                  className="text-lg font-sans font-medium text-[#0F172A]"
                  id="modal-title"
                >
                  New sign-in on Mac
                </p>
                <p className="text-sm font-normal text-[#1E293B] mt-2">
                  Your account is at risk if this wasn’t you
                </p>
                <div className="mt-4">
                  <div className="rounded-lg border border-[#CBD5E1] p-4 flex gap-4">
                    <div>
                      <Image
                        width={64}
                        height={64}
                        src={'../../assets/laptop.svg'}
                        alt="laptop"
                      />
                    </div>
                    <div>
                      <p className="text-base font-semibold text-[#0F172A]">
                        Mac
                      </p>
                      <p className="text-sm font-normal text-[#0F172A]">
                        tamnh1s-Macbook-pro
                      </p>
                      <p className="text-sm font-normal text-[#0F172A]">
                        Hanoi, Vietnam
                      </p>
                    </div>
                  </div>
                </div>
                <p className="mt-4 text-sm font-normal text-[#1E293B]">
                  Do you recognize this activity?
                </p>
              </div>
            </div>
            <div className="sm:flex sm:flex-row-reverse mt-6">
              <button
                onClick={yesHandler}
                type="button"
                className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
              >
                Yes, it was me
              </button>
              <button
                onClick={noHandler}
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-[#B91C1C] shadow-xs hover:bg-gray-50 sm:mt-0 sm:w-auto border-[#B91C1C] border"
              >
                No, secure account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityActivity;
