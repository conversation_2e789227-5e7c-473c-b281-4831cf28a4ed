import { activate2FA, verify2FA } from '@/utils/api';
import Cookies from 'js-cookie';
import { useQRCode } from 'next-qrcode';
import { useState } from 'react';
import Input from '../Input';
import Modal from './Modal';

type Type = 'activate' | 'verify';

const AuthenticatorSetup = ({
  onClose,
  onConfirm,
  QRCodeURL,
  type = 'activate',
  has2FA = false,
  sessionToken,
}: {
  onClose: () => void;
  onConfirm: () => void;
  QRCodeURL?: string;
  type?: Type;
  has2FA?: boolean;
  sessionToken?: string;
}) => {
  const [isNext, setIsNext] = useState(has2FA);
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const { Canvas } = useQRCode();

  let context = null;

  const onConfirmHandler = async () => {
    if (!isNext) {
      setIsNext(true);
    } else {
      try {
        if (type === 'activate') {
          await activate2FA(code);
        } else {
          const response = await verify2FA(code, sessionToken);
          if (response.status === 200) {
            const { session_token } = response.data.meta;
            Cookies.set('session_token', session_token, {
              expires: 1,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'Strict',
            });
            Cookies.set('user_id', response.data.data.user.id);
          }
        }
        onConfirm();
      } catch (error) {
        const data = error.response.data;
        if (data.status == 401) {
          if (data.meta.is_authenticated) onConfirm();
        } else {
          setError(data.errors[0].message);
        }
      }
    }
  };

  if (!isNext && !has2FA) {
    context = (
      <>
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <p
            className="text-lg font-sans font-medium text-[#0F172A]"
            id="modal-title"
          >
            Set up authenticator app
          </p>
          <ul className="list-disc ml-3 mt-2">
            <li>Access your Authenticator app</li>
            <li>Choose Scan a QR code</li>
          </ul>
        </div>
        <div className="flex justify-center items-center w-full my-20">
          <Canvas
            text={QRCodeURL}
            options={{
              margin: 3,
              scale: 4,
              width: 200,
            }}
          />
        </div>
      </>
    );
  }
  if (isNext) {
    context = (
      <>
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <p
            className="text-lg font-sans font-medium text-[#0F172A]"
            id="modal-title"
          >
            2-Step Verification
          </p>
          <p className="mt-2 text-sm font-normal text-[#1E293B]">
            Get a verification code from your authenticator app
          </p>
        </div>
        <Input
          value={code}
          onChangeFunc={(event) => {
            setCode(event.target.value);
          }}
          className="mt-2 w-full"
        />
        {error && (
          <p className="mt-2 text-sm font-normal text-red-500">{error}</p>
        )}
      </>
    );
  }
  return (
    <Modal className="sm:w-full sm:max-w-lg" onClose={onClose}>
      <div className="bg-white">
        <div className="sm:flex sm:items-start sm:flex-col">{context}</div>
        <div
          className={`mt-6 flex items-center ${isNext && !has2FA ? 'justify-between' : 'justify-end'}`}
        >
          {isNext && !has2FA && (
            <p
              onClick={() => setIsNext(false)}
              className="text-sm font-normal text-[#64748B] cursor-pointer"
            >
              Back
            </p>
          )}
          <div className="sm:flex sm:flex-row-reverse">
            <button
              onClick={onConfirmHandler}
              type="button"
              className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
            >
              {isNext ? 'Confirm' : 'Next'}
            </button>
            <button
              onClick={onClose}
              type="button"
              className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AuthenticatorSetup;
