import { close } from '@/utils/icon';

export const DuplicateMail = ({
  setIsAlreadyRegistered,
}: {
  setIsAlreadyRegistered: (value: boolean) => void;
}) => {
  return (
    <div
      className="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl p-6">
            <div
              className="flex justify-end cursor-pointer"
              onClick={() => setIsAlreadyRegistered(false)}
            >
              {close}
            </div>
            <div className="bg-white">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left">
                  <p
                    className="text-lg font-sans font-medium text-[#0F172A]"
                    id="modal-title"
                  >
                    This email account has already been registered.
                  </p>
                  <div className="mt-4">
                    <p className="text-sm font-sans font-normal text-[#1E293B]">
                      Please log in or use a different email account.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="sm:flex sm:flex-row-reverse mt-6">
              <button
                onClick={() => {
                  setIsAlreadyRegistered(false);
                }}
                type="button"
                className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
              >
                Try again
              </button>
              <button
                onClick={() => {
                  setIsAlreadyRegistered(false);
                }}
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
