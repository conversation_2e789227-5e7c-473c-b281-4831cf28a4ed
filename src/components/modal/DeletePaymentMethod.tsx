import { cardIc, close } from '@/utils/icon';

const DeletePaymentMethod = ({
  onClose,
  onConfirm,
}: {
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <div
      className="relative z-10"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div className="fixed inset-0 z-10 w-screen">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl p-6">
            <div className="flex justify-end cursor-pointer" onClick={onClose}>
              {close}
            </div>
            <div className="bg-white">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left">
                  <p
                    className="text-lg font-sans font-medium text-[#0F172A]"
                    id="modal-title"
                  >
                    Delete payment method
                  </p>
                  <div className="max-w-3xl mx-auto mt-6">
                    <p>
                      Are you sure you want to delete this payment method? This
                      action cannot be undone.
                    </p>
                    <div className="mt-6 border-1 border-[#CBD5E1] rounded-lg p-4 flex justify-between items-center">
                      <div className="flex gap-4 items-stretch">
                        <div className="flex items-center">{cardIc}</div>
                        <div className="min-w-[135px]">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium">Credit Card</p>
                            <div className="bg-[#FFE600] p-1 text-xs font-medium rounded-lg">
                              Primary
                            </div>
                          </div>
                          <h1 className="font-normal text-sm text-[#0F172A]">
                            **** **** **** 1234
                          </h1>
                        </div>
                        <div className="flex items-end">
                          <h3 className="text-sm font-normal text-[#64748B]">
                            Expire 08/2025
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="sm:flex sm:flex-row-reverse mt-6">
              <button
                onClick={onConfirm}
                type="button"
                className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
              >
                Confirm
              </button>
              <button
                onClick={onClose}
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default DeletePaymentMethod;
