interface RadioProps {
  name?: string;
  checked?: boolean; // controlled
  defaultChecked?: boolean; // uncontrolled
  required?: boolean;
  disabled?: boolean;
  label?: string;
  className?: string;
  labelClassName?: string;
  onChangeFunc?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errorMessage?: string;
}
const RadioInput = (props: RadioProps) => {
  return (
    <label
      className={`flex items-center gap-1 cursor-pointer mt-1 ${props.labelClassName}`}
    >
      <input
        type="radio"
        name={props.name}
        {...(props.defaultChecked !== undefined
          ? { defaultChecked: props.defaultChecked }
          : { checked: props.checked })}
        onChange={props.onChangeFunc}
        className={`accent-black w-5 h-5 border-[#030712] ${props.className}`}
      />
      {props.label}
    </label>
  );
};
export default RadioInput;
