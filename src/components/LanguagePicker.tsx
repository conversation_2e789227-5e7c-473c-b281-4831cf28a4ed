import { earth } from '@/utils/icon';
import { languages } from '../utils/util';

const LanguagePicker = () => {
  return (
    <div className="absolute right-[40px] top-[25px] flex gap-1">
      {earth}
      <select className="w-20 text-sm">
        {languages.map((language, index) => (
          <option key={index} value={language}>
            {' '}
            {language}{' '}
          </option>
        ))}
      </select>
    </div>
  );
};
export default LanguagePicker;
