import { useRef, useState } from 'react';

const OTP = () => {
  const [OTP, setOTP] = useState<string[]>(Array(6).fill(''));
  const OTPRefs = useRef<(HTMLInputElement | null)[]>([]);
  const handleOTPChange = (index: number, value: string) => {
    if (!/^[a-zA-Z0-9]?$/.test(value)) return;

    setOTP((prevOTP) => {
      const newOTP = [...prevOTP];
      newOTP[index] = value.toUpperCase();

      // Move focus to the next input OTP
      if (value && index < 5) {
        OTPRefs.current[index + 1]?.focus();
      }

      return newOTP;
    });
  };

  // Move focus to previous input if OTP at index is wrong
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === 'Backspace' && !OTP[index] && index > 0) {
      OTPRefs.current[index - 1]?.focus();
    }
  };

  return (
    <div className="flex flex-row items-center justify-between mx-auto w-full max-w-xs gap-4">
      {OTP.map((digit, index) => (
        <div className="w-12 h-12" key={index}>
          <input
            type="text"
            value={digit}
            maxLength={1}
            className="font-medium font-sans w-full h-full flex flex-col items-center justify-center text-center rounded-lg bg-[#FFFFFF] border border-gray-200 placeholder-gray-500 text-lg focus:border-2 focus:outline-none focus:border-[#EAB308] focus:bg-white"
            ref={(el) => {
              OTPRefs.current[index] = el;
            }}
            onChange={(e) => handleOTPChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
          />
        </div>
      ))}
    </div>
  );
};
export default OTP;
