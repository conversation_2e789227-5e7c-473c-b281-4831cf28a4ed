'use client';
import { useAppSelector } from '@/app/hook';
import { NavItem } from '@/types';
import {
  analytics,
  analyticsChecked,
  aplusContent,
  aplusContentChecked,
  connection,
  general,
  home,
  homeChecked,
  languageAndRegion,
  notification,
  productListing,
  productListingChecked,
  security,
  setting,
  subscriptionAndBilling,
  userManagement,
} from '@/utils/icon';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import Tooltip from './Tooltip';
export const navItems: NavItem[] = [
  {
    title: 'Home Page',
    icon: home,
    iconChecked: homeChecked,
    path: '/',
    children: [
      { title: 'General', icon: general, path: '/general' },
      {
        title: 'Subscription & Billing',
        icon: subscriptionAndBilling,
        path: '/subscription-and-billing',
      },
      {
        title: 'Notifications',
        icon: notification,
        path: '/notification',
      },
      {
        title: 'Security',
        icon: security,
        path: '/security',
      },
      {
        title: 'Connections',
        icon: connection,
        path: '/connection',
      },
      {
        title: 'User Management',
        icon: userManagement,
        path: '/user-management',
      },
      {
        title: 'Language and Region',
        icon: languageAndRegion,
        path: '/language-and-region',
      },
    ],
  },
  {
    title: 'Product Listing',
    icon: productListing,
    iconChecked: productListingChecked,
    path: '/product-listing',
    children: [
      { title: 'Your Store', icon: undefined, path: '/your-store' },
      { title: 'Listing Builder', icon: undefined, path: '/listing-builder' },
      { title: 'Keyword List', icon: undefined, path: '/keyword-list' },
    ],
  },
  {
    title: 'A+ Content',
    icon: aplusContent,
    iconChecked: aplusContentChecked,
    path: '/aplus-content',
    children: [
      {
        title: 'A+ Management',
        icon: undefined,
        path: '/aplus-content/management',
      },
      {
        title: 'A+ Builder',
        icon: undefined,
        path: '/aplus-content/builder',
        children: [
          {
            title: 'Drafts',
            icon: undefined,
            path: '/aplus-content/builder/drafts',
          },
          {
            title: 'Version History',
            icon: undefined,
            path: '/aplus-content/builder/version-history',
          },
        ],
      },
      {
        title: 'Content Experiment',
        icon: undefined,
        path: '/aplus-content/experiment',
      },
    ],
  },
  {
    title: 'Analytics',
    icon: analytics,
    iconChecked: analyticsChecked,
    path: '/analytics',
    children: [
      { title: 'Chidren 0', icon: undefined, path: '/report' },
      {
        title: 'Chidren 1',
        icon: undefined,
        path: '/report',
      },
      {
        title: 'Chidren 2',
        icon: undefined,
        path: '/report',
      },
    ],
  },
];
export default function Sidebar() {
  const router = useRouter();
  const [selectedItem, setSelectedItem] = useState(navItems[0]);
  const showSettingHandler = () => {
    router.push('/home/<USER>/');
  };

  const showHomeSidebar = useAppSelector((state) => state.ui.showHomeSidebar);

  return (
    <>
      <aside className=" bg-white text-black h-screen flex flex-col justify-between border-r border-[#E2E8F0]">
        <nav className="mt-20">
          <ul className="flex flex-col gap-8 p-4">
            {navItems.map((item: NavItem) => (
              <li
                className="cursor-pointer hover:bg-[#F3F4F6] rounded-lg relative group inline-block"
                key={item.title}
                onClick={() => setSelectedItem(item)}
              >
                <span
                  data-tooltip-target={`tooltip-right ${item.title}`}
                  data-tooltip-placement="right"
                  className="text-lg w-[24px] h-[24px] flex justify-center items-center"
                >
                  {item.title === selectedItem.title
                    ? item.iconChecked
                    : item.icon}
                </span>
                <Tooltip className="left-full top-1/2">{item.title}</Tooltip>
              </li>
            ))}
          </ul>
        </nav>
        <nav className="cursor-pointer" onClick={showSettingHandler}>
          <ul className="flex flex-col gap-4 p-4">
            <li>
              <div className="hover:bg-[#F3F4F6] rounded-lg">
                <span className="text-lg w-[24px] h-[24px] flex justify-center items-center">
                  {setting}
                </span>
              </div>
            </li>
          </ul>
        </nav>
      </aside>
      {showHomeSidebar && (
        <div className="min-w-[15%] bg-white cursor-pointer border-r border-[#E2E8F0] px-2">
          <div className="mt-20">
            <p className="text-[#64748B] text-xs font-bold uppercase py-4">
              {selectedItem.title}
            </p>
            <ul className="flex flex-col gap-4 mt-4">
              {selectedItem.children?.map((item) => (
                <li
                  className="px-6 py-2 rounded-lg cursor-pointer hover:bg-[#F3F4F6]"
                  key={item.title}
                  onClick={() => router.push('/home' + item.path)}
                >
                  <span className="text-sm font-sans font-medium text-[##0F172A]">
                    {item.title}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </>
  );
}
