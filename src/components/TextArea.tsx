import React from 'react';

interface TextAreaProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  maxLength?: number;
  placeholder?: string;
  className?: string;
  onFocus?: React.FocusEventHandler<HTMLTextAreaElement>;
  onBlur?: React.FocusEventHandler<HTMLTextAreaElement>;
}

const TextArea: React.FC<TextAreaProps> = ({
  value,
  onChange,
  maxLength = 200,
  placeholder = '',
  className = '',
  onFocus,
  onBlur,
}) => {
  return (
    <div className={`relative w-full`}>
      {' '}
      {/* Container for positioning */}
      <textarea
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        maxLength={maxLength}
        placeholder={placeholder}
        className={`w-full min-h-[100px] p-3 pr-16 rounded-lg border-2 focus:border-yellow-500 border-[#CBD5E1] outline-none text-base transition-colors resize-none ${className}`}
        onFocus={onFocus}
      />
      <span className="absolute bottom-2 right-1 text-gray-500 text-sm pointer-events-none select-none px-1">
        {value.length}/{maxLength}
      </span>
    </div>
  );
};

export default TextArea;
