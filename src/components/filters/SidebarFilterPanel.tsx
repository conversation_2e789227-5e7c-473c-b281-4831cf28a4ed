import { calendar, close, next, previous, search } from '@/utils/icon';
import { useEffect, useRef, useState } from 'react';

interface FilterOption {
  id: string;
  label: string;
  color: string;
  bgColor: string;
}

interface FilterPanelProps {
  onClose: () => void;
  onApply: (filters: any) => void;
  initialFilters?: any;
}

const SidebarFilterPanel = ({
  onClose,
  onApply,
  initialFilters = {},
}: FilterPanelProps) => {
  // State for filter values
  const [statusSearch, setStatusSearch] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(
    initialFilters.statuses || []
  );
  const [selectedPerformance, setSelectedPerformance] = useState<string[]>(
    initialFilters.performance || []
  );
  const [dateRange, setDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>(initialFilters.dateRange || { startDate: '', endDate: '' });
  const [updatedDateRange, setUpdatedDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>(initialFilters.updatedDateRange || { startDate: '', endDate: '' });

  // State for UI controls
  const [statusExpanded, setStatusExpanded] = useState(true);
  const [performanceExpanded, setPerformanceExpanded] = useState(true);
  const [createdDateExpanded, setCreatedDateExpanded] = useState(true);
  const [updatedDateExpanded, setUpdatedDateExpanded] = useState(true);

  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showPerformanceDropdown, setShowPerformanceDropdown] = useState(false);
  const [showCreatedDatePicker, setShowCreatedDatePicker] = useState(false);
  const [showUpdatedDatePicker, setShowUpdatedDatePicker] = useState(false);
  const [performanceSearch, setPerformanceSearch] = useState('');

  // Current month and year for calendar
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  // Refs for handling outside clicks
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const performanceDropdownRef = useRef<HTMLDivElement>(null);
  const createdDatePickerRef = useRef<HTMLDivElement>(null);
  const updatedDatePickerRef = useRef<HTMLDivElement>(null);

  // Status options with their respective colors
  const statusOptions: FilterOption[] = [
    {
      id: 'excellent',
      label: 'Excellent listing',
      color: 'text-green-800',
      bgColor: 'bg-green-100',
    },
    {
      id: 'good',
      label: 'Good listing',
      color: 'text-blue-800',
      bgColor: 'bg-blue-100',
    },
    {
      id: 'need-improvement',
      label: 'Need improvement',
      color: 'text-yellow-800',
      bgColor: 'bg-yellow-100',
    },
    {
      id: 'poor',
      label: 'Poor listing',
      color: 'text-orange-800',
      bgColor: 'bg-orange-100',
    },
    {
      id: 'critical',
      label: 'Critical issues',
      color: 'text-red-800',
      bgColor: 'bg-red-100',
    },
  ];

  // Performance options
  const performanceOptions = [
    { id: 'complete', label: 'Complete' },
    { id: 'pending', label: 'Pending' },
    { id: 'draft', label: 'Draft' },
  ];

  // Filter options based on search
  const filteredStatusOptions = statusOptions.filter((option) =>
    option.label.toLowerCase().includes(statusSearch.toLowerCase())
  );

  const filteredPerformanceOptions = performanceOptions.filter((option) =>
    option.label.toLowerCase().includes(performanceSearch.toLowerCase())
  );

  // Handle outside clicks for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        statusDropdownRef.current &&
        !statusDropdownRef.current.contains(event.target as Node)
      ) {
        setShowStatusDropdown(false);
      }
      if (
        performanceDropdownRef.current &&
        !performanceDropdownRef.current.contains(event.target as Node)
      ) {
        setShowPerformanceDropdown(false);
      }
      if (
        createdDatePickerRef.current &&
        !createdDatePickerRef.current.contains(event.target as Node)
      ) {
        setShowCreatedDatePicker(false);
      }
      if (
        updatedDatePickerRef.current &&
        !updatedDatePickerRef.current.contains(event.target as Node)
      ) {
        setShowUpdatedDatePicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle status selection
  const toggleStatus = (id: string) => {
    if (selectedStatuses.includes(id)) {
      setSelectedStatuses(selectedStatuses.filter((s) => s !== id));
    } else {
      setSelectedStatuses([...selectedStatuses, id]);
    }
  };

  // Toggle performance selection
  const togglePerformance = (id: string) => {
    if (selectedPerformance.includes(id)) {
      setSelectedPerformance(selectedPerformance.filter((p) => p !== id));
    } else {
      setSelectedPerformance([...selectedPerformance, id]);
    }
  };

  // Clear filters
  const clearStatusFilter = () => {
    setSelectedStatuses([]);
  };

  const clearPerformanceFilter = () => {
    setSelectedPerformance([]);
  };

  const clearCreatedDateFilter = () => {
    setDateRange({ startDate: '', endDate: '' });
  };

  const clearUpdatedDateFilter = () => {
    setUpdatedDateRange({ startDate: '', endDate: '' });
  };

  // Apply filters
  const handleApply = () => {
    onApply({
      statuses: selectedStatuses,
      performance: selectedPerformance,
      dateRange,
      updatedDateRange,
    });
    onClose();
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedStatuses([]);
    setSelectedPerformance([]);
    setDateRange({ startDate: '', endDate: '' });
    setUpdatedDateRange({ startDate: '', endDate: '' });
  };

  // Format date for display
  const formatDate = (date: string) => {
    return date || 'Choose date';
  };

  // Calendar navigation
  const goToPreviousMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  const goToNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const days = [];
    const date = new Date(currentYear, currentMonth, 1);
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const firstDayOfMonth = date.getDay();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const dayStr = `${i < 10 ? '0' : ''}${i}`;
      const monthStr = `${currentMonth + 1 < 10 ? '0' : ''}${currentMonth + 1}`;
      const dateStr = `${dayStr}/${monthStr}/${currentYear}`;

      days.push(
        <div
          key={i}
          className={`h-8 w-8 flex items-center justify-center rounded-full cursor-pointer
            ${dateRange.startDate === dateStr || dateRange.endDate === dateStr ? 'bg-yellow-400 text-black' : 'hover:bg-gray-100'}`}
          onClick={() => handleDateSelection(dateStr)}
        >
          {i}
        </div>
      );
    }

    return days;
  };

  // Handle date selection
  const handleDateSelection = (dateStr: string) => {
    if (!dateRange.startDate || (dateRange.startDate && dateRange.endDate)) {
      // Start a new range
      setDateRange({ startDate: dateStr, endDate: '' });
    } else {
      // Complete the range
      const startDate = new Date(
        dateRange.startDate.split('/').reverse().join('-')
      );
      const endDate = new Date(dateStr.split('/').reverse().join('-'));

      if (endDate < startDate) {
        setDateRange({ startDate: dateStr, endDate: dateRange.startDate });
      } else {
        setDateRange({ ...dateRange, endDate: dateStr });
      }
    }
  };

  // Handle updated date selection
  const handleUpdatedDateSelection = (dateStr: string) => {
    if (
      !updatedDateRange.startDate ||
      (updatedDateRange.startDate && updatedDateRange.endDate)
    ) {
      // Start a new range
      setUpdatedDateRange({ startDate: dateStr, endDate: '' });
    } else {
      // Complete the range
      const startDate = new Date(
        updatedDateRange.startDate.split('/').reverse().join('-')
      );
      const endDate = new Date(dateStr.split('/').reverse().join('-'));

      if (endDate < startDate) {
        setUpdatedDateRange({
          startDate: dateStr,
          endDate: updatedDateRange.startDate,
        });
      } else {
        setUpdatedDateRange({ ...updatedDateRange, endDate: dateStr });
      }
    }
  };

  // Generate calendar days for updated date picker
  const generateUpdatedCalendarDays = () => {
    const days = [];
    const date = new Date(currentYear, currentMonth, 1);
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const firstDayOfMonth = date.getDay();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const dayStr = `${i < 10 ? '0' : ''}${i}`;
      const monthStr = `${currentMonth + 1 < 10 ? '0' : ''}${currentMonth + 1}`;
      const dateStr = `${dayStr}/${monthStr}/${currentYear}`;

      days.push(
        <div
          key={i}
          className={`h-8 w-8 flex items-center justify-center rounded-full cursor-pointer
            ${updatedDateRange.startDate === dateStr || updatedDateRange.endDate === dateStr ? 'bg-yellow-400 text-black' : 'hover:bg-gray-100'}`}
          onClick={() => handleUpdatedDateSelection(dateStr)}
        >
          {i}
        </div>
      );
    }

    return days;
  };

  // Get month name
  const getMonthName = (month: number) => {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month];
  };

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-xl z-50 overflow-y-auto">
      <div className="p-6 space-y-6 pb-24">
        {/* Header with close button */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-medium">All filter</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            {close}
          </button>
        </div>

        {/* Status Filter */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setStatusExpanded(!statusExpanded)}
          >
            <h3 className="text-lg font-medium">Status</h3>
            <button className="text-gray-500">
              <svg
                className={`w-5 h-5 transition-transform ${statusExpanded ? 'rotate-180' : ''}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {statusExpanded && (
            <>
              {/* Status dropdown */}
              <div className="relative mt-3" ref={statusDropdownRef}>
                <div
                  className={`border ${selectedStatuses.length > 0 ? 'border-yellow-300' : 'border-gray-300'} rounded-lg p-3 flex items-center justify-between cursor-pointer`}
                  onClick={() => setShowStatusDropdown(!showStatusDropdown)}
                >
                  <div className="flex flex-wrap gap-2">
                    {selectedStatuses.length > 0 ? (
                      statusOptions
                        .filter((option) =>
                          selectedStatuses.includes(option.id)
                        )
                        .map((option) => (
                          <span
                            key={option.id}
                            className={`px-3 py-1 rounded-full ${option.bgColor} ${option.color}`}
                          >
                            {option.label}
                          </span>
                        ))
                    ) : (
                      <span className="text-gray-500">Choose status</span>
                    )}
                  </div>
                  <svg
                    className={`w-5 h-5 transition-transform ${showStatusDropdown ? 'rotate-180' : ''}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>

                {showStatusDropdown && (
                  <div className="absolute left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                    {/* Search input */}
                    <div className="relative p-2 border-b border-gray-200">
                      <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        {search}
                      </div>
                      <input
                        type="text"
                        className="block w-full pl-10 pr-3 py-2 rounded-lg text-gray-700 focus:outline-none"
                        placeholder="Search"
                        value={statusSearch}
                        onChange={(e) => setStatusSearch(e.target.value)}
                      />
                    </div>

                    {/* Selected count */}
                    {selectedStatuses.length > 0 && (
                      <div className="flex items-center p-2 border-b border-gray-200">
                        <div className="w-5 h-5 bg-black flex items-center justify-center rounded-sm mr-2">
                          <span className="text-white text-xs">−</span>
                        </div>
                        <span className="text-sm">
                          {selectedStatuses.length} items selected
                        </span>
                      </div>
                    )}

                    {/* Status options */}
                    <div className="max-h-60 overflow-y-auto">
                      {filteredStatusOptions.map((option) => (
                        <div
                          key={option.id}
                          className={`p-3 flex items-center hover:bg-gray-50 ${selectedStatuses.includes(option.id) ? 'bg-gray-100' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleStatus(option.id);
                          }}
                        >
                          <input
                            type="checkbox"
                            id={`status-${option.id}`}
                            checked={selectedStatuses.includes(option.id)}
                            onChange={() => {}}
                            className="w-5 h-5 rounded-sm"
                          />
                          <span
                            className={`ml-3 px-3 py-1 rounded-full ${option.bgColor} ${option.color}`}
                          >
                            {option.label}
                          </span>
                        </div>
                      ))}

                      {filteredStatusOptions.length === 0 && (
                        <div className="p-3 text-center text-gray-500">
                          No valid searching result
                        </div>
                      )}
                    </div>

                    {/* Clear button */}
                    <div className="p-2 text-right border-t border-gray-200">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          clearStatusFilter();
                        }}
                        className="text-blue-600 text-sm font-medium"
                      >
                        Clear all
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Clear button */}
              {selectedStatuses.length > 0 && (
                <div className="text-right mt-2">
                  <button
                    onClick={clearStatusFilter}
                    className="text-blue-600 text-sm font-medium"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Performance Filter */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setPerformanceExpanded(!performanceExpanded)}
          >
            <h3 className="text-lg font-medium">Performance</h3>
            <button className="text-gray-500">
              <svg
                className={`w-5 h-5 transition-transform ${performanceExpanded ? 'rotate-180' : ''}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {performanceExpanded && (
            <>
              <div className="relative mt-3" ref={performanceDropdownRef}>
                <div
                  className="border border-gray-300 rounded-lg p-3 flex items-center justify-between cursor-pointer"
                  onClick={() =>
                    setShowPerformanceDropdown(!showPerformanceDropdown)
                  }
                >
                  <div className="flex items-center gap-3">
                    {selectedPerformance.includes('complete') && (
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-gray-800 rounded-full mr-1"></div>
                        <span>Complete</span>
                      </div>
                    )}
                    {selectedPerformance.includes('pending') && (
                      <div className="flex items-center ml-3">
                        <div className="w-3 h-3 border border-gray-800 rounded-full mr-1"></div>
                        <span>Pending</span>
                      </div>
                    )}
                    {selectedPerformance.includes('draft') && (
                      <div className="flex items-center ml-3">
                        <div className="w-3 h-3 border border-gray-300 rounded-full mr-2"></div>
                        <span>Draft</span>
                      </div>
                    )}
                    {selectedPerformance.length === 0 && (
                      <span className="text-gray-500">Choose performance</span>
                    )}
                  </div>
                  <svg
                    className={`w-5 h-5 transition-transform ${showPerformanceDropdown ? 'rotate-180' : ''}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>

                {showPerformanceDropdown && (
                  <div className="absolute left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                    {/* Search input */}
                    <div className="relative p-2 border-b border-gray-200">
                      <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        {search}
                      </div>
                      <input
                        type="text"
                        className="block w-full pl-10 pr-3 py-2 rounded-lg text-gray-700 focus:outline-none"
                        placeholder="Search"
                        value={performanceSearch}
                        onChange={(e) => setPerformanceSearch(e.target.value)}
                      />
                    </div>

                    {/* Selected count */}
                    {selectedPerformance.length > 0 && (
                      <div className="flex items-center p-2 border-b border-gray-200">
                        <div className="w-5 h-5 bg-black flex items-center justify-center rounded-sm mr-2">
                          <span className="text-white text-xs">−</span>
                        </div>
                        <span className="text-sm">
                          {selectedPerformance.length} items selected
                        </span>
                      </div>
                    )}

                    {/* Performance options */}
                    <div className="max-h-60 overflow-y-auto">
                      {filteredPerformanceOptions.map((option) => (
                        <div
                          key={option.id}
                          className={`p-3 flex items-center hover:bg-gray-50 ${selectedPerformance.includes(option.id) ? 'bg-gray-100' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            togglePerformance(option.id);
                          }}
                        >
                          <input
                            type="checkbox"
                            id={`performance-${option.id}`}
                            checked={selectedPerformance.includes(option.id)}
                            onChange={() => {}}
                            className="w-5 h-5 rounded-sm"
                          />
                          <div className="ml-3 flex items-center">
                            {option.id === 'complete' && (
                              <div className="w-3 h-3 bg-gray-800 rounded-full mr-2"></div>
                            )}
                            {option.id === 'pending' && (
                              <div className="w-3 h-3 border border-gray-800 rounded-full mr-2"></div>
                            )}
                            {option.id === 'draft' && (
                              <div className="w-3 h-3 border border-gray-300 rounded-full mr-2"></div>
                            )}
                            <span>{option.label}</span>
                          </div>
                        </div>
                      ))}

                      {filteredPerformanceOptions.length === 0 && (
                        <div className="p-3 text-center text-gray-500">
                          No valid searching result
                        </div>
                      )}
                    </div>

                    {/* Clear button */}

                    <div className="p-2 text-right border-t border-gray-200">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          clearPerformanceFilter();
                        }}
                        className="text-blue-600 text-sm font-medium"
                      >
                        Clear all
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Clear button */}
              {selectedPerformance.length > 0 && (
                <div className="text-right mt-2">
                  <button
                    onClick={clearPerformanceFilter}
                    className="text-blue-600 text-sm font-medium"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Created date Filter */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setCreatedDateExpanded(!createdDateExpanded)}
          >
            <h3 className="text-lg font-medium">Created date</h3>
            <button className="text-gray-500">
              <svg
                className={`w-5 h-5 transition-transform ${createdDateExpanded ? 'rotate-180' : ''}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {createdDateExpanded && (
            <>
              <div className="relative mt-3" ref={createdDatePickerRef}>
                <div
                  className={`border ${dateRange.startDate ? 'border-yellow-30' : 'border-gray-300'} rounded-lg p-3 flex items-center justify-between cursor-pointer`}
                  onClick={() =>
                    setShowCreatedDatePicker(!showCreatedDatePicker)
                  }
                >
                  <span
                    className={
                      dateRange.startDate ? 'text-gray-900' : 'text-gray-500'
                    }
                  >
                    {dateRange.startDate
                      ? `${dateRange.startDate} - ${dateRange.endDate || dateRange.startDate}`
                      : 'Choose date'}
                  </span>
                  {calendar}
                </div>

                {showCreatedDatePicker && (
                  <div className="absolute left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                    <div className="p-4">
                      {/* Calendar header */}
                      <div className="flex items-center justify-between mb-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            goToPreviousMonth();
                          }}
                          className="p-1 hover:bg-gray-100 rounded-full"
                        >
                          {previous}
                        </button>
                        <span className="font-medium">
                          {getMonthName(currentMonth)} {currentYear}
                        </span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            goToNextMonth();
                          }}
                          className="p-1 hover:bg-gray-100 rounded-full"
                        >
                          {next}
                        </button>
                      </div>

                      {/* Days of week */}
                      <div className="grid grid-cols-7 gap-1 mb-2">
                        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(
                          (day) => (
                            <div
                              key={day}
                              className="h-8 w-8 flex items-center justify-center text-sm font-medium text-gray-500"
                            >
                              {day}
                            </div>
                          )
                        )}
                      </div>

                      {/* Calendar days */}
                      <div className="grid grid-cols-7 gap-1">
                        {generateCalendarDays()}
                      </div>

                      {/* Selected date range */}
                      {(dateRange.startDate || dateRange.endDate) && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-xs text-gray-500">
                                Start date
                              </div>
                              <div className="font-medium">
                                {dateRange.startDate || 'Not selected'}
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-500">
                                End date
                              </div>
                              <div className="font-medium">
                                {dateRange.endDate || 'Not selected'}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Clear button */}
                    <div className="p-2 text-right border-t border-gray-200">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          clearCreatedDateFilter();
                        }}
                        className="text-blue-600 text-sm font-medium"
                      >
                        Clear all
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Clear button */}
              {dateRange.startDate && dateRange.endDate && (
                <div className="text-right mt-2">
                  <button
                    onClick={clearCreatedDateFilter}
                    className="text-blue-600 text-sm font-medium"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </>
          )}
        </div>

        {/* Latest updated Filter */}
        <div>
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setUpdatedDateExpanded(!updatedDateExpanded)}
          >
            <h3 className="text-lg font-medium">Latest updated</h3>
            <button className="text-gray-500">
              <svg
                className={`w-5 h-5 transition-transform ${updatedDateExpanded ? 'rotate-180' : ''}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          {updatedDateExpanded && (
            <>
              <div className="relative mt-3" ref={updatedDatePickerRef}>
                <div
                  className={`border border-gray-300 rounded-lg p-3 flex items-center justify-between cursor-pointer`}
                  onClick={() =>
                    setShowUpdatedDatePicker(!showUpdatedDatePicker)
                  }
                >
                  <span
                    className={
                      updatedDateRange.startDate
                        ? 'text-gray-900'
                        : 'text-gray-500'
                    }
                  >
                    {updatedDateRange.startDate
                      ? `${updatedDateRange.startDate} - ${updatedDateRange.endDate || updatedDateRange.startDate}`
                      : 'Choose date'}
                  </span>
                  {calendar}
                </div>

                {showUpdatedDatePicker && (
                  <div className="absolute left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                    <div className="p-4">
                      {/* Calendar header */}
                      <div className="flex items-center justify-between mb-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            goToPreviousMonth();
                          }}
                          className="p-1 hover:bg-gray-100 rounded-full"
                        >
                          {previous}
                        </button>
                        <span className="font-medium">
                          {getMonthName(currentMonth)} {currentYear}
                        </span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            goToNextMonth();
                          }}
                          className="p-1 hover:bg-gray-100 rounded-full"
                        >
                          {next}
                        </button>
                      </div>

                      {/* Days of week */}
                      <div className="grid grid-cols-7 gap-1 mb-2">
                        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(
                          (day) => (
                            <div
                              key={day}
                              className="h-8 w-8 flex items-center justify-center text-sm font-medium text-gray-500"
                            >
                              {day}
                            </div>
                          )
                        )}
                      </div>

                      {/* Calendar days */}
                      <div className="grid grid-cols-7 gap-1">
                        {generateUpdatedCalendarDays()}
                      </div>

                      {/* Selected date range */}
                      {(updatedDateRange.startDate ||
                        updatedDateRange.endDate) && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-xs text-gray-500">
                                Start date
                              </div>
                              <div className="font-medium">
                                {updatedDateRange.startDate || 'Not selected'}
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-500">
                                End date
                              </div>
                              <div className="font-medium">
                                {updatedDateRange.endDate || 'Not selected'}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Clear button */}
                    <div className="p-2 text-right border-t border-gray-200">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          clearUpdatedDateFilter();
                        }}
                        className="text-blue-600 text-sm font-medium"
                      >
                        Clear all
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Clear button */}
              {updatedDateRange.startDate && updatedDateRange.endDate && (
                <div className="text-right mt-2">
                  <button
                    onClick={clearUpdatedDateFilter}
                    className="text-blue-600 text-sm font-medium"
                  >
                    Clear all
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Footer with action buttons */}
      <div className="fixed bottom-0 left-auto right-0 w-96 p-4 bg-white border-t border-gray-200 flex gap-3">
        <button
          onClick={clearAllFilters}
          className="flex-1 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium"
        >
          Clear
        </button>
        <button
          onClick={handleApply}
          className="flex-1 py-3 bg-yellow-400 rounded-lg text-gray-900 font-medium"
        >
          Apply
        </button>
      </div>
    </div>
  );
};

export default SidebarFilterPanel;
