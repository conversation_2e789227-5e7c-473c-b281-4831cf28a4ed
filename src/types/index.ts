import { JSX } from 'react';

export interface NavItem {
  path: string;
  title: string;
  icon?: JSX.Element;
  iconChecked?: JSX.Element;
  active?: boolean;
  subItems?: NavItem[];
  children?: NavItem[];
}

export interface Field {
  value: string;
  errorMsg: string;
}

export interface Billing {
  id: number;
  date: string;
  billCode: string;
  paid: number;
  isChecked: boolean;
}

export interface Plan {
  id: number;
  name: string;
  for: string;
  price: number;
  isSelected: boolean;
  features: string[];
}

export interface EnumOption {
  value: string;
  label: string;
}

export interface User {
  username: string;
  email: string;
  display_name: string | null;
  phone_number: string | null;
  address: string | null;
  two_fa_enabled_date: string | null;
  last_password_changed: string | null;
  date_joined: string | null;
}
