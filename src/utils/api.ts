import axios from 'axios';
import Cookies from 'js-cookie';

const api = axios.create({
  baseURL: process.env.BASE_URL || 'http://127.0.0.1:8000',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  // timeout: 10000,
});

// ✅ Add request interceptor (Optional: Add auth headers if needed)
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 Request: ${config.method?.toUpperCase()} ${config.url}`);
    const token = Cookies.get('session_token'); // or from cookies
    if (token) {
      config.headers['X-Session-Token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// ✅ Add response interceptor (Handles token refresh & errors)
api.interceptors.response.use(
  (response) => {
    console.log(`🚀 Response: ${response}`);
    return response;
  },
  async (error) => {
    // const originalRequest = error.config;

    // // 🔄 Automatically refresh token if 401 response (Optional)
    // if (error.response?.status === 401 && !originalRequest._retry) {
    //   originalRequest._retry = true;
    //   try {
    //     await api.post("/auth/refresh/"); // Request token refresh
    //     return api(originalRequest); // Retry original request
    //   } catch (refreshError) {
    //     console.log("Token refresh failed, logging out...: "  + refreshError);
    //     // window.location.href = "/auth/login";
    //   }
    // }

    console.log('❌ API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const checkEmailExist = async (email: string) => {
  const response = await api.get(
    '/api/v1/auth/is_email_available?email=' + email
  );
  return response.data.is_available;
};

export const signup = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  const response = await api.post('_allauth/app/v1/auth/signup', {
    email,
    password,
  });
  return response;
};

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  const response = await api.post('/api/v1/auth/login', {
    email,
    password,
  });
  return response;
};

export const resetPasswordRequest = async (email: string) => {
  const response = await api.post('/_allauth/app/v1/auth/password/request', {
    email,
  });
  return response;
};

export const resetPassword = async ({
  key,
  password,
}: {
  key: string;
  password: string;
}) => {
  const response = await api.post('/_allauth/app/v1/auth/password/reset', {
    key: key,
    password: password,
  });
  return response;
};

export const signInWithProvider = async (
  provider: string,
  processName: string,
  idToken: string,
  accessToken: string
) => {
  const response = await api.post('/_allauth/app/v1/auth/provider/token', {
    provider: provider,
    process: processName,
    token: {
      client_id: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID,
      id_token: idToken,
      access_token: accessToken,
    },
  });
  return response;
};

export const verifyEmail = async (key: string) => {
  const response = await api.post('/_allauth/app/v1/auth/email/verify', {
    key: key,
  });
  return response;
};

export const updateProfile = async (
  display_name: string,
  phone_number: string,
  address: string
) => {
  const response = await api.post('/api/v1/profile/', {
    display_name,
    phone_number,
    address,
  });
  return response;
};

export const getListingBuilderData = async (keywords: string) => {
  const response = await api.get(
    '/api/v1/product-type/get-pt-form?keywords=' +
      keywords +
      '&marketplaceIds=ATVPDKIKX0DER'
  );
  return response;
};

export const testProManHandler = async (
  first_name: string,
  last_name: string,
  email: string
) => {
  const response = await api.post('/api/v1/landing/signup/', {
    first_name,
    last_name,
    email,
  });
  return response;
};

export const updateEmail = async (email: string) => {
  const response = await api.post('/api/v1/email', { email });
  return response;
};

export const updateRecoveryPhone = async (phone: string) => {
  const response = await api.post('/api/v1/security/add-recovery-phone', {
    phone,
  });
  return response;
};

export const updatePassword = async (
  current_password: string,
  new_password: string
) => {
  const response = await api.post('/api/v1/password-reset', {
    new_password,
    current_password,
  });
  return response;
};

export const fetchUser = async () => {
  const response = await api.get('/api/v1/profile');
  return response;
};

export const get2FAQRCode = async () => {
  const response = await api.get(
    '/_allauth/app/v1/account/authenticators/totp'
  );
  return response;
};

export const activate2FA = async (code: string) => {
  const response = await api.post(
    '/_allauth/app/v1/account/authenticators/totp',
    {
      code: code,
    }
  );
  return response;
};

export const verify2FA = async (code: string, sessionToken: string) => {
  const response = await api.post(
    '/_allauth/app/v1/auth/2fa/authenticate',
    {
      code: code,
    },
    {
      headers: {
        'X-Session-Token': sessionToken,
        'Content-Type': 'application/json',
      },
    }
  );
  return response;
};

export const getProductTypesByKeywords = async (keywords: string) => {
  const response = await api.get(
    `/api/v1/product-type/get-product-type-by-keywords?marketplaceIds=ATVPDKIKX0DER&keywords=${encodeURIComponent(keywords)}`
  );
  return response;
};

export const getProductTypesByNodeId = async (nodeId?: string) => {
  let endpoint = '/api/v1/product-type/get-product-type-by-node-id';
  if (nodeId) {
    endpoint += `?nodeId=${nodeId}`;
  }
  const response = await api.get(endpoint);
  return response.data;
};

export const postListing = async (data: any) => {
  const response = await api.post(
    '/api/v1/listing?marketplaceIds=ATVPDKIKX0DER',
    data
  );
  return response;
};
export default api;
