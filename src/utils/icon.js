export const amazon = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.4305 17.6022C9.9236 21.6508 4.64413 18.2634 1.26457 16.2061C1.05544 16.0764 0.700003 16.2364 1.0084 16.5906C2.1343 17.9558 5.82411 21.2463 10.6404 21.2463C15.46 21.2463 18.3272 18.6165 18.6859 18.1577C19.0421 17.7028 18.7905 17.4519 18.4304 17.6022H18.4305ZM20.8196 16.2828C20.5912 15.9853 19.4305 15.9298 18.7001 16.0196C17.9685 16.1067 16.8704 16.5538 16.9659 16.8223C17.0149 16.9228 17.1149 16.8777 17.6174 16.8325C18.1214 16.7823 19.5331 16.6041 19.8273 16.9886C20.1228 17.3759 19.377 19.2204 19.2408 19.5179C19.1092 19.8154 19.2911 19.8921 19.5383 19.6939C19.7821 19.4959 20.2235 18.983 20.5197 18.2571C20.8138 17.5273 20.9933 16.5093 20.8196 16.2828Z"
      fill="#FF9900"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.7106 9.34028C12.7106 10.4026 12.7374 11.2885 12.2005 12.2318C11.7671 12.9989 11.0806 13.4706 10.3136 13.4706C9.26665 13.4706 8.65689 12.6729 8.65689 11.4956C8.65689 9.17149 10.7393 8.74968 12.7106 8.74968V9.34028ZM15.4602 15.9864C15.28 16.1474 15.0192 16.159 14.816 16.0515C13.9109 15.2999 13.7498 14.9509 13.2513 14.2338C11.7556 15.7601 10.6971 16.2164 8.75665 16.2164C6.4633 16.2164 4.67615 14.8013 4.67615 11.9672C4.67615 9.75449 5.87658 8.24728 7.58305 7.51102C9.06342 6.85898 11.1305 6.74395 12.7106 6.56377V6.21091C12.7106 5.56276 12.7603 4.79578 12.3807 4.2359C12.047 3.7335 11.4104 3.52639 10.8505 3.52639C9.81125 3.52639 8.88316 4.05943 8.65689 5.16391C8.61082 5.40941 8.43063 5.65103 8.18521 5.66251L5.539 5.37877C5.31662 5.32882 5.0712 5.14863 5.13255 4.80726C5.74231 1.60125 8.63765 0.634766 11.2301 0.634766C12.557 0.634766 14.2904 0.98762 15.3374 1.99243C16.6644 3.23109 16.5378 4.88397 16.5378 6.6826V10.9318C16.5378 12.2089 17.067 12.7687 17.5655 13.4591C17.7419 13.7045 17.7803 13.9999 17.5579 14.1839C17.0018 14.6479 16.0123 15.5108 15.4678 15.9941L15.4601 15.9864"
      fill="black"
    />
    <path
      d="M18.4305 17.6022C9.9236 21.6508 4.64413 18.2634 1.26457 16.2061C1.05544 16.0764 0.700003 16.2364 1.0084 16.5906C2.1343 17.9558 5.82411 21.2463 10.6404 21.2463C15.46 21.2463 18.3272 18.6165 18.6859 18.1577C19.0421 17.7028 18.7905 17.4519 18.4304 17.6022H18.4305ZM20.8196 16.2828C20.5912 15.9853 19.4305 15.9298 18.7001 16.0196C17.9685 16.1067 16.8704 16.5538 16.9659 16.8223C17.0149 16.9228 17.1149 16.8777 17.6174 16.8325C18.1214 16.7823 19.5331 16.6041 19.8273 16.9886C20.1228 17.3759 19.377 19.2204 19.2408 19.5179C19.1092 19.8154 19.2911 19.8921 19.5383 19.6939C19.7821 19.4959 20.2235 18.983 20.5197 18.2571C20.8138 17.5273 20.9933 16.5093 20.8196 16.2828Z"
      fill="#FF9900"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.7106 9.34028C12.7106 10.4026 12.7374 11.2885 12.2005 12.2318C11.7671 12.9989 11.0806 13.4706 10.3136 13.4706C9.26665 13.4706 8.65689 12.6729 8.65689 11.4956C8.65689 9.17149 10.7393 8.74968 12.7106 8.74968V9.34028ZM15.4602 15.9864C15.28 16.1474 15.0192 16.159 14.816 16.0515C13.9109 15.2999 13.7498 14.9509 13.2513 14.2338C11.7556 15.7601 10.6971 16.2164 8.75665 16.2164C6.4633 16.2164 4.67615 14.8013 4.67615 11.9672C4.67615 9.75449 5.87658 8.24728 7.58305 7.51102C9.06342 6.85898 11.1305 6.74395 12.7106 6.56377V6.21091C12.7106 5.56276 12.7603 4.79578 12.3807 4.2359C12.047 3.7335 11.4104 3.52639 10.8505 3.52639C9.81125 3.52639 8.88316 4.05943 8.65689 5.16391C8.61082 5.40941 8.43063 5.65103 8.18521 5.66251L5.539 5.37877C5.31662 5.32882 5.0712 5.14863 5.13255 4.80726C5.74231 1.60125 8.63765 0.634766 11.2301 0.634766C12.557 0.634766 14.2904 0.98762 15.3374 1.99243C16.6644 3.23109 16.5378 4.88397 16.5378 6.6826V10.9318C16.5378 12.2089 17.067 12.7687 17.5655 13.4591C17.7419 13.7045 17.7803 13.9999 17.5579 14.1839C17.0018 14.6479 16.0123 15.5108 15.4678 15.9941L15.4601 15.9864"
      fill="black"
    />
  </svg>
);

export const google = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.208 12.1942C20.208 11.5879 20.1536 11.0049 20.0525 10.4453H12V13.7526H16.6015C16.4032 14.8214 15.8009 15.7269 14.8953 16.3332V18.4784H17.6585C19.2753 16.9899 20.208 14.798 20.208 12.1942Z"
      fill="#4285F4"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 20.5505C14.3085 20.5505 16.2439 19.7849 17.6585 18.4791L14.8953 16.3338C14.1297 16.8468 13.1503 17.1499 12 17.1499C9.77308 17.1499 7.88819 15.6459 7.21585 13.625H4.35938V15.8402C5.76624 18.6345 8.65769 20.5505 12 20.5505Z"
      fill="#34A853"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.21584 13.6244C7.04484 13.1114 6.94768 12.5634 6.94768 11.9999C6.94768 11.4364 7.04484 10.8884 7.21584 10.3754V8.16016H4.35936C3.78029 9.31441 3.44995 10.6202 3.44995 11.9999C3.44995 13.3795 3.78029 14.6854 4.35936 15.8396L7.21584 13.6244Z"
      fill="#FBBC05"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 6.84979C13.2553 6.84979 14.3823 7.28117 15.2684 8.1284L17.7207 5.67611C16.24 4.29645 14.3046 3.44922 12 3.44922C8.65769 3.44922 5.76624 5.3652 4.35938 8.15949L7.21585 10.3747C7.88819 8.35381 9.77308 6.84979 12 6.84979Z"
      fill="#EA4335"
    />
  </svg>
);

export const earth = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 11H21M1 11C1 16.5228 5.47715 21 11 21M1 11C1 5.47715 5.47715 1 11 1M21 11C21 16.5228 16.5228 21 11 21M21 11C21 5.47715 16.5228 1 11 1M11 1C13.5013 3.73835 14.9228 7.29203 15 11C14.9228 14.708 13.5013 18.2616 11 21M11 1C8.49872 3.73835 7.07725 7.29203 7 11C7.07725 14.708 8.49872 18.2616 11 21"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const forwardArrow = (
  <svg
    width="16"
    height="12"
    viewBox="0 0 16 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.33337 6H14.6667M14.6667 6L9.66671 1M14.6667 6L9.66671 11"
      stroke="#1D4ED8"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const backArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 18L9 12L15 6"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const upArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 5L12 19M12 5L18 11M12 5L6 11"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const downArrow = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 19L12 5M12 19L18 13M12 19L6 13"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const danger = (
  <svg
    className="size-6 text-red-600"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="1.5"
    stroke="currentColor"
    aria-hidden="true"
    data-slot="icon"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
    />
  </svg>
);

export const loading = (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.5266 11.0744C15.2263 9.70283 15.4372 8.13365 15.1245 6.62603C14.8118 5.11842 13.9942 3.76255 12.8068 2.7824C11.6194 1.80225 10.1331 1.25645 8.59357 1.23516C7.05401 1.21386 5.55325 1.71835 4.33916 2.66528C3.12508 3.61221 2.2703 4.94495 1.91604 6.44334C1.56177 7.94173 1.7292 9.51614 2.39067 10.9065C3.05214 12.2969 4.16809 13.42 5.55417 14.0905C6.94025 14.7609 8.51355 14.9385 10.0142 14.5939L10.2905 15.7971C10.2895 15.7973 10.2885 15.7975 10.2875 15.7977C9.96123 15.8725 9.63202 15.9265 9.30182 15.9597C7.83731 16.1073 6.35335 15.8483 5.01664 15.2018C3.37763 14.4091 2.05806 13.081 1.27589 11.4369C0.49372 9.7928 0.295738 7.9311 0.714649 6.15929C1.13356 4.38749 2.1443 2.81157 3.57992 1.69185C5.01555 0.572124 6.79016 -0.0244144 8.61064 0.000765683C10.4311 0.0259458 12.1886 0.671338 13.5927 1.83034C14.9968 2.98934 15.9635 4.59261 16.3333 6.37532C16.6348 7.82924 16.5246 9.33158 16.0248 10.7161C15.9118 11.0292 15.7789 11.3362 15.6263 11.6354L14.5266 11.0744Z"
      fill="#94A3B8"
    />
  </svg>
);

export const logo = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4.68293" fill="#FACC15" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.41492 11.9023C7.41492 14.4887 9.51154 16.5853 12.0978 16.5853C14.6842 16.5853 16.7808 14.4887 16.7808 11.9023H19.9027C19.9027 16.2129 16.4084 19.7072 12.0978 19.7072C7.78733 19.7072 4.29297 16.2129 4.29297 11.9023H7.41492Z"
      fill="#020617"
    />
  </svg>
);

export const ringBell = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.35395 21C10.0591 21.6224 10.9853 22 11.9998 22C13.0142 22 13.9405 21.6224 14.6456 21M17.9998 8C17.9998 6.4087 17.3676 4.88258 16.2424 3.75736C15.1172 2.63214 13.5911 2 11.9998 2C10.4085 2 8.88235 2.63214 7.75713 3.75736C6.63192 4.88258 5.99977 6.4087 5.99977 8C5.99977 11.0902 5.22024 13.206 4.34944 14.6054C3.6149 15.7859 3.24763 16.3761 3.2611 16.5408C3.27601 16.7231 3.31463 16.7926 3.46155 16.9016C3.59423 17 4.19237 17 5.38863 17H18.6109C19.8072 17 20.4053 17 20.538 16.9016C20.6849 16.7926 20.7235 16.7231 20.7384 16.5408C20.7519 16.3761 20.3846 15.7859 19.6501 14.6054C18.7793 13.206 17.9998 11.0902 17.9998 8Z"
      stroke="#475569"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const home = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4774 1.33639C11.8197 1.24371 12.1805 1.24371 12.5228 1.33639C12.9202 1.44398 13.2548 1.70663 13.5218 1.91627C13.5473 1.93628 13.5721 1.95581 13.5964 1.97468L20.3787 7.2498C20.4048 7.27011 20.4307 7.29022 20.4564 7.31016C20.833 7.60244 21.1648 7.85994 21.4121 8.19424C21.6291 8.48762 21.7907 8.81813 21.8891 9.16953C22.0012 9.56995 22.0007 9.98996 22.0002 10.4667C22.0002 10.4992 22.0001 10.532 22.0001 10.5651V17.8386C22.0002 18.3657 22.0002 18.8205 21.9696 19.195C21.9373 19.5904 21.866 19.9836 21.6732 20.362C21.3855 20.9265 20.9266 21.3854 20.3621 21.673C19.9837 21.8658 19.5905 21.9371 19.1952 21.9694C18.8206 22 18.3658 22 17.8387 22H6.16157C5.63443 22 5.17968 22 4.80511 21.9694C4.40977 21.9371 4.01655 21.8658 3.63817 21.673C3.07368 21.3854 2.61474 20.9265 2.32712 20.362C2.13433 19.9836 2.06301 19.5904 2.03071 19.195C2.00011 18.8205 2.00012 18.3657 2.00014 17.8386L2.00014 10.5651C2.00014 10.532 2.0001 10.4992 2.00007 10.4667C1.99955 9.98996 1.9991 9.56995 2.11118 9.16953C2.20954 8.81813 2.37119 8.48762 2.58818 8.19424C2.83544 7.85994 3.16725 7.60243 3.54389 7.31015C3.56958 7.29021 3.59549 7.2701 3.62159 7.2498L10.4039 1.97468C10.4282 1.95581 10.453 1.93628 10.4785 1.91627C10.7455 1.70663 11.0801 1.44398 11.4774 1.33639ZM10.0001 20H14.0001V13.6C14.0001 13.3035 13.9994 13.1412 13.9898 13.0246C13.9895 13.02 13.9891 13.0156 13.9887 13.0115C13.9845 13.0111 13.9801 13.0107 13.9755 13.0103C13.8589 13.0008 13.6967 13 13.4001 13H10.6001C10.3036 13 10.1414 13.0008 10.0248 13.0103C10.0201 13.0107 10.0157 13.0111 10.0116 13.0115C10.0112 13.0156 10.0108 13.02 10.0104 13.0246C10.0009 13.1412 10.0001 13.3035 10.0001 13.6V20ZM16.0001 20L16.0001 13.5681C16.0002 13.3157 16.0002 13.0699 15.9832 12.8618C15.9645 12.6332 15.9204 12.3635 15.7822 12.092C15.5904 11.7157 15.2844 11.4097 14.9081 11.218C14.6367 11.0797 14.367 11.0356 14.1384 11.017C13.9302 11 13.6844 11 13.432 11H10.5683C10.3159 11 10.0701 11 9.86191 11.017C9.63332 11.0356 9.36358 11.0797 9.09216 11.218C8.71583 11.4097 8.40987 11.7157 8.21813 12.092C8.07983 12.3635 8.03576 12.6332 8.01709 12.8618C8.00008 13.0699 8.00011 13.3157 8.00014 13.5681L8.00014 20H6.20014C5.62359 20 5.25131 19.9992 4.96798 19.9761C4.69631 19.9539 4.59559 19.9162 4.54615 19.891C4.35799 19.7951 4.20501 19.6422 4.10913 19.454C4.08394 19.4046 4.04626 19.3038 4.02407 19.0322C4.00092 18.7488 4.00014 18.3766 4.00014 17.8V10.5651C4.00014 9.93409 4.00883 9.80983 4.03715 9.70863C4.06994 9.5915 4.12382 9.48133 4.19615 9.38353C4.25864 9.29904 4.3514 9.2159 4.84947 8.82851L11.6318 3.55338C11.8187 3.40801 11.9177 3.33177 11.9928 3.28156C11.9954 3.27986 11.9978 3.27825 12.0001 3.27672C12.0025 3.27825 12.0049 3.27986 12.0074 3.28156C12.0826 3.33177 12.1816 3.40801 12.3685 3.55338L19.1508 8.82851C19.6489 9.2159 19.7416 9.29904 19.8041 9.38353C19.8765 9.48133 19.9303 9.5915 19.9631 9.70863C19.9915 9.80983 20.0001 9.93409 20.0001 10.5651V17.8C20.0001 18.3766 19.9994 18.7488 19.9762 19.0322C19.954 19.3038 19.9163 19.4046 19.8911 19.454C19.7953 19.6422 19.6423 19.7951 19.4541 19.891C19.4047 19.9162 19.304 19.9539 19.0323 19.9761C18.749 19.9992 18.3767 20 17.8001 20H16.0001Z"
      fill="#030712"
    />
  </svg>
);

export const homeChecked = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4774 1.33639C11.8197 1.24371 12.1805 1.24371 12.5228 1.33639C12.9202 1.44398 13.2548 1.70663 13.5218 1.91627C13.5473 1.93628 13.5721 1.95581 13.5964 1.97468L20.3787 7.2498C20.4048 7.27011 20.4307 7.29022 20.4564 7.31016C20.833 7.60244 21.1648 7.85994 21.4121 8.19424C21.6291 8.48762 21.7907 8.81813 21.8891 9.16953C22.0012 9.56995 22.0007 9.98996 22.0002 10.4667C22.0002 10.4992 22.0001 10.532 22.0001 10.5651V17.8386C22.0002 18.3657 22.0002 18.8205 21.9696 19.195C21.9373 19.5904 21.866 19.9836 21.6732 20.362C21.3855 20.9265 20.9266 21.3854 20.3621 21.673C19.9837 21.8658 19.5905 21.9371 19.1952 21.9694C18.8206 22 18.3658 22 17.8387 22H6.16157C5.63443 22 5.17968 22 4.80511 21.9694C4.40977 21.9371 4.01655 21.8658 3.63817 21.673C3.07368 21.3854 2.61474 20.9265 2.32712 20.362C2.13433 19.9836 2.06301 19.5904 2.03071 19.195C2.00011 18.8205 2.00012 18.3657 2.00014 17.8386L2.00014 10.5651C2.00014 10.532 2.0001 10.4992 2.00007 10.4667C1.99955 9.98996 1.9991 9.56995 2.11118 9.16953C2.20954 8.81813 2.37119 8.48762 2.58818 8.19424C2.83544 7.85994 3.16725 7.60243 3.54389 7.31015C3.56958 7.29021 3.59549 7.2701 3.62159 7.2498L10.4039 1.97468C10.4282 1.95581 10.453 1.93628 10.4785 1.91627C10.7455 1.70663 11.0801 1.44398 11.4774 1.33639ZM10.0001 20H14.0001V13.6C14.0001 13.3035 13.9994 13.1412 13.9898 13.0246C13.9895 13.02 13.9891 13.0156 13.9887 13.0115C13.9845 13.0111 13.9801 13.0107 13.9755 13.0103C13.8589 13.0008 13.6967 13 13.4001 13H10.6001C10.3036 13 10.1414 13.0008 10.0248 13.0103C10.0201 13.0107 10.0157 13.0111 10.0116 13.0115C10.0112 13.0156 10.0108 13.02 10.0104 13.0246C10.0009 13.1412 10.0001 13.3035 10.0001 13.6V20ZM16.0001 20L16.0001 13.5681C16.0002 13.3157 16.0002 13.0699 15.9832 12.8618C15.9645 12.6332 15.9204 12.3635 15.7822 12.092C15.5904 11.7157 15.2844 11.4097 14.9081 11.218C14.6367 11.0797 14.367 11.0356 14.1384 11.017C13.9302 11 13.6844 11 13.432 11H10.5683C10.3159 11 10.0701 11 9.86191 11.017C9.63332 11.0356 9.36358 11.0797 9.09216 11.218C8.71583 11.4097 8.40987 11.7157 8.21813 12.092C8.07983 12.3635 8.03576 12.6332 8.01709 12.8618C8.00008 13.0699 8.00011 13.3157 8.00014 13.5681L8.00014 20H6.20014C5.62359 20 5.25131 19.9992 4.96798 19.9761C4.69631 19.9539 4.59559 19.9162 4.54615 19.891C4.35799 19.7951 4.20501 19.6422 4.10913 19.454C4.08394 19.4046 4.04626 19.3038 4.02407 19.0322C4.00092 18.7488 4.00014 18.3766 4.00014 17.8V10.5651C4.00014 9.93409 4.00883 9.80983 4.03715 9.70863C4.06994 9.5915 4.12382 9.48133 4.19615 9.38353C4.25864 9.29904 4.3514 9.2159 4.84947 8.82851L11.6318 3.55338C11.8187 3.40801 11.9177 3.33177 11.9928 3.28156C11.9954 3.27986 11.9978 3.27825 12.0001 3.27672C12.0025 3.27825 12.0049 3.27986 12.0074 3.28156C12.0826 3.33177 12.1816 3.40801 12.3685 3.55338L19.1508 8.82851C19.6489 9.2159 19.7416 9.29904 19.8041 9.38353C19.8765 9.48133 19.9303 9.5915 19.9631 9.70863C19.9915 9.80983 20.0001 9.93409 20.0001 10.5651V17.8C20.0001 18.3766 19.9994 18.7488 19.9762 19.0322C19.954 19.3038 19.9163 19.4046 19.8911 19.454C19.7953 19.6422 19.6423 19.7951 19.4541 19.891C19.4047 19.9162 19.304 19.9539 19.0323 19.9761C18.749 19.9992 18.3767 20 17.8001 20H16.0001Z"
      fill="#CA8A04"
    />
  </svg>
);

export const productListing = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5 11H12.5M17.5 15H12.5M17.5 7H12.5M9 3L9 21M7.8 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11984 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V7.8C3 6.11984 3 5.27976 3.32698 4.63803C3.6146 4.07354 4.07354 3.6146 4.63803 3.32698C5.27976 3 6.11984 3 7.8 3Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const productListingChecked = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.7587 4.31292e-07H14.2413C15.0463 -1.23241e-05 15.7106 -2.28137e-05 16.2518 0.0441945C16.8139 0.0901197 17.3306 0.188684 17.816 0.435975C18.5686 0.819468 19.1805 1.43139 19.564 2.18404C19.8113 2.66937 19.9099 3.18608 19.9558 3.74817C20 4.28936 20 4.95372 20 5.75868V14.2413C20 15.0463 20 15.7106 19.9558 16.2518C19.9099 16.8139 19.8113 17.3306 19.564 17.816C19.1805 18.5686 18.5686 19.1805 17.816 19.564C17.3306 19.8113 16.8139 19.9099 16.2518 19.9558C15.7106 20 15.0463 20 14.2413 20H5.75868C4.95372 20 4.28936 20 3.74817 19.9558C3.18608 19.9099 2.66937 19.8113 2.18404 19.564C1.43139 19.1805 0.819468 18.5686 0.435975 17.816C0.188684 17.3306 0.0901197 16.8139 0.0441945 16.2518C-2.28137e-05 15.7106 -1.23241e-05 15.0463 4.31292e-07 14.2413V5.7587C-1.23241e-05 4.95373 -2.28137e-05 4.28937 0.0441945 3.74817C0.0901197 3.18608 0.188684 2.66937 0.435975 2.18404C0.819468 1.43139 1.43139 0.819468 2.18404 0.435975C2.66937 0.188684 3.18608 0.0901197 3.74817 0.0441945C4.28937 -2.28137e-05 4.95373 -1.23241e-05 5.7587 4.31292e-07ZM8 18H14.2C15.0566 18 15.6389 17.9992 16.089 17.9624C16.5274 17.9266 16.7516 17.8617 16.908 17.782C17.2843 17.5903 17.5903 17.2843 17.782 16.908C17.8617 16.7516 17.9266 16.5274 17.9624 16.089C17.9992 15.6389 18 15.0566 18 14.2V5.8C18 4.94342 17.9992 4.36113 17.9624 3.91104C17.9266 3.47262 17.8617 3.24842 17.782 3.09202C17.5903 2.7157 17.2843 2.40973 16.908 2.21799C16.7516 2.1383 16.5274 2.07337 16.089 2.03755C15.6389 2.00078 15.0566 2 14.2 2H8L8 18ZM6 2L6 18H5.8C4.94342 18 4.36113 17.9992 3.91104 17.9624C3.47262 17.9266 3.24842 17.8617 3.09202 17.782C2.7157 17.5903 2.40973 17.2843 2.21799 16.908C2.1383 16.7516 2.07337 16.5274 2.03755 16.089C2.00078 15.6389 2 15.0566 2 14.2V5.8C2 4.94342 2.00078 4.36113 2.03755 3.91104C2.07337 3.47262 2.1383 3.24842 2.21799 3.09202C2.40973 2.7157 2.7157 2.40973 3.09202 2.21799C3.24842 2.1383 3.47262 2.07337 3.91104 2.03755C4.36113 2.00078 4.94342 2 5.8 2H6ZM9.5 5C9.5 4.44772 9.94772 4 10.5 4H15.5C16.0523 4 16.5 4.44772 16.5 5C16.5 5.55229 16.0523 6 15.5 6H10.5C9.94772 6 9.5 5.55229 9.5 5ZM9.5 9C9.5 8.44772 9.94772 8 10.5 8H15.5C16.0523 8 16.5 8.44772 16.5 9C16.5 9.55229 16.0523 10 15.5 10H10.5C9.94772 10 9.5 9.55229 9.5 9ZM9.5 13C9.5 12.4477 9.94772 12 10.5 12H15.5C16.0523 12 16.5 12.4477 16.5 13C16.5 13.5523 16.0523 14 15.5 14H10.5C9.94772 14 9.5 13.5523 9.5 13Z"
      fill="#CA8A04"
    />
  </svg>
);

export const aplusContent = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 7.8C3 6.11984 3 5.27976 3.32698 4.63803C3.6146 4.07354 4.07354 3.6146 4.63803 3.32698C5.27976 3 6.11984 3 7.8 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11984 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V7.8Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 12.9999H15M7 16.9999L11.2717 7.60219C11.5031 7.09318 11.6188 6.83867 11.7791 6.75971C11.9184 6.6911 12.0816 6.6911 12.2209 6.75971C12.3812 6.83867 12.4969 7.09318 12.7283 7.60219L17 16.9999"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const aplusContentChecked = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7587 2H16.2413C17.0463 1.99999 17.7106 1.99998 18.2518 2.04419C18.8139 2.09012 19.3306 2.18868 19.816 2.43597C20.5686 2.81947 21.1805 3.43139 21.564 4.18404C21.8113 4.66937 21.9099 5.18608 21.9558 5.74817C22 6.28936 22 6.95372 22 7.75868V16.2413C22 17.0463 22 17.7106 21.9558 18.2518C21.9099 18.8139 21.8113 19.3306 21.564 19.816C21.1805 20.5686 20.5686 21.1805 19.816 21.564C19.3306 21.8113 18.8139 21.9099 18.2518 21.9558C17.7106 22 17.0463 22 16.2413 22H7.75868C6.95372 22 6.28936 22 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43597 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413V7.7587C1.99999 6.95373 1.99998 6.28937 2.04419 5.74817C2.09012 5.18608 2.18868 4.66937 2.43597 4.18404C2.81947 3.43139 3.43139 2.81947 4.18404 2.43597C4.66937 2.18868 5.18608 2.09012 5.74817 2.04419C6.28937 1.99998 6.95373 1.99999 7.7587 2ZM5.91104 4.03755C5.47262 4.07337 5.24842 4.1383 5.09202 4.21799C4.7157 4.40973 4.40973 4.7157 4.21799 5.09202C4.1383 5.24842 4.07337 5.47262 4.03755 5.91104C4.00078 6.36113 4 6.94342 4 7.8V16.2C4 17.0566 4.00078 17.6389 4.03755 18.089C4.07337 18.5274 4.1383 18.7516 4.21799 18.908C4.40973 19.2843 4.7157 19.5903 5.09202 19.782C5.24842 19.8617 5.47262 19.9266 5.91104 19.9624C6.36113 19.9992 6.94342 20 7.8 20H16.2C17.0566 20 17.6389 19.9992 18.089 19.9624C18.5274 19.9266 18.7516 19.8617 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C19.8617 18.7516 19.9266 18.5274 19.9624 18.089C19.9992 17.6389 20 17.0566 20 16.2V7.8C20 6.94342 19.9992 6.36113 19.9624 5.91104C19.9266 5.47262 19.8617 5.24842 19.782 5.09202C19.5903 4.7157 19.2843 4.40973 18.908 4.21799C18.7516 4.1383 18.5274 4.07337 18.089 4.03755C17.6389 4.00078 17.0566 4 16.2 4H7.8C6.94342 4 6.36113 4.00078 5.91104 4.03755Z"
      fill="#CA8A04"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.337 5.86238C11.7549 5.65655 12.2447 5.65655 12.6625 5.86238C13.0298 6.04325 13.2234 6.36031 13.3173 6.52557C13.4179 6.70269 13.5209 6.92957 13.6235 7.15521C13.6284 7.1662 13.6334 7.17718 13.6384 7.18815L17.9101 16.5859C18.1387 17.0887 17.9163 17.6815 17.4136 17.9101C16.9108 18.1386 16.3179 17.9163 16.0894 17.4135L14.5377 13.9997H9.46185L7.91012 17.4135C7.68159 17.9163 7.08873 18.1386 6.58595 17.9101C6.08317 17.6815 5.86085 17.0887 6.08939 16.5859L10.3611 7.18815C10.3661 7.17717 10.3711 7.16619 10.3761 7.15521C10.4786 6.92956 10.5817 6.70269 10.6823 6.52557C10.7761 6.36031 10.9698 6.04325 11.337 5.86238ZM10.3709 11.9997H13.6286L11.9998 8.41631L10.3709 11.9997Z"
      fill="#CA8A04"
    />
  </svg>
);

export const analytics = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.9754 4.01031C13.8588 4.00078 13.6965 4.00001 13.4 4.00001H10.6C10.3035 4.00001 10.1412 4.00078 10.0246 4.01031C10.02 4.01069 10.0156 4.01107 10.0115 4.01145C10.0111 4.0156 10.0107 4.02 10.0103 4.02464C10.0008 4.14122 10 4.30348 10 4.60001V6.99895C10 6.9993 10 6.99965 10 7.00001C10 7.00036 10 7.00071 10 7.00106V20H14V4.60001C14 4.30348 13.9992 4.14122 13.9897 4.02464C13.9893 4.02 13.9889 4.0156 13.9886 4.01145C13.9844 4.01107 13.98 4.01069 13.9754 4.01031ZM16 10L16 4.56809C16 4.31571 16.0001 4.06993 15.9831 3.86178C15.9644 3.63318 15.9203 3.36345 15.782 3.09202C15.5903 2.7157 15.2843 2.40974 14.908 2.21799C14.6366 2.07969 14.3668 2.03563 14.1382 2.01695C13.9301 1.99994 13.6843 1.99997 13.4319 2L10.5681 2C10.3157 1.99997 10.0699 1.99994 9.86178 2.01695C9.63318 2.03563 9.36345 2.07969 9.09202 2.21799C8.7157 2.40974 8.40974 2.7157 8.21799 3.09202C8.07969 3.36345 8.03563 3.63318 8.01695 3.86178C7.99994 4.06993 7.99997 4.31572 8 4.56811C8 4.57873 8.00001 4.58936 8.00001 4.60001V6.00001H4.60001C4.58936 6.00001 4.57873 6 4.56811 6C4.31572 5.99997 4.06993 5.99994 3.86178 6.01695C3.63318 6.03563 3.36345 6.07969 3.09202 6.21799C2.7157 6.40974 2.40974 6.7157 2.21799 7.09202C2.07969 7.36345 2.03563 7.63318 2.01695 7.86178C1.99994 8.06993 1.99997 8.31573 2 8.56811C2 8.57873 2.00001 8.58936 2.00001 8.60001L2 19.4319C1.99997 19.6843 1.99994 19.9301 2.01695 20.1382C2.03563 20.3668 2.07969 20.6366 2.21799 20.908C2.40974 21.2843 2.7157 21.5903 3.09203 21.782C3.36345 21.9203 3.63318 21.9644 3.86178 21.9831C4.06993 22.0001 4.31571 22 4.56809 22H19.4319C19.6843 22 19.9301 22.0001 20.1382 21.9831C20.3668 21.9644 20.6366 21.9203 20.908 21.782C21.2843 21.5903 21.5903 21.2843 21.782 20.908C21.9203 20.6366 21.9644 20.3668 21.9831 20.1382C22.0001 19.9301 22 19.6843 22 19.4319V12.5681C22 12.3157 22.0001 12.0699 21.9831 11.8618C21.9644 11.6332 21.9203 11.3634 21.782 11.092C21.5903 10.7157 21.2843 10.4097 20.908 10.218C20.6366 10.0797 20.3668 10.0356 20.1382 10.017C19.9301 9.99994 19.6843 9.99997 19.4319 10L16 10ZM16 12V20H19.4C19.6965 20 19.8588 19.9992 19.9754 19.9897C19.98 19.9893 19.9844 19.9889 19.9886 19.9885C19.9889 19.9844 19.9893 19.98 19.9897 19.9754C19.9992 19.8588 20 19.6965 20 19.4V12.6C20 12.3035 19.9992 12.1412 19.9897 12.0246C19.9893 12.02 19.9889 12.0156 19.9885 12.0115C19.9844 12.0111 19.98 12.0107 19.9754 12.0103C19.8588 12.0008 19.6965 12 19.4 12H16ZM8.00001 8.00001H4.60001C4.30348 8.00001 4.14122 8.00078 4.02464 8.01031C4.02 8.01069 4.0156 8.01107 4.01145 8.01145C4.01107 8.0156 4.01069 8.02 4.01031 8.02464C4.00078 8.14122 4.00001 8.30348 4.00001 8.60001V19.4C4.00001 19.6965 4.00078 19.8588 4.01031 19.9754C4.01069 19.98 4.01107 19.9844 4.01145 19.9885C4.0156 19.9889 4.02 19.9893 4.02464 19.9897C4.14122 19.9992 4.30348 20 4.6 20H8L8.00001 8.00001ZM3.09203 21.782C3.09203 21.782 3.09203 21.782 3.09203 21.782V21.782Z"
      fill="#030712"
    />
  </svg>
);

export const analyticsChecked = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.9754 4.01031C13.8588 4.00078 13.6965 4.00001 13.4 4.00001H10.6C10.3035 4.00001 10.1412 4.00078 10.0246 4.01031C10.02 4.01069 10.0156 4.01107 10.0115 4.01145C10.0111 4.0156 10.0107 4.02 10.0103 4.02464C10.0008 4.14122 10 4.30348 10 4.60001V6.99895C10 6.9993 10 6.99965 10 7.00001C10 7.00036 10 7.00071 10 7.00106V20H14V4.60001C14 4.30348 13.9992 4.14122 13.9897 4.02464C13.9893 4.02 13.9889 4.0156 13.9886 4.01145C13.9844 4.01107 13.98 4.01069 13.9754 4.01031ZM16 10L16 4.56809C16 4.31571 16.0001 4.06993 15.9831 3.86178C15.9644 3.63318 15.9203 3.36345 15.782 3.09202C15.5903 2.7157 15.2843 2.40974 14.908 2.21799C14.6366 2.07969 14.3668 2.03563 14.1382 2.01695C13.9301 1.99994 13.6843 1.99997 13.4319 2L10.5681 2C10.3157 1.99997 10.0699 1.99994 9.86178 2.01695C9.63318 2.03563 9.36345 2.07969 9.09202 2.21799C8.7157 2.40974 8.40974 2.7157 8.21799 3.09202C8.07969 3.36345 8.03563 3.63318 8.01695 3.86178C7.99994 4.06993 7.99997 4.31572 8 4.56811C8 4.57873 8.00001 4.58936 8.00001 4.60001V6.00001H4.60001C4.58936 6.00001 4.57873 6 4.56811 6C4.31572 5.99997 4.06993 5.99994 3.86178 6.01695C3.63318 6.03563 3.36345 6.07969 3.09202 6.21799C2.7157 6.40974 2.40974 6.7157 2.21799 7.09202C2.07969 7.36345 2.03563 7.63318 2.01695 7.86178C1.99994 8.06993 1.99997 8.31573 2 8.56811C2 8.57873 2.00001 8.58936 2.00001 8.60001L2 19.4319C1.99997 19.6843 1.99994 19.9301 2.01695 20.1382C2.03563 20.3668 2.07969 20.6366 2.21799 20.908C2.40974 21.2843 2.7157 21.5903 3.09203 21.782C3.36345 21.9203 3.63318 21.9644 3.86178 21.9831C4.06993 22.0001 4.31571 22 4.56809 22H19.4319C19.6843 22 19.9301 22.0001 20.1382 21.9831C20.3668 21.9644 20.6366 21.9203 20.908 21.782C21.2843 21.5903 21.5903 21.2843 21.782 20.908C21.9203 20.6366 21.9644 20.3668 21.9831 20.1382C22.0001 19.9301 22 19.6843 22 19.4319V12.5681C22 12.3157 22.0001 12.0699 21.9831 11.8618C21.9644 11.6332 21.9203 11.3634 21.782 11.092C21.5903 10.7157 21.2843 10.4097 20.908 10.218C20.6366 10.0797 20.3668 10.0356 20.1382 10.017C19.9301 9.99994 19.6843 9.99997 19.4319 10L16 10ZM16 12V20H19.4C19.6965 20 19.8588 19.9992 19.9754 19.9897C19.98 19.9893 19.9844 19.9889 19.9886 19.9885C19.9889 19.9844 19.9893 19.98 19.9897 19.9754C19.9992 19.8588 20 19.6965 20 19.4V12.6C20 12.3035 19.9992 12.1412 19.9897 12.0246C19.9893 12.02 19.9889 12.0156 19.9885 12.0115C19.9844 12.0111 19.98 12.0107 19.9754 12.0103C19.8588 12.0008 19.6965 12 19.4 12H16ZM8.00001 8.00001H4.60001C4.30348 8.00001 4.14122 8.00078 4.02464 8.01031C4.02 8.01069 4.0156 8.01107 4.01145 8.01145C4.01107 8.0156 4.01069 8.02 4.01031 8.02464C4.00078 8.14122 4.00001 8.30348 4.00001 8.60001V19.4C4.00001 19.6965 4.00078 19.8588 4.01031 19.9754C4.01069 19.98 4.01107 19.9844 4.01145 19.9885C4.0156 19.9889 4.02 19.9893 4.02464 19.9897C4.14122 19.9992 4.30348 20 4.6 20H8L8.00001 8.00001ZM3.09203 21.782C3.09203 21.782 3.09203 21.782 3.09203 21.782V21.782Z"
      fill="#CA8A04"
    />
  </svg>
);

export const setting = (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_10386_9435)">
      <path
        d="M9.96029 21.99C9.34029 21.99 8.73029 21.81 8.21029 21.47C7.69029 21.13 7.28029 20.65 7.02029 20.08L6.44029 18.77C6.32029 18.49 6.11029 18.27 5.85029 18.11C5.59029 17.96 5.29029 17.9 4.98029 17.93L3.55029 18.08C2.92029 18.14 2.31029 18.03 1.75029 17.75C1.20029 17.47 0.740288 17.03 0.420288 16.49C0.100288 15.95 -0.0297119 15.34 0.000288101 14.72C0.0302881 14.1 0.240288 13.5 0.610288 13L1.46029 11.84C1.64029 11.59 1.73029 11.3 1.73029 11C1.73029 10.7 1.63029 10.4 1.46029 10.16L0.610288 9C0.250288 8.48 0.0402881 7.89 0.000288101 7.27C-0.0297119 6.65 0.120288 6.03 0.430288 5.49C0.740288 4.95 1.20029 4.52 1.75029 4.23C2.31029 3.95 2.93029 3.83 3.55029 3.9L4.98029 4.05C5.27029 4.08 5.58029 4.02 5.85029 3.87C6.10029 3.72 6.31029 3.49 6.43029 3.22L7.01029 1.9C7.26029 1.33 7.67029 0.85 8.19029 0.51C9.24029 -0.17 10.6503 -0.17 11.6903 0.51C12.2103 0.85 12.6203 1.33 12.8703 1.9L13.4603 3.21C13.5803 3.49 13.7903 3.72 14.0503 3.87C14.3103 4.02 14.6103 4.08 14.9103 4.05L16.3403 3.9C16.9503 3.84 17.5803 3.95 18.1403 4.23C18.7003 4.51 19.1503 4.95 19.4703 5.49C19.7803 6.03 19.9303 6.64 19.9003 7.27C19.8703 7.89 19.6603 8.49 19.2903 8.99L18.4403 10.15C18.2603 10.4 18.1703 10.69 18.1703 10.99C18.1703 11.3 18.2603 11.59 18.4403 11.84L19.2803 13C19.6503 13.5 19.8603 14.1 19.8903 14.72C19.9203 15.34 19.7703 15.96 19.4603 16.5C19.1503 17.04 18.6903 17.47 18.1403 17.76C17.5903 18.04 16.9603 18.16 16.3503 18.09L14.9203 17.94C14.6203 17.91 14.3203 17.97 14.0603 18.12C13.8003 18.27 13.6003 18.5 13.4703 18.77L12.8903 20.08C12.6403 20.65 12.2303 21.13 11.7003 21.47C11.1803 21.81 10.5703 21.99 9.95029 21.99H9.96029ZM8.27029 17.96L8.85029 19.27C8.94029 19.48 9.10029 19.67 9.30029 19.8C9.69029 20.05 10.2303 20.06 10.6203 19.8C10.8203 19.67 10.9703 19.49 11.0703 19.27L11.6503 17.96C11.9503 17.3 12.4303 16.75 13.0603 16.39C13.6903 16.03 14.4103 15.87 15.1403 15.95L16.5703 16.1C16.8003 16.12 17.0403 16.08 17.2503 15.97C17.4603 15.86 17.6303 15.7 17.7503 15.5C17.8703 15.3 17.9203 15.06 17.9103 14.83C17.9003 14.6 17.8203 14.37 17.6803 14.18L16.8303 13.02C16.4003 12.42 16.1703 11.72 16.1803 10.99C16.1803 10.27 16.4103 9.57 16.8403 8.98L17.6903 7.82C17.8303 7.63 17.9103 7.4 17.9203 7.17C17.9303 6.94 17.8803 6.7 17.7603 6.5C17.6403 6.3 17.4703 6.13 17.2603 6.02C17.0503 5.91 16.8103 5.87 16.5803 5.9L15.1503 6.05C14.4303 6.13 13.7103 5.97 13.0703 5.61C12.4403 5.24 11.9503 4.7 11.6503 4.03L11.0603 2.72C10.9603 2.5 10.8103 2.32 10.6103 2.19C10.2203 1.93 9.68029 1.93 9.29029 2.19C9.09029 2.32 8.94029 2.5 8.84029 2.71L8.26029 4.02C7.97029 4.68 7.48029 5.23 6.85029 5.59C6.21029 5.96 5.49029 6.11 4.77029 6.03L3.33029 5.88C3.09029 5.85 2.86029 5.9 2.65029 6C2.44029 6.11 2.27029 6.27 2.15029 6.47C2.03029 6.67 1.98029 6.91 1.99029 7.14C2.00029 7.37 2.08029 7.6 2.22029 7.79L3.07029 8.95C3.50029 9.54 3.73029 10.24 3.73029 10.97C3.73029 11.7 3.50029 12.4 3.07029 12.99L2.22029 14.15C2.08029 14.34 2.00029 14.57 1.99029 14.8C1.98029 15.03 2.03029 15.27 2.15029 15.47C2.27029 15.67 2.44029 15.84 2.65029 15.94C2.86029 16.05 3.09029 16.09 3.33029 16.07L4.76029 15.92C5.49029 15.84 6.20029 16 6.84029 16.36C7.47029 16.73 7.96029 17.27 8.26029 17.94L8.27029 17.96Z"
        fill="#030712"
      />
      <path
        d="M9.95996 14.99C7.74996 14.99 5.95996 13.2 5.95996 10.99C5.95996 8.78001 7.74996 6.99001 9.95996 6.99001C12.17 6.99001 13.96 8.78001 13.96 10.99C13.96 13.2 12.17 14.99 9.95996 14.99ZM9.95996 8.99001C8.85996 8.99001 7.95996 9.89001 7.95996 10.99C7.95996 12.09 8.85996 12.99 9.95996 12.99C11.06 12.99 11.96 12.09 11.96 10.99C11.96 9.89001 11.06 8.99001 9.95996 8.99001Z"
        fill="#030712"
      />
    </g>
    <defs>
      <clipPath id="clip0_10386_9435">
        <rect width="19.91" height="21.99" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const hamburger = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 12H21M3 6H21M3 18H21"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const general = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.7587 4.31291e-07H16.2413C17.0463 -1.23241e-05 17.7106 -2.28137e-05 18.2518 0.0441945C18.8139 0.0901197 19.3306 0.188684 19.816 0.435975C20.5686 0.819468 21.1805 1.43139 21.564 2.18404C21.8113 2.66937 21.9099 3.18608 21.9558 3.74818C22 4.28937 22 4.95372 22 5.75868V16.2413C22 17.0463 22 17.7106 21.9558 18.2518C21.9099 18.8139 21.8113 19.3306 21.564 19.816C21.1805 20.5686 20.5686 21.1805 19.816 21.564C19.6461 21.6506 19.4713 21.7195 19.2899 21.7745C18.5428 22.0008 17.5943 22.0005 16.3661 22C16.3113 22 16.2559 22 16.2 22H5.8C5.74407 22 5.68871 22 5.6339 22C4.40566 22.0005 3.45716 22.0008 2.71007 21.7745C2.52872 21.7195 2.35389 21.6506 2.18404 21.564C1.43139 21.1805 0.819468 20.5686 0.435975 19.816C0.188684 19.3306 0.0901197 18.8139 0.0441945 18.2518C-2.28137e-05 17.7106 -1.23241e-05 17.0463 4.31291e-07 16.2413V5.7587C-1.23241e-05 4.95373 -2.28137e-05 4.28937 0.0441945 3.74818C0.0901197 3.18608 0.188684 2.66937 0.435975 2.18404C0.819468 1.43139 1.43139 0.819468 2.18404 0.435975C2.66937 0.188684 3.18608 0.0901197 3.74818 0.0441945C4.28937 -2.28137e-05 4.95373 -1.23241e-05 5.7587 4.31291e-07ZM3.91104 2.03755C3.47262 2.07337 3.24842 2.1383 3.09202 2.21799C2.7157 2.40973 2.40973 2.7157 2.21799 3.09202C2.1383 3.24842 2.07337 3.47262 2.03755 3.91104C2.00078 4.36113 2 4.94342 2 5.8V16.2C2 17.0566 2.00078 17.6389 2.03755 18.089C2.06354 18.407 2.10483 18.6123 2.15597 18.7607C2.63035 16.9045 4.12992 15.4729 6.02455 15.0961C6.50935 14.9996 7.06664 14.9998 7.88424 15C7.92226 15 7.96084 15 8 15H14C14.0392 15 14.0777 15 14.1158 15C14.9334 14.9998 15.4907 14.9996 15.9755 15.0961C17.8701 15.4729 19.3697 16.9045 19.844 18.7607C19.8952 18.6123 19.9365 18.407 19.9625 18.089C19.9992 17.6389 20 17.0566 20 16.2V5.8C20 4.94342 19.9992 4.36113 19.9625 3.91104C19.9266 3.47262 19.8617 3.24842 19.782 3.09202C19.5903 2.7157 19.2843 2.40973 18.908 2.21799C18.7516 2.1383 18.5274 2.07337 18.089 2.03755C17.6389 2.00078 17.0566 2 16.2 2H5.8C4.94342 2 4.36113 2.00078 3.91104 2.03755ZM17.9918 19.9701C17.984 19.7089 17.9693 19.5503 17.9424 19.4147C17.7056 18.2247 16.7753 17.2944 15.5853 17.0576C15.3198 17.0048 14.9772 17 14 17H8C7.02276 17 6.68018 17.0048 6.41473 17.0576C5.22466 17.2944 4.29437 18.2247 4.05765 19.4147C4.03068 19.5503 4.01596 19.7089 4.00817 19.9701C4.41515 19.9965 4.97445 20 5.8 20H16.2C17.0256 20 17.5849 19.9965 17.9918 19.9701ZM11 5.5C9.34315 5.5 8 6.84315 8 8.5C8 10.1569 9.34315 11.5 11 11.5C12.6569 11.5 14 10.1569 14 8.5C14 6.84315 12.6569 5.5 11 5.5ZM6 8.5C6 5.73858 8.23858 3.5 11 3.5C13.7614 3.5 16 5.73858 16 8.5C16 11.2614 13.7614 13.5 11 13.5C8.23858 13.5 6 11.2614 6 8.5Z"
      fill="#030712"
    />
  </svg>
);

export const subscriptionAndBilling = (
  <svg
    width="18"
    height="20"
    viewBox="0 0 18 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.7587 4.31291e-07H12.2413C13.0463 -1.23241e-05 13.7106 -2.28137e-05 14.2518 0.0441945C14.8139 0.0901197 15.3306 0.188684 15.816 0.435975C16.5686 0.819468 17.1805 1.43139 17.564 2.18404C17.8113 2.66937 17.9099 3.18608 17.9558 3.74817C18 4.28936 18 4.95372 18 5.75868V19C18 19.3761 17.7889 19.7204 17.4538 19.8911C17.1186 20.0618 16.716 20.03 16.4118 19.8087L14.2789 18.2575L12.3747 19.7809C12.0229 20.0623 11.5262 20.0738 11.1618 19.8087L9 18.2365L6.83817 19.8087C6.47378 20.0738 5.97715 20.0623 5.62531 19.7809L3.72111 18.2575L1.58817 19.8087C1.28399 20.03 0.881394 20.0618 0.546223 19.8911C0.211053 19.7204 9.08128e-07 19.3761 9.08128e-07 19L4.31291e-07 5.7587C-1.23241e-05 4.95373 -2.28137e-05 4.28937 0.0441945 3.74817C0.0901197 3.18608 0.188684 2.66937 0.435975 2.18404C0.819468 1.43139 1.43139 0.819468 2.18404 0.435975C2.66937 0.188684 3.18608 0.0901197 3.74818 0.0441945C4.28937 -2.28137e-05 4.95373 -1.23241e-05 5.7587 4.31291e-07ZM3.91104 2.03755C3.47262 2.07337 3.24842 2.1383 3.09202 2.21799C2.7157 2.40973 2.40973 2.7157 2.21799 3.09202C2.1383 3.24842 2.07337 3.47262 2.03755 3.91104C2.00078 4.36113 2 4.94342 2 5.8V17.0362L3.16183 16.1913C3.52623 15.9262 4.02285 15.9377 4.3747 16.2191L6.27889 17.7425L8.41183 16.1913C8.76248 15.9362 9.23752 15.9362 9.58817 16.1913L11.7211 17.7425L13.6253 16.2191C13.9771 15.9377 14.4738 15.9262 14.8382 16.1913L16 17.0362V5.8C16 4.94342 15.9992 4.36113 15.9625 3.91104C15.9266 3.47262 15.8617 3.24842 15.782 3.09202C15.5903 2.7157 15.2843 2.40973 14.908 2.21799C14.7516 2.1383 14.5274 2.07337 14.089 2.03755C13.6389 2.00078 13.0566 2 12.2 2H5.8C4.94342 2 4.36113 2.00078 3.91104 2.03755ZM13.2071 5.29289C13.5976 5.68342 13.5976 6.31658 13.2071 6.70711L8.70711 11.2071C8.31658 11.5976 7.68342 11.5976 7.2929 11.2071L5.29289 9.20711C4.90237 8.81658 4.90237 8.18342 5.29289 7.79289C5.68342 7.40237 6.31658 7.40237 6.70711 7.79289L8 9.08579L11.7929 5.29289C12.1834 4.90237 12.8166 4.90237 13.2071 5.29289Z"
      fill="#030712"
    />
  </svg>
);

export const notification = (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.4143 2.58579C16.6333 1.80474 15.3669 1.80474 14.5859 2.58579C13.8048 3.36683 13.8048 4.63317 14.5859 5.41421C15.3669 6.19526 16.6333 6.19526 17.4143 5.41421C18.1954 4.63317 18.1954 3.36683 17.4143 2.58579ZM13.1717 1.17157C14.7338 -0.390524 17.2664 -0.390524 18.8285 1.17157C20.3906 2.73367 20.3906 5.26633 18.8285 6.82843C17.2664 8.39052 14.7338 8.39052 13.1717 6.82843C11.6096 5.26633 11.6096 2.73367 13.1717 1.17157ZM5.7588 1H9.0001C9.55238 1 10.0001 1.44772 10.0001 2C10.0001 2.55228 9.55238 3 9.0001 3H5.8001C4.94352 3 4.36122 3.00078 3.91113 3.03755C3.47272 3.07337 3.24852 3.1383 3.09212 3.21799C2.71579 3.40973 2.40983 3.71569 2.21808 4.09202C2.13839 4.24842 2.07347 4.47262 2.03765 4.91104C2.00087 5.36113 2.0001 5.94342 2.0001 6.8V12C2.0001 12.9944 2.00869 13.2954 2.06824 13.5176C2.25318 14.2078 2.79227 14.7469 3.48246 14.9319C3.70474 14.9914 4.00574 15 5.0001 15C5.55238 15 6.0001 15.4477 6.0001 16V17.9194L8.06061 16.271C8.08335 16.2528 8.10578 16.2348 8.12793 16.2171C8.55 15.8791 8.86906 15.6237 9.23263 15.4379C9.55358 15.274 9.89522 15.1541 10.2483 15.0817C10.6482 14.9996 11.0569 14.9998 11.5976 15C11.626 15 11.6547 15 11.6838 15H13.2001C14.0567 15 14.639 14.9992 15.0891 14.9624C15.5275 14.9266 15.7517 14.8617 15.9081 14.782C16.2844 14.5903 16.5904 14.2843 16.7821 13.908C16.8618 13.7516 16.9267 13.5274 16.9625 13.089C16.9993 12.6389 17.0001 12.0566 17.0001 11.2V11C17.0001 10.4477 17.4478 10 18.0001 10C18.5524 10 19.0001 10.4477 19.0001 11V11.2413C19.0001 12.0463 19.0001 12.7106 18.9559 13.2518C18.91 13.8139 18.8114 14.3306 18.5641 14.816C18.1806 15.5686 17.5687 16.1805 16.8161 16.564C16.3307 16.8113 15.814 16.9099 15.2519 16.9558C14.7107 17 14.0464 17 13.2414 17H11.6838C11.0197 17 10.8263 17.0047 10.6504 17.0408C10.4738 17.0771 10.303 17.137 10.1425 17.219C9.98255 17.3007 9.82859 17.4178 9.31 17.8327L6.89692 19.7632C6.71323 19.9102 6.52598 20.06 6.36137 20.1689C6.20394 20.273 5.8987 20.4593 5.50173 20.4597C5.0449 20.4602 4.61276 20.2525 4.32778 19.8955C4.08013 19.5852 4.03492 19.2305 4.01785 19.0425C4 18.846 4.00005 18.6062 4.00009 18.371L4.0001 16.9918C3.60829 16.9789 3.27229 16.9461 2.96482 16.8637C1.58445 16.4938 0.506262 15.4156 0.136393 14.0353C-0.00070332 13.5236 -0.000383486 12.933 4.72167e-05 12.1376C7.16546e-05 12.0924 9.64501e-05 12.0465 9.64501e-05 12L9.59733e-05 6.7587C8.32179e-05 5.95373 7.27288e-05 5.28937 0.0442901 4.74817C0.0902153 4.18608 0.18878 3.66937 0.43607 3.18404C0.819564 2.43139 1.43149 1.81947 2.18413 1.43597C2.66947 1.18868 3.18617 1.09012 3.74827 1.04419C4.28947 0.999976 4.95383 0.999987 5.7588 1Z"
      fill="#030712"
    />
  </svg>
);

export const security = (
  <svg
    width="18"
    height="22"
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.71423 0.0885525C8.90394 0.0611401 9.09659 0.0611401 9.2863 0.0885525C9.50493 0.120145 9.70837 0.197001 9.86991 0.258026C9.88469 0.263612 9.89912 0.269064 9.91319 0.274337L15.275 2.28501C15.3189 2.30148 15.3624 2.31774 15.4056 2.33386C16.0234 2.56451 16.566 2.76707 16.9838 3.13529C17.3493 3.45741 17.6309 3.8637 17.8041 4.31905C18.0022 4.83956 18.0014 5.41867 18.0004 6.07812C18.0003 6.12421 18.0003 6.1707 18.0003 6.21759V11C18.0003 13.8266 16.463 16.1832 14.8012 17.9144C13.1283 19.6572 11.2143 20.8882 10.2022 21.4786C10.1888 21.4864 10.1752 21.4944 10.1612 21.5026C9.97649 21.6111 9.73677 21.7519 9.41963 21.8199C9.16017 21.8755 8.84036 21.8755 8.5809 21.8199C8.26376 21.7519 8.02403 21.6111 7.83934 21.5026C7.82537 21.4944 7.81171 21.4864 7.79836 21.4786C6.78626 20.8882 4.87222 19.6572 3.19932 17.9144C1.53756 16.1832 0.000264831 13.8266 0.000264831 11V6.21759C0.000264831 6.17071 0.000197478 6.12422 0.00013072 6.07812C-0.000824265 5.41868 -0.00166303 4.83956 0.196405 4.31905C0.369677 3.8637 0.651239 3.45741 1.01675 3.13529C1.43457 2.76707 1.97712 2.56451 2.59491 2.33386C2.63809 2.31773 2.68164 2.30148 2.72555 2.28501L8.08734 0.274338C8.10141 0.269064 8.11584 0.263611 8.13062 0.258026C8.29216 0.197001 8.4956 0.120145 8.71423 0.0885525ZM8.99725 2.07127C8.9564 2.08466 8.901 2.10522 8.78959 2.147L3.42779 4.15767C2.60255 4.46713 2.44374 4.54354 2.33909 4.63576C2.21726 4.74313 2.1234 4.87856 2.06564 5.03034C2.01604 5.16071 2.00026 5.33624 2.00026 6.21759V11C2.00026 13.0818 3.13995 14.9644 4.64218 16.5294C6.13327 18.0828 7.87024 19.2051 8.80618 19.7511C8.86339 19.7845 8.90465 19.8085 8.94053 19.8287C8.96869 19.8446 8.98742 19.8546 9.00027 19.8611C9.01311 19.8546 9.03185 19.8446 9.06 19.8287C9.09588 19.8085 9.13714 19.7845 9.19435 19.7511C10.1303 19.2051 11.8673 18.0828 13.3584 16.5294C14.8606 14.9644 16.0003 13.0818 16.0003 11V6.21759C16.0003 5.33624 15.9845 5.16071 15.9349 5.03034C15.8771 4.87856 15.7833 4.74313 15.6614 4.63576C15.5568 4.54354 15.398 4.46713 14.5727 4.15767L9.21094 2.147C9.09953 2.10522 9.04413 2.08466 9.00328 2.07127C9.00224 2.07092 9.00123 2.0706 9.00027 2.07028C8.9993 2.0706 8.99829 2.07092 8.99725 2.07127ZM13.2074 7.29289C13.5979 7.68341 13.5979 8.31658 13.2074 8.7071L8.70737 13.2071C8.31685 13.5976 7.68368 13.5976 7.29316 13.2071L5.29316 11.2071C4.90263 10.8166 4.90263 10.1834 5.29316 9.79289C5.68368 9.40236 6.31685 9.40236 6.70737 9.79289L8.00027 11.0858L11.7932 7.29289C12.1837 6.90236 12.8168 6.90236 13.2074 7.29289Z"
      fill="#030712"
    />
  </svg>
);

export const connection = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 20C5 19.4477 4.55228 19 4 19C3.44772 19 3 19.4477 3 20C3 20.5523 3.44772 21 4 21C4.55228 21 5 20.5523 5 20ZM13 20C13 19.4477 12.5523 19 12 19C11.4477 19 11 19.4477 11 20C11 20.5523 11.4477 21 12 21C12.5523 21 13 20.5523 13 20ZM21 20C21 19.4477 20.5523 19 20 19C19.4477 19 19 19.4477 19 20C19 20.5523 19.4477 21 20 21C20.5523 21 21 20.5523 21 20ZM20 5C20 4.52038 19.9996 4.21061 19.9834 3.97363C19.9678 3.74572 19.9411 3.65879 19.9238 3.61719C19.8223 3.37226 19.6277 3.17766 19.3828 3.07617C19.3412 3.05894 19.2543 3.03216 19.0264 3.0166C18.7894 3.00043 18.4796 3 18 3H6C5.52038 3 5.21061 3.00043 4.97363 3.0166C4.74572 3.03216 4.65879 3.05894 4.61719 3.07617C4.37226 3.17766 4.17766 3.37226 4.07617 3.61719C4.05894 3.65879 4.03216 3.74572 4.0166 3.97363C4.00043 4.21061 4 4.52038 4 5C4 5.47962 4.00043 5.78939 4.0166 6.02637C4.03216 6.25428 4.05894 6.34121 4.07617 6.38281C4.17766 6.62774 4.37226 6.82234 4.61719 6.92383C4.65879 6.94106 4.74572 6.96784 4.97363 6.9834C5.21061 6.99957 5.52038 7 6 7H18C18.4796 7 18.7894 6.99957 19.0264 6.9834C19.2543 6.96784 19.3412 6.94106 19.3828 6.92383C19.6277 6.82234 19.8223 6.62774 19.9238 6.38281C19.9411 6.34121 19.9678 6.25428 19.9834 6.02637C19.9996 5.78939 20 5.47962 20 5ZM22 5C22 5.45214 22.0004 5.84131 21.9785 6.16211C21.956 6.49194 21.9065 6.82251 21.7715 7.14844C21.467 7.88321 20.8832 8.46701 20.1484 8.77148C19.8225 8.90649 19.4919 8.95601 19.1621 8.97852C18.8413 9.00039 18.4521 9 18 9H13V12H15.2002C16.0237 12 16.7016 11.999 17.252 12.0439C17.814 12.0899 18.3311 12.1883 18.8164 12.4355C19.5689 12.819 20.181 13.4311 20.5645 14.1836C20.8117 14.6689 20.9101 15.186 20.9561 15.748C20.9891 16.1519 20.995 16.6245 20.9971 17.1729C22.1633 17.5841 23 18.6931 23 20C23 21.6569 21.6569 23 20 23C18.3431 23 17 21.6569 17 20C17 18.6952 17.8339 17.5877 18.9971 17.1748C18.995 16.6445 18.9899 16.2421 18.9629 15.9111C18.9271 15.4727 18.8619 15.2482 18.7822 15.0918C18.5905 14.7155 18.2845 14.4095 17.9082 14.2178C17.7518 14.1381 17.5273 14.0729 17.0889 14.0371C16.6388 14.0003 16.0566 14 15.2002 14H13V17.1738C14.1647 17.5859 15 18.6941 15 20C15 21.6569 13.6569 23 12 23C10.3431 23 9 21.6569 9 20C9 18.6941 9.83532 17.5859 11 17.1738V14H8.7998C7.94341 14 7.36117 14.0003 6.91113 14.0371C6.47272 14.0729 6.2482 14.1381 6.0918 14.2178C5.71554 14.4095 5.40951 14.7155 5.21777 15.0918C5.13808 15.2482 5.07293 15.4727 5.03711 15.9111C5.01008 16.2421 5.00407 16.6445 5.00195 17.1748C6.16556 17.5875 7 18.6949 7 20C7 21.6569 5.65685 23 4 23C2.34315 23 1 21.6569 1 20C1 18.6934 1.83621 17.5844 3.00195 17.1729C3.00407 16.6245 3.01095 16.1519 3.04395 15.748C3.08987 15.186 3.18827 14.6689 3.43555 14.1836C3.81902 13.4311 4.43109 12.819 5.18359 12.4355C5.6689 12.1883 6.18599 12.0899 6.74805 12.0439C7.29843 11.999 7.97632 12 8.7998 12H11V9H6C5.54786 9 5.15869 9.00039 4.83789 8.97852C4.50806 8.95601 4.17749 8.90649 3.85156 8.77148C3.11679 8.46701 2.53299 7.88321 2.22852 7.14844C2.09351 6.82251 2.04399 6.49194 2.02148 6.16211C1.99961 5.84131 2 5.45214 2 5C2 4.54786 1.99961 4.15869 2.02148 3.83789C2.04399 3.50806 2.09351 3.17749 2.22852 2.85156C2.53299 2.11679 3.11679 1.53299 3.85156 1.22852C4.17749 1.09351 4.50806 1.04399 4.83789 1.02148C5.15869 0.999612 5.54786 1 6 1H18C18.4521 1 18.8413 0.999612 19.1621 1.02148C19.4919 1.04399 19.8225 1.09351 20.1484 1.22852C20.8832 1.53299 21.467 2.11679 21.7715 2.85156C21.9065 3.17749 21.956 3.50806 21.9785 3.83789C22.0004 4.15869 22 4.54786 22 5Z"
      fill="#030712"
    />
  </svg>
);

export const userManagement = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.50016 14C12.7146 14.0001 15.5221 15.7557 17.2726 18.3838C17.4329 18.6245 17.6081 18.8841 17.7287 19.1299C17.8665 19.411 17.9657 19.7344 17.9425 20.1201C17.924 20.4276 17.8226 20.713 17.7013 20.9395C17.58 21.166 17.3983 21.4088 17.1525 21.5947C16.8232 21.8438 16.4682 21.9313 16.1545 21.9678C15.866 22.0013 15.5221 22 15.1779 22H3.82242C3.478 22 3.13349 22.0013 2.84488 21.9678C2.53125 21.9313 2.17605 21.8437 1.84684 21.5947C1.60125 21.4089 1.42025 21.1659 1.29898 20.9395C1.1777 20.7129 1.0753 20.4276 1.0568 20.1201C1.03364 19.7344 1.13283 19.411 1.27066 19.1299C1.39119 18.884 1.56635 18.6246 1.72672 18.3838C3.47722 15.7555 6.28553 14 9.50016 14ZM17.1066 15.3877C17.3546 14.8945 17.9551 14.6958 18.4484 14.9434C20.0828 15.7644 21.4651 17.0719 22.465 18.6826C22.5975 18.896 22.7831 19.1719 22.8732 19.5332L22.9054 19.6934L22.924 19.8701C22.9458 20.2778 22.8265 20.6613 22.6593 20.9619C22.4681 21.3057 22.1514 21.6537 21.7111 21.8408C21.2925 22.0187 20.8371 22 20.5002 22C19.9479 22 19.5002 21.5523 19.5002 21C19.5002 20.4477 19.9479 20 20.5002 20C20.7146 20 20.8277 19.9986 20.9084 19.9932C20.909 19.992 20.9106 19.9914 20.9113 19.9902C20.9123 19.9884 20.9123 19.9861 20.9132 19.9844C20.9039 19.9673 20.8926 19.9444 20.8752 19.915C20.8466 19.867 20.8122 19.8121 20.7658 19.7373C19.9435 18.4127 18.8283 17.3722 17.5509 16.7305C17.0575 16.4825 16.8587 15.8812 17.1066 15.3877ZM9.50016 16C7.05372 16 4.82988 17.3332 3.39176 19.4922C3.30242 19.6263 3.23378 19.73 3.17691 19.8203C3.13261 19.8907 3.10376 19.9427 3.08316 19.9814C3.2292 19.9977 3.43673 20 3.82242 20H15.1779C15.5629 20 15.7702 19.9977 15.9162 19.9814C15.8956 19.9428 15.8676 19.8905 15.8234 19.8203C15.7665 19.7299 15.6979 19.6264 15.6086 19.4922C14.1705 17.3331 11.9466 16.0001 9.50016 16ZM13.0002 7.5C13.0002 5.56706 11.4331 4.00009 9.50016 4C7.56716 4 6.00016 5.567 6.00016 7.5C6.00016 9.433 7.56716 11 9.50016 11C11.4331 10.9999 13.0002 9.43294 13.0002 7.5ZM17.5002 7.5C17.5002 6.21284 16.8054 5.08614 15.7668 4.47754L15.5548 4.36328L15.466 4.31348C15.0369 4.04329 14.8742 3.48625 15.1046 3.02246C15.3351 2.55912 15.8766 2.35301 16.3507 2.53125L16.4455 2.57227L16.7775 2.75195C18.4046 3.70568 19.5002 5.47414 19.5002 7.5C19.5002 9.66082 18.2538 11.5289 16.4455 12.4277C15.951 12.6735 15.3505 12.4719 15.1046 11.9775C14.8589 11.483 15.0603 10.8825 15.5548 10.6367C16.7097 10.0628 17.5002 8.87276 17.5002 7.5ZM15.0002 7.5C15.0002 10.5375 12.5376 12.9999 9.50016 13C6.46259 13 4.00016 10.5376 4.00016 7.5C4.00016 4.46243 6.46259 2 9.50016 2C12.5376 2.00009 15.0002 4.46249 15.0002 7.5Z"
      fill="#030712"
    />
  </svg>
);
export const languageAndRegion = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 11H21M1 11C1 16.5228 5.47715 21 11 21M1 11C1 5.47715 5.47715 1 11 1M21 11C21 16.5228 16.5228 21 11 21M21 11C21 5.47715 16.5228 1 11 1M11 1C13.5013 3.73835 14.9228 7.29203 15 11C14.9228 14.708 13.5013 18.2616 11 21M11 1C8.49872 3.73835 7.07725 7.29203 7 11C7.07725 14.708 8.49872 18.2616 11 21"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const close = (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11C20 6.02944 15.9706 2 11 2ZM0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 17.0751 17.0751 22 11 22C4.92487 22 0 17.0751 0 11ZM7.29289 7.29289C7.68342 6.90237 8.31658 6.90237 8.70711 7.29289L11 9.58579L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L12.4142 11L14.7071 13.2929C15.0976 13.6834 15.0976 14.3166 14.7071 14.7071C14.3166 15.0976 13.6834 15.0976 13.2929 14.7071L11 12.4142L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071C6.90237 14.3166 6.90237 13.6834 7.29289 13.2929L9.58579 11L7.29289 8.70711C6.90237 8.31658 6.90237 7.68342 7.29289 7.29289Z"
      fill="#030712"
    />
  </svg>
);

export const deleteIc = (color = '#B91C1C') => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.16146 8.6011e-07H10.8385C11.3657 -1.70213e-05 11.8205 -3.25181e-05 12.195 0.0305712C12.5904 0.0628723 12.9836 0.134188 13.362 0.326982C13.9265 0.614602 14.3854 1.07354 14.673 1.63803C14.8658 2.01641 14.9371 2.40963 14.9694 2.80497C14.9969 3.14075 14.9997 3.54097 15 4H19C19.5523 4 20 4.44772 20 5C20 5.55229 19.5523 6 19 6H18V16.2413C18 17.0463 18 17.7106 17.9558 18.2518C17.9099 18.8139 17.8113 19.3306 17.564 19.816C17.1805 20.5686 16.5686 21.1805 15.816 21.564C15.3306 21.8113 14.8139 21.9099 14.2518 21.9558C13.7106 22 13.0463 22 12.2413 22H7.75868C6.95372 22 6.28936 22 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43597 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413L2 6H1C0.447715 6 0 5.55229 0 5C0 4.44772 0.447715 4 1 4H5.00003C5.00031 3.54097 5.00314 3.14075 5.03057 2.80497C5.06287 2.40963 5.13419 2.01641 5.32698 1.63803C5.6146 1.07354 6.07354 0.614602 6.63803 0.326982C7.01641 0.134188 7.40963 0.0628723 7.80497 0.0305713C8.17954 -3.25181e-05 8.63431 -1.70213e-05 9.16146 8.6011e-07ZM4 6V16.2C4 17.0566 4.00078 17.6389 4.03755 18.089C4.07337 18.5274 4.1383 18.7516 4.21799 18.908C4.40973 19.2843 4.71569 19.5903 5.09202 19.782C5.24842 19.8617 5.47262 19.9266 5.91104 19.9625C6.36113 19.9992 6.94342 20 7.8 20H12.2C13.0566 20 13.6389 19.9992 14.089 19.9625C14.5274 19.9266 14.7516 19.8617 14.908 19.782C15.2843 19.5903 15.5903 19.2843 15.782 18.908C15.8617 18.7516 15.9266 18.5274 15.9624 18.089C15.9992 17.6389 16 17.0566 16 16.2V6H4ZM12.9999 4H7.00007C7.00051 3.5349 7.00357 3.21698 7.02393 2.96784C7.04612 2.69617 7.0838 2.59546 7.10899 2.54601C7.20487 2.35785 7.35785 2.20487 7.54601 2.109C7.59545 2.0838 7.69617 2.04612 7.96784 2.02393C8.25117 2.00078 8.62345 2 9.2 2H10.8C11.3766 2 11.7488 2.00078 12.0322 2.02393C12.3038 2.04612 12.4045 2.0838 12.454 2.109C12.6422 2.20487 12.7951 2.35785 12.891 2.54601C12.9162 2.59546 12.9539 2.69617 12.9761 2.96784C12.9964 3.21698 12.9995 3.5349 12.9999 4ZM8 9.5C8.55228 9.5 9 9.94772 9 10.5V15.5C9 16.0523 8.55228 16.5 8 16.5C7.44772 16.5 7 16.0523 7 15.5V10.5C7 9.94772 7.44772 9.5 8 9.5ZM12 9.5C12.5523 9.5 13 9.94772 13 10.5V15.5C13 16.0523 12.5523 16.5 12 16.5C11.4477 16.5 11 16.0523 11 15.5V10.5C11 9.94772 11.4477 9.5 12 9.5Z"
      fill={color}
    />
  </svg>
);

export const editIc = (color = '#1E40AF') => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.7929 1.79289C18.0118 0.573939 19.9882 0.573941 21.2071 1.79289C22.4261 3.01184 22.4261 4.98815 21.2071 6.20711L11.6444 15.7698C11.6246 15.7896 11.605 15.8093 11.5855 15.8288C11.2981 16.1168 11.0446 16.3707 10.7391 16.5579C10.4707 16.7224 10.178 16.8436 9.87191 16.9171C9.52346 17.0008 9.16471 17.0004 8.75781 17C8.73027 17 8.70251 17 8.67452 17H6.99997C6.44769 17 5.99997 16.5523 5.99997 16V14.3255C5.99997 14.2975 5.99995 14.2697 5.99992 14.2422C5.99954 13.8353 5.99921 13.4765 6.08286 13.1281C6.15636 12.822 6.27757 12.5293 6.44206 12.2609C6.62929 11.9554 6.8832 11.702 7.17119 11.4145C7.19068 11.395 7.21033 11.3754 7.23013 11.3556L16.7929 1.79289ZM19.7929 3.2071C19.355 2.7692 18.645 2.7692 18.2071 3.2071L8.64434 12.7698C8.26478 13.1494 8.19479 13.2285 8.14733 13.3059C8.0925 13.3954 8.0521 13.4929 8.0276 13.595C8.0064 13.6833 7.99997 13.7887 7.99997 14.3255V15H8.67452C9.2113 15 9.3167 14.9936 9.40502 14.9724C9.50706 14.9479 9.6046 14.9075 9.69407 14.8526C9.77152 14.8052 9.85059 14.7352 10.2302 14.3556L19.7929 4.79289C20.2308 4.35499 20.2308 3.64501 19.7929 3.2071ZM5.7587 3L10 3C10.5523 3 11 3.44771 11 4C11 4.55228 10.5523 5 10 5H5.8C4.94342 5 4.36113 5.00078 3.91104 5.03755C3.47262 5.07337 3.24842 5.1383 3.09202 5.21799C2.7157 5.40973 2.40973 5.71569 2.21799 6.09202C2.1383 6.24842 2.07337 6.47262 2.03755 6.91104C2.00078 7.36113 2 7.94342 2 8.8V17.2C2 18.0566 2.00078 18.6389 2.03755 19.089C2.07337 19.5274 2.1383 19.7516 2.21799 19.908C2.40973 20.2843 2.7157 20.5903 3.09202 20.782C3.24842 20.8617 3.47262 20.9266 3.91104 20.9624C4.36113 20.9992 4.94342 21 5.8 21H14.2C15.0566 21 15.6389 20.9992 16.089 20.9624C16.5274 20.9266 16.7516 20.8617 16.908 20.782C17.2843 20.5903 17.5903 20.2843 17.782 19.908C17.8617 19.7516 17.9266 19.5274 17.9624 19.089C17.9992 18.6389 18 18.0566 18 17.2V13C18 12.4477 18.4477 12 19 12C19.5523 12 20 12.4477 20 13V17.2413C20 18.0463 20 18.7106 19.9558 19.2518C19.9099 19.8139 19.8113 20.3306 19.564 20.816C19.1805 21.5686 18.5686 22.1805 17.816 22.564C17.3306 22.8113 16.8139 22.9099 16.2518 22.9558C15.7106 23 15.0463 23 14.2413 23H5.75868C4.95372 23 4.28936 23 3.74817 22.9558C3.18608 22.9099 2.66937 22.8113 2.18404 22.564C1.43139 22.1805 0.819468 21.5686 0.435975 20.816C0.188684 20.3306 0.0901197 19.8139 0.0441945 19.2518C-2.28137e-05 18.7106 -1.23241e-05 18.0463 4.31292e-07 17.2413V8.7587C-1.23241e-05 7.95373 -2.28137e-05 7.28937 0.0441945 6.74817C0.0901197 6.18608 0.188684 5.66937 0.435975 5.18404C0.819468 4.43139 1.43139 3.81947 2.18404 3.43597C2.66937 3.18868 3.18608 3.09012 3.74817 3.04419C4.28937 2.99998 4.95373 2.99999 5.7587 3Z"
      fill={color}
    />
  </svg>
);

export const cardIc = (
  <svg
    width="40"
    height="28"
    viewBox="0 0 40 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M35.6522 0.0876083H4.34776C1.94656 0.0876083 0 2.03417 0 4.43537V23.5659C0 25.9671 1.94656 27.9136 4.34776 27.9136H35.6522C38.0534 27.9136 40 25.9671 40 23.5659V4.43537C40 2.03417 38.0534 0.0876083 35.6522 0.0876083Z"
      fill="#566FF8"
    />
    <path
      d="M26.0879 23.5652H22.6096C22.4943 23.567 22.3798 23.5458 22.2727 23.5029C22.1657 23.46 22.0682 23.3962 21.986 23.3153C21.9038 23.2344 21.8386 23.1379 21.794 23.0315C21.7495 22.9251 21.7266 22.8109 21.7266 22.6956C21.7266 22.5803 21.7495 22.4661 21.794 22.3597C21.8386 22.2533 21.9038 22.1569 21.986 22.0759C22.0682 21.995 22.1657 21.9312 22.2727 21.8883C22.3798 21.8454 22.4943 21.8242 22.6096 21.826H26.0879C26.3162 21.8295 26.534 21.9227 26.6942 22.0854C26.8544 22.2481 26.9442 22.4673 26.9442 22.6956C26.9442 22.924 26.8544 23.1432 26.6942 23.3059C26.534 23.4686 26.3162 23.5617 26.0879 23.5652Z"
      fill="white"
    />
    <path
      d="M33.9128 23.5651H30.4345C30.2062 23.5616 29.9884 23.4685 29.8282 23.3058C29.6679 23.1431 29.5781 22.9239 29.5781 22.6955C29.5781 22.4671 29.6679 22.248 29.8282 22.0853C29.9884 21.9225 30.2062 21.8294 30.4345 21.8259H33.9128C34.1411 21.8294 34.3589 21.9225 34.5191 22.0853C34.6793 22.248 34.7692 22.4671 34.7692 22.6955C34.7692 22.9239 34.6793 23.1431 34.5191 23.3058C34.3589 23.4685 34.1411 23.5616 33.9128 23.5651Z"
      fill="white"
    />
    <path
      d="M39.9993 4.43497V23.5646C39.9993 24.7179 39.5411 25.824 38.7256 26.6395C37.9101 27.4551 36.804 27.9132 35.6507 27.9132H29.156C15.8514 25.4429 5.75595 13.9488 5.56445 0.0863876H35.6515C36.8047 0.0866042 37.9106 0.544852 38.7259 1.36035C39.5412 2.17584 39.9993 3.28179 39.9993 4.43497Z"
      fill="#6C95F8"
    />
    <path
      d="M39.9988 4.43537V13.6266C39.7118 13.6439 39.4163 13.6527 39.1293 13.6527C34.9686 13.6631 30.9115 12.3563 27.539 9.91955C24.1666 7.48278 21.652 4.0411 20.3555 0.0876083H35.651C36.8041 0.0878248 37.9098 0.545961 38.7251 1.36128C39.5404 2.17659 39.9986 3.28234 39.9988 4.43537Z"
      fill="#8FB6F8"
    />
    <path
      d="M13.0434 19.2169C13.0427 19.4473 12.9508 19.6681 12.7879 19.831C12.625 19.994 12.4043 20.0859 12.1738 20.0866H6.08686C5.85853 20.0831 5.64074 19.9899 5.48051 19.8272C5.32028 19.6645 5.23047 19.4453 5.23047 19.217C5.23047 18.9886 5.32028 18.7694 5.48051 18.6067C5.64074 18.444 5.85853 18.3509 6.08686 18.3474H12.1738C12.3272 18.3477 12.4775 18.3901 12.6085 18.4699L12.6171 18.4786C12.774 18.5686 12.8987 18.7055 12.9737 18.87C12.9652 18.8786 12.9737 18.8786 12.9737 18.8786C13.0193 18.9856 13.043 19.1006 13.0434 19.2169Z"
      fill="#C6E5FF"
    />
    <path
      d="M17.3911 22.6959C17.3945 22.743 17.3886 22.7903 17.3738 22.8351C17.3439 23.0392 17.2411 23.2257 17.0845 23.3599C16.9278 23.4942 16.7279 23.5672 16.5216 23.5656H6.08686C5.85853 23.5621 5.64074 23.4689 5.48051 23.3062C5.32028 23.1435 5.23047 22.9243 5.23047 22.696C5.23047 22.4676 5.32028 22.2484 5.48051 22.0857C5.64074 21.923 5.85853 21.8299 6.08686 21.8264H16.5216C16.752 21.8271 16.9728 21.9189 17.1357 22.0818C17.2986 22.2447 17.3904 22.4655 17.3911 22.6959Z"
      fill="#C6E5FF"
    />
    <path
      d="M15.6535 7.04343V12.2615C15.6522 12.7224 15.4685 13.164 15.1426 13.4899C14.8167 13.8157 14.3751 13.9994 13.9143 14.0007H6.95777C6.49693 13.9994 6.05536 13.8157 5.72951 13.4898C5.40367 13.164 5.22004 12.7224 5.21875 12.2615V7.04343C5.21894 6.72539 5.30666 6.41354 5.47231 6.14206C5.63796 5.87057 5.87515 5.64991 6.15788 5.50428C6.40334 5.37112 6.67853 5.30228 6.95777 5.3042H13.9143C14.3751 5.30549 14.8168 5.48915 15.1427 5.81504C15.4686 6.14092 15.6522 6.58255 15.6535 7.04343Z"
      fill="#C6E5FF"
    />
    <path
      d="M12.9739 18.8701C12.8524 18.7396 12.7305 18.6092 12.6172 18.4787C12.7741 18.5687 12.8988 18.7055 12.9739 18.8701Z"
      fill="#FEEC88"
    />
    <path
      d="M15.6519 7.04335V12.2614C15.6505 12.7223 15.4669 13.1639 15.141 13.4898C14.8151 13.8156 14.3735 13.9993 13.9126 14.0007H9.40809C7.87168 11.3629 6.77347 8.49333 6.15625 5.50379C6.40171 5.37063 6.6769 5.3018 6.95614 5.30371H13.9126C14.3736 5.30501 14.8153 5.48872 15.1412 5.81469C15.4671 6.14067 15.6507 6.5824 15.6519 7.04335Z"
      fill="#EAF7FF"
    />
    <path
      d="M17.3909 22.6954C17.3942 22.7425 17.3883 22.7898 17.3735 22.8346C16.9303 22.5129 16.4956 22.1824 16.0781 21.8259H16.5214C16.7517 21.8266 16.9725 21.9184 17.1354 22.0813C17.2983 22.2443 17.3902 22.465 17.3909 22.6954Z"
      fill="#EAF7FF"
    />
  </svg>
);

export const download = (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5 0C11.0523 0 11.5 0.447715 11.5 1V10.5858L14.7929 7.29289C15.1834 6.90237 15.8166 6.90237 16.2071 7.29289C16.5976 7.68342 16.5976 8.31658 16.2071 8.70711L11.2071 13.7071C10.8166 14.0976 10.1834 14.0976 9.79289 13.7071L4.79289 8.70711C4.40237 8.31658 4.40237 7.68342 4.79289 7.29289C5.18342 6.90237 5.81658 6.90237 6.20711 7.29289L9.5 10.5858V1C9.5 0.447715 9.94772 0 10.5 0ZM1.5 12C2.05229 12 2.5 12.4477 2.5 13V14.2C2.5 15.0566 2.50078 15.6389 2.53755 16.089C2.57337 16.5274 2.6383 16.7516 2.71799 16.908C2.90973 17.2843 3.2157 17.5903 3.59202 17.782C3.74842 17.8617 3.97262 17.9266 4.41104 17.9624C4.86113 17.9992 5.44342 18 6.3 18H14.7C15.5566 18 16.1389 17.9992 16.589 17.9624C17.0274 17.9266 17.2516 17.8617 17.408 17.782C17.7843 17.5903 18.0903 17.2843 18.282 16.908C18.3617 16.7516 18.4266 16.5274 18.4624 16.089C18.4992 15.6389 18.5 15.0566 18.5 14.2V13C18.5 12.4477 18.9477 12 19.5 12C20.0523 12 20.5 12.4477 20.5 13V14.2413C20.5 15.0463 20.5 15.7106 20.4558 16.2518C20.4099 16.8139 20.3113 17.3306 20.064 17.816C19.6805 18.5686 19.0686 19.1805 18.316 19.564C17.8306 19.8113 17.3139 19.9099 16.7518 19.9558C16.2106 20 15.5463 20 14.7413 20H6.25868C5.45372 20 4.78936 20 4.24817 19.9558C3.68608 19.9099 3.16937 19.8113 2.68404 19.564C1.93139 19.1805 1.31947 18.5686 0.935975 17.816C0.688684 17.3306 0.59012 16.8139 0.544195 16.2518C0.499977 15.7106 0.499988 15.0463 0.5 14.2413L0.500001 13C0.500001 12.4477 0.947716 12 1.5 12Z"
      fill="#1E40AF"
    />
  </svg>
);

export const back = (
  <svg
    width="18"
    height="14"
    viewBox="0 0 18 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.70711 0.292893C8.09763 0.683417 8.09763 1.31658 7.70711 1.70711L3.41421 6H17C17.5523 6 18 6.44772 18 7C18 7.55228 17.5523 8 17 8H3.41421L7.70711 12.2929C8.09763 12.6834 8.09763 13.3166 7.70711 13.7071C7.31658 14.0976 6.68342 14.0976 6.29289 13.7071L0.292893 7.70711C-0.0976311 7.31658 -0.0976311 6.68342 0.292893 6.29289L6.29289 0.292893C6.68342 -0.0976311 7.31658 -0.0976311 7.70711 0.292893Z"
      fill="#030712"
    />
  </svg>
);

export const spinner = (
  <svg
    aria-hidden="true"
    className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
    viewBox="0 0 100 101"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
      fill="currentColor"
    />
    <path
      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
      fill="currentFill"
    />
  </svg>
);

export const eyeoff = (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.79331 2.29289C3.18383 1.90237 3.817 1.90237 4.20752 2.29289L7.84625 5.93162C7.90455 5.97793 7.95837 6.03139 8.00637 6.09174L18.3974 16.4828C18.4652 16.5347 18.5272 16.5962 18.5813 16.6667L22.2075 20.2929C22.598 20.6834 22.598 21.3166 22.2075 21.7071C21.817 22.0976 21.1838 22.0976 20.7933 21.7071L17.6396 18.5534C16.1947 19.3988 14.472 20 12.5004 20C9.63874 20 7.30576 18.7353 5.57716 17.2926C3.85091 15.8518 2.66738 14.1857 2.07463 13.2472C2.0668 13.2348 2.05876 13.2221 2.05055 13.2092C1.93291 13.024 1.78018 12.7836 1.70298 12.4467C1.64065 12.1748 1.64067 11.825 1.70303 11.553C1.78028 11.2161 1.93344 10.9751 2.05139 10.7895C2.05967 10.7765 2.06777 10.7637 2.07567 10.7512C2.67804 9.79766 3.90132 8.07525 5.69576 6.60955L2.79331 3.70711C2.40278 3.31658 2.40278 2.68342 2.79331 2.29289ZM7.11828 8.03208C5.47911 9.32619 4.33406 10.921 3.76654 11.8194C3.73105 11.8755 3.70617 11.915 3.68537 11.9492C3.67149 11.9721 3.66235 11.9878 3.65625 11.9988C3.65625 11.9992 3.65625 11.9996 3.65625 12C3.65625 12.0004 3.65625 12.0008 3.65625 12.0012C3.66227 12.012 3.67127 12.0276 3.6849 12.05C3.70556 12.084 3.73029 12.1232 3.76563 12.1792C4.2983 13.0226 5.3524 14.4999 6.85869 15.7571C8.36263 17.0123 10.2571 18 12.5004 18C13.8533 18 15.0778 17.6414 16.1674 17.0812L14.5322 15.446C13.9368 15.7977 13.2418 16 12.5004 16C10.2913 16 8.50042 14.2091 8.50042 12C8.50042 11.2586 8.70271 10.5636 9.05438 9.96818L7.11828 8.03208ZM10.568 11.4818C10.5239 11.647 10.5004 11.8207 10.5004 12C10.5004 13.1046 11.3958 14 12.5004 14C12.6798 14 12.8534 13.9765 13.0186 13.9324L10.568 11.4818ZM12.5004 6C12.1186 6 11.7482 6.02849 11.3892 6.08156C10.8429 6.16234 10.3345 5.78492 10.2537 5.23858C10.1729 4.69223 10.5503 4.18385 11.0967 4.10307C11.5505 4.03597 12.0187 4 12.5004 4C15.3621 4 17.6951 5.26472 19.4237 6.70743C21.1499 8.14818 22.3335 9.81429 22.9262 10.7528C22.934 10.7652 22.9421 10.7779 22.9503 10.7908C23.0679 10.976 23.2207 11.2164 23.2979 11.5533C23.3602 11.8253 23.3602 12.1751 23.2978 12.4471C23.2205 12.7841 23.0671 13.0254 22.949 13.2113C22.9407 13.2243 22.9325 13.2371 22.9246 13.2497C22.6063 13.7536 22.1232 14.4581 21.4832 15.2224C21.1287 15.6459 20.498 15.7017 20.0745 15.3472C19.6511 14.9926 19.5952 14.3619 19.9498 13.9385C20.5211 13.2561 20.9524 12.6267 21.2338 12.1814C21.2694 12.1251 21.2943 12.0855 21.3152 12.0512C21.3292 12.0281 21.3384 12.0122 21.3446 12.0012C21.3446 12.0008 21.3446 12.0004 21.3446 12C21.3446 11.9996 21.3446 11.9992 21.3446 11.9988C21.3386 11.988 21.3296 11.9725 21.3159 11.95C21.2953 11.916 21.2705 11.8768 21.2352 11.8208C20.7025 10.9774 19.6484 9.50007 18.1421 8.24291C16.6382 6.9877 14.7437 6 12.5004 6Z"
      fill="#030712"
    />
  </svg>
);

export const eyeon = (
  <svg
    width="23"
    height="16"
    viewBox="0 0 23 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.85868 4.24291C4.35239 5.50007 3.29828 6.97738 2.76562 7.8208C2.73028 7.87676 2.70556 7.91596 2.6849 7.94998C2.67127 7.97243 2.66227 7.98796 2.65625 7.99879C2.65625 7.99919 2.65625 7.99959 2.65625 8C2.65625 8.00041 2.65625 8.00081 2.65625 8.00121C2.66227 8.01204 2.67127 8.02757 2.6849 8.05002C2.70556 8.08404 2.73028 8.12324 2.76562 8.1792C3.29828 9.02262 4.35239 10.4999 5.85868 11.7571C7.36262 13.0123 9.25707 14 11.5004 14C13.7437 14 15.6382 13.0123 17.1421 11.7571C18.6484 10.4999 19.7025 9.02262 20.2352 8.1792C20.2705 8.12324 20.2952 8.08404 20.3159 8.05002C20.3295 8.02757 20.3385 8.01204 20.3446 8.00121C20.3446 8.00081 20.3446 8.0004 20.3446 8C20.3446 7.9996 20.3446 7.99919 20.3446 7.99879C20.3385 7.98796 20.3295 7.97243 20.3159 7.94998C20.2952 7.91596 20.2705 7.87676 20.2352 7.8208C19.7025 6.97738 18.6484 5.50007 17.1421 4.24291C15.6382 2.9877 13.7437 2 11.5004 2C9.25707 2 7.36262 2.9877 5.85868 4.24291ZM4.57715 2.70743C6.30575 1.26472 8.63873 0 11.5004 0C14.3621 0 16.6951 1.26472 18.4237 2.70743C20.1499 4.14818 21.3334 5.81429 21.9262 6.75285C21.934 6.76525 21.9421 6.77791 21.9503 6.79084C22.0679 6.97598 22.2206 7.21636 22.2978 7.55317C22.3601 7.82512 22.3601 8.17488 22.2978 8.44683C22.2206 8.78365 22.0679 9.02403 21.9503 9.20917C21.9421 9.22209 21.934 9.23475 21.9262 9.24715C21.3334 10.1857 20.1499 11.8518 18.4237 13.2926C16.6951 14.7353 14.3621 16 11.5004 16C8.63873 16 6.30575 14.7353 4.57715 13.2926C2.8509 11.8518 1.66737 10.1857 1.07462 9.24715C1.06679 9.23475 1.05875 9.2221 1.05054 9.20917C0.932917 9.02403 0.780202 8.78365 0.703001 8.44683C0.640666 8.17488 0.640666 7.82512 0.703001 7.55317C0.780202 7.21635 0.932917 6.97597 1.05054 6.79083C1.05875 6.7779 1.06679 6.76525 1.07462 6.75285C1.66737 5.81429 2.8509 4.14818 4.57715 2.70743ZM11.5004 6C10.3958 6 9.5004 6.89543 9.5004 8C9.5004 9.10457 10.3958 10 11.5004 10C12.605 10 13.5004 9.10457 13.5004 8C13.5004 6.89543 12.605 6 11.5004 6ZM7.5004 8C7.5004 5.79086 9.29126 4 11.5004 4C13.7095 4 15.5004 5.79086 15.5004 8C15.5004 10.2091 13.7095 12 11.5004 12C9.29126 12 7.5004 10.2091 7.5004 8Z"
      fill="#030712"
    />
  </svg>
);

export const diamond = (
  <svg
    width="34"
    height="25"
    viewBox="0 0 34 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M33.5082 7.32879C33.9039 8.25205 33.7078 9.3225 33.0106 10.0455C30.5578 12.5892 24.7268 18.6361 20.9019 22.6027C19.9607 23.5787 18.6631 24.1301 17.3072 24.1301C15.9513 24.1301 14.6536 23.5787 13.7125 22.6027C9.88756 18.6361 4.05666 12.5892 1.60385 10.0455C0.906658 9.3225 0.710591 8.25205 1.10621 7.32879C1.79239 5.72766 2.86542 3.22396 3.55003 1.62665C3.94351 0.708562 4.84621 0.113281 5.84497 0.113281H28.7694C29.7682 0.113281 30.6709 0.708562 31.0644 1.62665C31.7489 3.22396 32.8219 5.72766 33.5082 7.32879Z"
      fill="url(#paint0_linear_9907_14129)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M33.7086 8.22769C33.7309 8.89061 33.4884 9.54949 33.0105 10.0451C30.5577 12.5888 24.7267 18.6357 20.9019 22.6023C19.9607 23.5783 18.663 24.1297 17.3072 24.1297C15.9512 24.1297 14.6536 23.5783 13.7125 22.6023C9.88751 18.6357 4.05661 12.5888 1.6038 10.0451C1.12583 9.54949 0.883355 8.89061 0.905715 8.22769H33.7086Z"
      fill="url(#paint1_linear_9907_14129)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M25.784 7.93766C25.9523 8.52676 25.898 9.15733 25.6317 9.70912C24.5113 12.03 21.3616 18.5542 19.2738 22.8792C18.9092 23.6343 18.1447 24.1142 17.3062 24.1142C16.4677 24.1142 15.7032 23.6343 15.3387 22.8792C13.2507 18.5542 10.1012 12.03 8.98074 9.70912C8.71433 9.15733 8.66017 8.52676 8.82849 7.93766C9.46883 5.69654 11.064 0.113281 11.064 0.113281H23.5484C23.5484 0.113281 25.1437 5.69654 25.784 7.93766Z"
      fill="url(#paint2_linear_9907_14129)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_9907_14129"
        x1="17.9314"
        y1="0.113281"
        x2="17.9314"
        y2="12.5977"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FFBD56" />
        <stop offset="1" stopColor="#FF781B" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_9907_14129"
        x1="20.4283"
        y1="6.35511"
        x2="19.1799"
        y2="22.5848"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FFBD56" />
        <stop offset="1" stopColor="#FF781B" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_9907_14129"
        x1="17.3062"
        y1="3.2344"
        x2="17.3062"
        y2="23.2095"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#F8E759" />
        <stop offset="1" stopColor="#FBAC05" />
      </linearGradient>
    </defs>
  </svg>
);

export const fromTo = (
  <svg
    width="24"
    height="84"
    viewBox="0 0 24 84"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="4" fill="#030712" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13 63C13 63.5523 12.5523 64 12 64C11.4477 64 11 63.5523 11 63L11 21C11 20.4477 11.4477 20 12 20C12.5523 20 13 20.4477 13 21L13 63Z"
      fill="black"
    />
    <circle cx="12" cy="72" r="4" fill="#030712" />
  </svg>
);

export const logout = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.7587 4.31292e-07L7 9.08129e-07C7.55229 9.08129e-07 8 0.447716 8 1C8 1.55229 7.55229 2 7 2H5.8C4.94342 2 4.36113 2.00078 3.91104 2.03755C3.47262 2.07337 3.24842 2.1383 3.09202 2.21799C2.7157 2.40973 2.40974 2.7157 2.21799 3.09202C2.1383 3.24842 2.07337 3.47262 2.03755 3.91104C2.00078 4.36113 2 4.94342 2 5.8V14.2C2 15.0566 2.00078 15.6389 2.03755 16.089C2.07337 16.5274 2.1383 16.7516 2.21799 16.908C2.40973 17.2843 2.7157 17.5903 3.09202 17.782C3.24842 17.8617 3.47262 17.9266 3.91104 17.9624C4.36113 17.9992 4.94342 18 5.8 18H7C7.55229 18 8 18.4477 8 19C8 19.5523 7.55229 20 7 20H5.75868C4.95372 20 4.28936 20 3.74818 19.9558C3.18608 19.9099 2.66937 19.8113 2.18404 19.564C1.43139 19.1805 0.819468 18.5686 0.435975 17.816C0.188684 17.3306 0.0901197 16.8139 0.0441946 16.2518C-2.28137e-05 15.7106 -1.23241e-05 15.0463 4.31291e-07 14.2413V5.7587C-1.23241e-05 4.95373 -2.28137e-05 4.28937 0.0441947 3.74817C0.09012 3.18608 0.188685 2.66937 0.435976 2.18404C0.81947 1.43139 1.43139 0.819468 2.18404 0.435975C2.66937 0.188684 3.18608 0.0901197 3.74818 0.0441945C4.28937 -2.28137e-05 4.95373 -1.23241e-05 5.7587 4.31292e-07ZM13.2929 4.29289C13.6834 3.90237 14.3166 3.90237 14.7071 4.29289L19.7071 9.29289C20.0976 9.68342 20.0976 10.3166 19.7071 10.7071L14.7071 15.7071C14.3166 16.0976 13.6834 16.0976 13.2929 15.7071C12.9024 15.3166 12.9024 14.6834 13.2929 14.2929L16.5858 11H7C6.44772 11 6 10.5523 6 10C6 9.44772 6.44772 9 7 9H16.5858L13.2929 5.70711C12.9024 5.31658 12.9024 4.68342 13.2929 4.29289Z"
      fill="#B91C1C"
    />
  </svg>
);

export const done = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.833984 10C0.833984 4.9374 4.93804 0.833344 10.0007 0.833344C15.0633 0.833344 19.1673 4.9374 19.1673 10C19.1673 15.0626 15.0633 19.1667 10.0007 19.1667C4.93804 19.1667 0.833984 15.0626 0.833984 10ZM14.3399 6.91075C14.6653 7.23619 14.6653 7.76383 14.3399 8.08927L9.33991 13.0893C9.01447 13.4147 8.48683 13.4147 8.1614 13.0893L5.6614 10.5893C5.33596 10.2638 5.33596 9.73619 5.6614 9.41076C5.98683 9.08532 6.51447 9.08532 6.83991 9.41076L8.75065 11.3215L13.1614 6.91075C13.4868 6.58532 14.0145 6.58532 14.3399 6.91075Z"
      fill="#15803D"
    />
  </svg>
);

export const savedIcon = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.6667 5L7.50004 14.1667L3.33337 10"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const twoStepAuth = (
  <svg
    width="18"
    height="22"
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 13.5V7.49999M6 10.5H12M17 11C17 15.9084 11.646 19.4784 9.69799 20.6149C9.4766 20.744 9.3659 20.8086 9.20968 20.8421C9.08844 20.8681 8.91156 20.8681 8.79032 20.8421C8.6341 20.8086 8.5234 20.744 8.30201 20.6149C6.35396 19.4784 1 15.9084 1 11V6.21759C1 5.41808 1 5.01833 1.13076 4.6747C1.24627 4.37113 1.43398 4.10027 1.67766 3.88552C1.9535 3.64243 2.3278 3.50207 3.0764 3.22134L8.4382 1.21067C8.6461 1.13271 8.75005 1.09373 8.85698 1.07827C8.95184 1.06457 9.04816 1.06457 9.14302 1.07827C9.24995 1.09373 9.3539 1.13271 9.5618 1.21067L14.9236 3.22134C15.6722 3.50207 16.0465 3.64243 16.3223 3.88552C16.566 4.10027 16.7537 4.37113 16.8692 4.6747C17 5.01833 17 5.41808 17 6.21759V11Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const email = (
  <svg
    width="22"
    height="18"
    viewBox="0 0 22 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 4L9.16492 9.71544C9.82609 10.1783 10.1567 10.4097 10.5163 10.4993C10.8339 10.5785 11.1661 10.5785 11.4837 10.4993C11.8433 10.4097 12.1739 10.1783 12.8351 9.71544L21 4M5.8 17H16.2C17.8802 17 18.7202 17 19.362 16.673C19.9265 16.3854 20.3854 15.9265 20.673 15.362C21 14.7202 21 13.8802 21 12.2V5.8C21 4.11984 21 3.27976 20.673 2.63803C20.3854 2.07354 19.9265 1.6146 19.362 1.32698C18.7202 1 17.8802 1 16.2 1H5.8C4.11984 1 3.27976 1 2.63803 1.32698C2.07354 1.6146 1.6146 2.07354 1.32698 2.63803C1 3.27976 1 4.11984 1 5.8V12.2C1 13.8802 1 14.7202 1.32698 15.362C1.6146 15.9265 2.07354 16.3854 2.63803 16.673C3.27976 17 4.11984 17 5.8 17Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const password = (
  <svg
    width="22"
    height="12"
    viewBox="0 0 22 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 6H11.005M16 6H16.005M6 6H6.005M4.2 1H17.8C18.9201 1 19.4802 1 19.908 1.21799C20.2843 1.40973 20.5903 1.71569 20.782 2.09202C21 2.51984 21 3.0799 21 4.2V7.8C21 8.9201 21 9.48016 20.782 9.90798C20.5903 10.2843 20.2843 10.5903 19.908 10.782C19.4802 11 18.9201 11 17.8 11H4.2C3.0799 11 2.51984 11 2.09202 10.782C1.71569 10.5903 1.40973 10.2843 1.21799 9.90798C1 9.48016 1 8.9201 1 7.8V4.2C1 3.0799 1 2.51984 1.21799 2.09202C1.40973 1.71569 1.71569 1.40973 2.09202 1.21799C2.51984 1 3.0799 1 4.2 1ZM11.25 6C11.25 6.13807 11.1381 6.25 11 6.25C10.8619 6.25 10.75 6.13807 10.75 6C10.75 5.86193 10.8619 5.75 11 5.75C11.1381 5.75 11.25 5.86193 11.25 6ZM16.25 6C16.25 6.13807 16.1381 6.25 16 6.25C15.8619 6.25 15.75 6.13807 15.75 6C15.75 5.86193 15.8619 5.75 16 5.75C16.1381 5.75 16.25 5.86193 16.25 6ZM6.25 6C6.25 6.13807 6.13807 6.25 6 6.25C5.86193 6.25 5.75 6.13807 5.75 6C5.75 5.86193 5.86193 5.75 6 5.75C6.13807 5.75 6.25 5.86193 6.25 6Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const recoveryPhone = (
  <svg
    width="16"
    height="22"
    viewBox="0 0 16 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 16.5H8.01M4.2 21H11.8C12.9201 21 13.4802 21 13.908 20.782C14.2843 20.5903 14.5903 20.2843 14.782 19.908C15 19.4802 15 18.9201 15 17.8V4.2C15 3.07989 15 2.51984 14.782 2.09202C14.5903 1.71569 14.2843 1.40973 13.908 1.21799C13.4802 1 12.9201 1 11.8 1H4.2C3.0799 1 2.51984 1 2.09202 1.21799C1.71569 1.40973 1.40973 1.71569 1.21799 2.09202C1 2.51984 1 3.0799 1 4.2V17.8C1 18.9201 1 19.4802 1.21799 19.908C1.40973 20.2843 1.71569 20.5903 2.09202 20.782C2.51984 21 3.07989 21 4.2 21ZM8.5 16.5C8.5 16.7761 8.27614 17 8 17C7.72386 17 7.5 16.7761 7.5 16.5C7.5 16.2239 7.72386 16 8 16C8.27614 16 8.5 16.2239 8.5 16.5Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const detailing = (
  <svg
    width="8"
    height="14"
    viewBox="0 0 8 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L7.70711 6.29289C8.09763 6.68342 8.09763 7.31658 7.70711 7.70711L1.70711 13.7071C1.31658 14.0976 0.683417 14.0976 0.292893 13.7071C-0.0976311 13.3166 -0.0976311 12.6834 0.292893 12.2929L5.58579 7L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"
      fill="#030712"
    />
  </svg>
);

export const authenticator = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.56811 2.50544e-06C2.57873 3.81674e-06 2.58936 5.12805e-06 2.60001 5.12805e-06H6.40001C6.41065 5.12805e-06 6.42128 3.81674e-06 6.4319 2.50544e-06C6.68429 -2.74161e-05 6.93008 -5.6861e-05 7.13824 0.0169504C7.36683 0.0356272 7.63656 0.0796948 7.90799 0.217992C8.28431 0.409739 8.59027 0.7157 8.78202 1.09202C8.92031 1.36345 8.96438 1.63318 8.98306 1.86178C9.00007 2.06994 9.00004 2.31574 9.00001 2.56812V6.43189C9.00004 6.68427 9.00007 6.93007 8.98306 7.13824C8.96438 7.36683 8.92031 7.63656 8.78202 7.90799C8.59027 8.28431 8.28431 8.59027 7.90799 8.78202C7.63656 8.92031 7.36683 8.96438 7.13824 8.98306C6.93007 9.00007 6.68427 9.00004 6.43189 9.00001H2.56812C2.31574 9.00004 2.06994 9.00007 1.86178 8.98306C1.63318 8.96438 1.36345 8.92031 1.09202 8.78202C0.7157 8.59027 0.409739 8.28431 0.217992 7.90799C0.0796948 7.63656 0.0356272 7.36683 0.0169504 7.13824C-5.6861e-05 6.93008 -2.74161e-05 6.68429 2.50544e-06 6.4319C3.81674e-06 6.42128 5.12805e-06 6.41065 5.12805e-06 6.40001V2.60001C5.12805e-06 2.58936 3.81674e-06 2.57873 2.50544e-06 2.56811C-2.74161e-05 2.31572 -5.6861e-05 2.06993 0.0169504 1.86178C0.0356272 1.63318 0.0796947 1.36345 0.217992 1.09202C0.409739 0.7157 0.7157 0.409739 1.09202 0.217992C1.36345 0.0796947 1.63318 0.0356272 1.86178 0.0169504C2.06993 -5.6861e-05 2.31572 -2.74161e-05 2.56811 2.50544e-06ZM2.01145 2.01145C2.01107 2.0156 2.01069 2.02 2.01031 2.02464C2.00078 2.14122 2.00001 2.30348 2.00001 2.60001V6.40001C2.00001 6.69653 2.00078 6.85879 2.01031 6.97537C2.01069 6.98001 2.01107 6.98441 2.01145 6.98856C2.0156 6.98894 2.02 6.98932 2.02464 6.9897C2.14122 6.99923 2.30348 7.00001 2.60001 7.00001H6.40001C6.69653 7.00001 6.85879 6.99923 6.97537 6.9897C6.98001 6.98932 6.98441 6.98894 6.98856 6.98856C6.98894 6.98441 6.98932 6.98001 6.9897 6.97537C6.99923 6.85879 7.00001 6.69653 7.00001 6.40001V2.60001C7.00001 2.30348 6.99923 2.14122 6.9897 2.02464C6.98932 2.02 6.98894 2.0156 6.98856 2.01145C6.98441 2.01107 6.98001 2.01069 6.97537 2.01031C6.85879 2.00078 6.69653 2.00001 6.40001 2.00001H2.60001C2.30348 2.00001 2.14122 2.00078 2.02464 2.01031C2.02 2.01069 2.0156 2.01107 2.01145 2.01145ZM13.5681 2.50544e-06H17.4319C17.6843 -2.74161e-05 17.9301 -5.6861e-05 18.1382 0.0169504C18.3668 0.0356271 18.6366 0.0796946 18.908 0.217992C19.2843 0.409739 19.5903 0.715701 19.782 1.09202C19.9203 1.36345 19.9644 1.63318 19.9831 1.86178C20.0001 2.06993 20 2.31571 20 2.56809V6.43192C20 6.6843 20.0001 6.93008 19.9831 7.13824C19.9644 7.36683 19.9203 7.63656 19.782 7.90799C19.5903 8.28431 19.2843 8.59027 18.908 8.78202C18.6366 8.92031 18.3668 8.96438 18.1382 8.98306C17.9301 9.00007 17.6843 9.00004 17.4319 9.00001H13.5681C13.3157 9.00004 13.0699 9.00007 12.8618 8.98306C12.6332 8.96438 12.3634 8.92031 12.092 8.78202C11.7157 8.59027 11.4097 8.28431 11.218 7.90799C11.0797 7.63656 11.0356 7.36683 11.0169 7.13824C10.9999 6.93007 11 6.68428 11 6.43189V2.56812C11 2.31574 10.9999 2.06994 11.0169 1.86178C11.0356 1.63318 11.0797 1.36345 11.218 1.09202C11.4097 0.715701 11.7157 0.409739 12.092 0.217992C12.3634 0.0796946 12.6332 0.0356271 12.8618 0.0169504C13.0699 -5.6861e-05 13.3157 -2.74161e-05 13.5681 2.50544e-06ZM13.0115 2.01145C13.0111 2.0156 13.0107 2.02 13.0103 2.02464C13.0008 2.14122 13 2.30348 13 2.60001V6.40001C13 6.69653 13.0008 6.85879 13.0103 6.97537C13.0107 6.98001 13.0111 6.98441 13.0115 6.98856C13.0156 6.98894 13.02 6.98932 13.0246 6.9897C13.1412 6.99923 13.3035 7.00001 13.6 7.00001H17.4C17.6965 7.00001 17.8588 6.99923 17.9754 6.9897C17.98 6.98932 17.9844 6.98893 17.9886 6.98855C17.9889 6.9844 17.9893 6.98001 17.9897 6.97537C17.9992 6.85879 18 6.69653 18 6.40001V2.60001C18 2.30348 17.9992 2.14122 17.9897 2.02464C17.9893 2.02 17.9889 2.0156 17.9885 2.01145C17.9844 2.01107 17.98 2.01069 17.9754 2.01031C17.8588 2.00078 17.6965 2.00001 17.4 2.00001H13.6C13.3035 2.00001 13.1412 2.00078 13.0246 2.01031C13.02 2.01069 13.0156 2.01107 13.0115 2.01145ZM3.50001 4.50001C3.50001 3.94772 3.94772 3.50001 4.50001 3.50001H4.51001C5.06229 3.50001 5.51001 3.94772 5.51001 4.50001C5.51001 5.05229 5.06229 5.50001 4.51001 5.50001H4.50001C3.94772 5.50001 3.50001 5.05229 3.50001 4.50001ZM14.5 4.50001C14.5 3.94772 14.9477 3.50001 15.5 3.50001H15.51C16.0623 3.50001 16.51 3.94772 16.51 4.50001C16.51 5.05229 16.0623 5.50001 15.51 5.50001H15.5C14.9477 5.50001 14.5 5.05229 14.5 4.50001ZM10 11C10 10.4477 10.4477 10 11 10H11.01C11.5623 10 12.01 10.4477 12.01 11C12.01 11.5523 11.5623 12 11.01 12H11C10.4477 12 10 11.5523 10 11ZM2.56812 11H6.43189C6.68428 11 6.93007 10.9999 7.13824 11.0169C7.36683 11.0356 7.63656 11.0797 7.90799 11.218C8.28431 11.4097 8.59027 11.7157 8.78202 12.092C8.92031 12.3634 8.96438 12.6332 8.98306 12.8618C9.00007 13.0699 9.00004 13.3157 9.00001 13.5681V17.4319C9.00004 17.6843 9.00007 17.9301 8.98306 18.1382C8.96438 18.3668 8.92031 18.6366 8.78202 18.908C8.59027 19.2843 8.28431 19.5903 7.90799 19.782C7.63656 19.9203 7.36683 19.9644 7.13824 19.9831C6.93008 20.0001 6.6843 20 6.43192 20H2.56809C2.31571 20 2.06993 20.0001 1.86178 19.9831C1.63318 19.9644 1.36345 19.9203 1.09202 19.782C0.715701 19.5903 0.409739 19.2843 0.217992 18.908C0.0796946 18.6366 0.0356271 18.3668 0.0169504 18.1382C-5.6861e-05 17.9301 -2.74161e-05 17.6843 2.50544e-06 17.4319V13.5681C-2.74161e-05 13.3157 -5.6861e-05 13.0699 0.0169504 12.8618C0.0356271 12.6332 0.0796946 12.3634 0.217992 12.092C0.409739 11.7157 0.715701 11.4097 1.09202 11.218C1.36345 11.0797 1.63318 11.0356 1.86178 11.0169C2.06994 10.9999 2.31574 11 2.56812 11ZM2.01145 13.0115C2.01107 13.0156 2.01069 13.02 2.01031 13.0246C2.00078 13.1412 2.00001 13.3035 2.00001 13.6V17.4C2.00001 17.6965 2.00078 17.8588 2.01031 17.9754C2.01069 17.98 2.01107 17.9844 2.01145 17.9885C2.0156 17.9889 2.02 17.9893 2.02464 17.9897C2.14122 17.9992 2.30348 18 2.60001 18H6.40001C6.69653 18 6.85879 17.9992 6.97537 17.9897C6.98001 17.9893 6.98441 17.9889 6.98856 17.9885C6.98894 17.9844 6.98932 17.98 6.9897 17.9754C6.99923 17.8588 7.00001 17.6965 7.00001 17.4V13.6C7.00001 13.3035 6.99923 13.1412 6.9897 13.0246C6.98932 13.02 6.98894 13.0156 6.98856 13.0115C6.98441 13.0111 6.98001 13.0107 6.97537 13.0103C6.85879 13.0008 6.69653 13 6.40001 13H2.60001C2.30348 13 2.14122 13.0008 2.02464 13.0103C2.02 13.0107 2.0156 13.0111 2.01145 13.0115ZM13.5 12C13.5 11.4477 13.9477 11 14.5 11H19C19.5523 11 20 11.4477 20 12C20 12.5523 19.5523 13 19 13H14.5C13.9477 13 13.5 12.5523 13.5 12ZM12 13.5C12.5523 13.5 13 13.9477 13 14.5V19C13 19.5523 12.5523 20 12 20C11.4477 20 11 19.5523 11 19V14.5C11 13.9477 11.4477 13.5 12 13.5ZM19 14C19.5523 14 20 14.4477 20 15V19C20 19.5523 19.5523 20 19 20H15C14.4477 20 14 19.5523 14 19C14 18.4477 14.4477 18 15 18H18V15C18 14.4477 18.4477 14 19 14ZM3.50001 15.5C3.50001 14.9477 3.94772 14.5 4.50001 14.5H4.51001C5.06229 14.5 5.51001 14.9477 5.51001 15.5C5.51001 16.0523 5.06229 16.5 4.51001 16.5H4.50001C3.94772 16.5 3.50001 16.0523 3.50001 15.5ZM14.5 15.5C14.5 14.9477 14.9477 14.5 15.5 14.5H15.51C16.0623 14.5 16.51 14.9477 16.51 15.5C16.51 16.0523 16.0623 16.5 15.51 16.5H15.5C14.9477 16.5 14.5 16.0523 14.5 15.5Z"
      fill="#030712"
    />
  </svg>
);

export const warning = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.832031 9.99992C0.832031 4.93731 4.93609 0.833252 9.9987 0.833252C15.0613 0.833252 19.1654 4.93731 19.1654 9.99992C19.1654 15.0625 15.0613 19.1666 9.9987 19.1666C4.93609 19.1666 0.832031 15.0625 0.832031 9.99992ZM9.16536 13.3333C9.16536 12.873 9.53846 12.4999 9.9987 12.4999H10.007C10.4673 12.4999 10.8404 12.873 10.8404 13.3333C10.8404 13.7935 10.4673 14.1666 10.007 14.1666H9.9987C9.53846 14.1666 9.16536 13.7935 9.16536 13.3333ZM9.9987 5.83325C10.4589 5.83325 10.832 6.20635 10.832 6.66658V9.99992C10.832 10.4602 10.4589 10.8333 9.9987 10.8333C9.53846 10.8333 9.16536 10.4602 9.16536 9.99992V6.66658C9.16536 6.20635 9.53846 5.83325 9.9987 5.83325Z"
      fill="#EA580C"
    />
  </svg>
);

export const stock = (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.5 6.27774L9.99997 11M9.99997 11L1.49997 6.27774M9.99997 11L10 20.5M19 15.0585V6.94144C19 6.5988 19 6.42747 18.9495 6.27468C18.9049 6.1395 18.8318 6.01542 18.7354 5.91073C18.6263 5.79239 18.4766 5.70919 18.177 5.54279L10.777 1.43168C10.4934 1.27412 10.3516 1.19534 10.2015 1.16445C10.0685 1.13712 9.93146 1.13712 9.79855 1.16445C9.64838 1.19534 9.50658 1.27412 9.22297 1.43168L1.82297 5.54279C1.52345 5.70919 1.37369 5.79239 1.26463 5.91073C1.16816 6.01542 1.09515 6.1395 1.05048 6.27468C1 6.42748 1 6.5988 1 6.94144V15.0585C1 15.4012 1 15.5725 1.05048 15.7253C1.09515 15.8605 1.16816 15.9846 1.26463 16.0893C1.37369 16.2076 1.52345 16.2908 1.82297 16.4572L9.22297 20.5683C9.50658 20.7259 9.64838 20.8046 9.79855 20.8355C9.93146 20.8629 10.0685 20.8629 10.2015 20.8355C10.3516 20.8046 10.4934 20.7259 10.777 20.5683L18.177 16.4572C18.4766 16.2908 18.6263 16.2076 18.7354 16.0893C18.8318 15.9846 18.9049 15.8605 18.9495 15.7253C19 15.5725 19 15.4012 19 15.0585Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const browser = (
  <svg
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7 19H15M11 15V19M5.8 15H16.2C17.8802 15 18.7202 15 19.362 14.673C19.9265 14.3854 20.3854 13.9265 20.673 13.362C21 12.7202 21 11.8802 21 10.2V5.8C21 4.11984 21 3.27976 20.673 2.63803C20.3854 2.07354 19.9265 1.6146 19.362 1.32698C18.7202 1 17.8802 1 16.2 1H5.8C4.11984 1 3.27976 1 2.63803 1.32698C2.07354 1.6146 1.6146 2.07354 1.32698 2.63803C1 3.27976 1 4.11984 1 5.8V10.2C1 11.8802 1 12.7202 1.32698 13.362C1.6146 13.9265 2.07354 14.3854 2.63803 14.673C3.27976 15 4.11984 15 5.8 15Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const expand = (
  <svg
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L7 5.58579L12.2929 0.292893C12.6834 -0.0976311 13.3166 -0.0976311 13.7071 0.292893C14.0976 0.683417 14.0976 1.31658 13.7071 1.70711L7.70711 7.70711C7.31658 8.09763 6.68342 8.09763 6.29289 7.70711L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"
      fill="#030712"
    />
  </svg>
);

export const collapse = (
  <svg
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.29289 0.292893C6.68342 -0.0976311 7.31658 -0.0976311 7.70711 0.292893L13.7071 6.29289C14.0976 6.68342 14.0976 7.31658 13.7071 7.70711C13.3166 8.09763 12.6834 8.09763 12.2929 7.70711L7 2.41421L1.70711 7.70711C1.31658 8.09763 0.683417 8.09763 0.292893 7.70711C-0.0976311 7.31658 -0.0976311 6.68342 0.292893 6.29289L6.29289 0.292893Z"
      fill="#030712"
    />
  </svg>
);

export const chrome = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 12C22 17.522 17.522 22 12 22C6.478 22 2 17.522 2 12C2 6.478 6.478 2 12 2C17.522 2 22 6.478 22 12Z"
      fill="#4CAF50"
    />
    <path
      d="M11.9996 2V12L15.9996 14L11.5781 22C11.7366 22 11.8411 22 11.9996 22C17.5261 22 21.9996 17.5265 21.9996 12C21.9996 6.4735 17.5261 2 11.9996 2Z"
      fill="#FFC107"
    />
    <path
      d="M22 12C22 17.522 17.522 22 12 22C6.478 22 2 17.522 2 12C2 6.478 6.478 2 12 2C17.522 2 22 6.478 22 12Z"
      fill="#4CAF50"
    />
    <path
      d="M11.9996 2V12L15.9996 14L11.5781 22C11.7366 22 11.8411 22 11.9996 22C17.5261 22 21.9996 17.5265 21.9996 12C21.9996 6.4735 17.5261 2 11.9996 2Z"
      fill="#FFC107"
    />
    <path
      d="M20.9203 7.5H12.0003V14L10.5003 13.5L3.58031 6.63H3.57031C5.34031 3.845 8.45531 2 12.0003 2C15.9003 2 19.2753 4.24 20.9203 7.5Z"
      fill="#F44336"
    />
    <path
      d="M3.57812 6.63196L7.99962 14.063L10.4991 13.5L3.57812 6.63196Z"
      fill="#DD2C00"
    />
    <path
      d="M11.5781 22L16.0451 13.9705L13.9996 12.5L11.5781 22Z"
      fill="#558B2F"
    />
    <path d="M20.9329 7.5H12.0004L11.2109 9.79L20.9329 7.5Z" fill="#F9A825" />
    <path
      d="M16.5 12C16.5 14.4845 14.4845 16.5 12 16.5C9.5155 16.5 7.5 14.4845 7.5 12C7.5 9.5155 9.5155 7.5 12 7.5C14.4845 7.5 16.5 9.5155 16.5 12Z"
      fill="white"
    />
    <path
      d="M15.5 12C15.5 13.9335 13.9335 15.5 12 15.5C10.0665 15.5 8.5 13.9335 8.5 12C8.5 10.0665 10.0665 8.5 12 8.5C13.9335 8.5 15.5 10.0665 15.5 12Z"
      fill="#2196F3"
    />
  </svg>
);

export const dropdown = (
  <svg
    width="10"
    height="6"
    viewBox="0 0 10 6"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M-2.18557e-07 0.5L5 5.5L10 0.5L-2.18557e-07 0.5Z" fill="#475569" />
  </svg>
);

export const sorting = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 4V20M17 20L13 16M17 20L21 16M7 20V4M7 4L3 8M7 4L11 8"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const flex = (color) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2 6C2 4.89543 2.89543 4 4 4C5.10457 4 6 4.89543 6 6C6 7.10457 5.10457 8 4 8C2.89543 8 2 7.10457 2 6ZM8 6C8 5.44772 8.44772 5 9 5H21C21.5523 5 22 5.44772 22 6C22 6.55229 21.5523 7 21 7L9 7C8.44772 7 8 6.55228 8 6ZM2 12C2 10.8954 2.89543 10 4 10C5.10457 10 6 10.8954 6 12C6 13.1046 5.10457 14 4 14C2.89543 14 2 13.1046 2 12ZM8 12C8 11.4477 8.44772 11 9 11L21 11C21.5523 11 22 11.4477 22 12C22 12.5523 21.5523 13 21 13L9 13C8.44772 13 8 12.5523 8 12ZM2 18C2 16.8954 2.89543 16 4 16C5.10457 16 6 16.8954 6 18C6 19.1046 5.10457 20 4 20C2.89543 20 2 19.1046 2 18ZM8 18C8 17.4477 8.44772 17 9 17L21 17C21.5523 17 22 17.4477 22 18C22 18.5523 21.5523 19 21 19L9 19C8.44772 19 8 18.5523 8 18Z"
      fill={color}
    />
  </svg>
);

export const grid = (color) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7587 2H16.2413C17.0463 1.99999 17.7106 1.99998 18.2518 2.04419C18.8139 2.09012 19.3306 2.18868 19.816 2.43598C20.5686 2.81947 21.1805 3.43139 21.564 4.18404C21.8113 4.66937 21.9099 5.18608 21.9558 5.74817C22 6.28936 22 6.95372 22 7.75868V16.2413C22 17.0463 22 17.7106 21.9558 18.2518C21.9099 18.8139 21.8113 19.3306 21.564 19.816C21.1805 20.5686 20.5686 21.1805 19.816 21.564C19.3306 21.8113 18.8139 21.9099 18.2518 21.9558C17.7106 22 17.0463 22 16.2413 22H7.75868C6.95372 22 6.28936 22 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43598 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413V7.7587C1.99999 6.95373 1.99998 6.28937 2.04419 5.74817C2.09012 5.18608 2.18868 4.66937 2.43597 4.18404C2.81947 3.43139 3.43139 2.81947 4.18404 2.43597C4.66937 2.18868 5.18608 2.09012 5.74817 2.04419C6.28937 1.99998 6.95373 1.99999 7.7587 2ZM4 13V16.2C4 17.0566 4.00078 17.6389 4.03755 18.089C4.07337 18.5274 4.1383 18.7516 4.21799 18.908C4.40973 19.2843 4.7157 19.5903 5.09202 19.782C5.24842 19.8617 5.47262 19.9266 5.91104 19.9624C6.36113 19.9992 6.94342 20 7.8 20H11V13H4ZM11 11H4V7.8C4 6.94342 4.00078 6.36113 4.03755 5.91104C4.07337 5.47262 4.1383 5.24842 4.21799 5.09202C4.40973 4.7157 4.7157 4.40973 5.09202 4.21799C5.24842 4.1383 5.47262 4.07337 5.91104 4.03755C6.36113 4.00078 6.94342 4 7.8 4H11V11ZM13 13V20H16.2C17.0566 20 17.6389 19.9992 18.089 19.9624C18.5274 19.9266 18.7516 19.8617 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C19.8617 18.7516 19.9266 18.5274 19.9624 18.089C19.9992 17.6389 20 17.0566 20 16.2V13H13ZM20 11H13V4H16.2C17.0566 4 17.6389 4.00078 18.089 4.03755C18.5274 4.07337 18.7516 4.1383 18.908 4.21799C19.2843 4.40973 19.5903 4.7157 19.782 5.09202C19.8617 5.24842 19.9266 5.47262 19.9624 5.91104C19.9992 6.36113 20 6.94342 20 7.8V11Z"
      fill={color}
    />
  </svg>
);

export const add = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 0C8.55228 0 9 0.447715 9 1V7H15C15.5523 7 16 7.44772 16 8C16 8.55228 15.5523 9 15 9H9V15C9 15.5523 8.55228 16 8 16C7.44772 16 7 15.5523 7 15V9H1C0.447715 9 0 8.55228 0 8C0 7.44772 0.447715 7 1 7H7V1C7 0.447715 7.44772 0 8 0Z"
      fill="#030712"
    />
  </svg>
);

export const search = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2ZM0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 9.84869 15.3729 11.5509 14.3199 12.9056L19.7071 18.2929C20.0976 18.6834 20.0976 19.3166 19.7071 19.7071C19.3166 20.0976 18.6834 20.0976 18.2929 19.7071L12.9057 14.3198C11.551 15.3729 9.84873 16 8 16C3.58172 16 0 12.4183 0 8Z"
      fill="#030712"
    />
  </svg>
);

export const filter = (
  <svg
    width="22"
    height="20"
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.56809 2.50544e-06C2.57871 3.81674e-06 2.58934 5.12805e-06 2.59999 5.12805e-06L19.4319 2.50544e-06C19.6843 -2.74161e-05 19.9301 -5.6861e-05 20.1382 0.0169504C20.3668 0.0356272 20.6365 0.0796947 20.908 0.217992C21.2843 0.409739 21.5903 0.715701 21.782 1.09202C21.9203 1.36345 21.9644 1.63318 21.983 1.86178C22 2.06993 22 2.31571 22 2.56809L22 3.2694C22 3.28764 22 3.30639 22.0001 3.32559C22.0007 3.53391 22.0015 3.79616 21.9343 4.05176C21.8761 4.27325 21.7803 4.4831 21.651 4.67216C21.5018 4.89032 21.3032 5.0615 21.1453 5.19747C21.1308 5.21001 21.1166 5.22224 21.1028 5.23419L14.707 10.7772C14.5983 10.8714 14.5439 10.9189 14.506 10.9552C14.505 10.9562 14.504 10.9571 14.5031 10.958C14.503 10.9593 14.5029 10.9607 14.5029 10.9621C14.5003 11.0145 14.5 11.0867 14.5 11.2306V16.4584C14.5 16.4693 14.5001 16.4821 14.5003 16.4964C14.5017 16.6339 14.5048 16.9193 14.4054 17.1858C14.3218 17.4099 14.1858 17.6108 14.0087 17.7716C13.7983 17.9629 13.5321 18.0661 13.4039 18.1158C13.3905 18.121 13.3787 18.1256 13.3685 18.1296L9.96849 19.4896C9.95924 19.4933 9.94993 19.4971 9.94056 19.5008C9.78169 19.5644 9.60605 19.6347 9.45112 19.681C9.28444 19.7307 9.01062 19.7957 8.69481 19.7301C8.30778 19.6497 7.96815 19.4198 7.74974 19.0903C7.57152 18.8214 7.53021 18.543 7.51449 18.3698C7.49987 18.2088 7.49993 18.0196 7.49998 17.8484L7.49999 11.2306C7.49999 11.0867 7.4997 11.0145 7.49711 10.9621C7.49704 10.9607 7.49697 10.9593 7.4969 10.958C7.49596 10.9571 7.49499 10.9562 7.49397 10.9552C7.45606 10.9189 7.40167 10.8714 7.29294 10.7772L0.897167 5.23419C0.883381 5.22224 0.869176 5.21001 0.854625 5.19747C0.696805 5.06149 0.498123 4.89032 0.348969 4.67216C0.219715 4.4831 0.123892 4.27325 0.065678 4.05176C-0.00149834 3.79617 -0.000732896 3.53391 -0.000124929 3.3256C-6.89005e-05 3.30639 -1.41834e-05 3.28764 -1.41834e-05 3.2694V2.60001C-1.41834e-05 2.58936 -1.54947e-05 2.57873 -1.6806e-05 2.56811C-4.67275e-05 2.31572 -7.61729e-05 2.06993 0.0169311 1.86178C0.0356079 1.63318 0.0796754 1.36345 0.217973 1.09202C0.40972 0.7157 0.715681 0.409739 1.092 0.217992C1.36343 0.0796948 1.63316 0.0356272 1.86176 0.0169504C2.06991 -5.6861e-05 2.31571 -2.74161e-05 2.56809 2.50544e-06ZM2.01143 2.01145C2.01105 2.0156 2.01067 2.02 2.01029 2.02464C2.00076 2.14122 1.99999 2.30348 1.99999 2.60001V3.2694C1.99999 3.41327 2.00027 3.48549 2.00287 3.5379C2.00293 3.53931 2.00301 3.54067 2.00308 3.54197C2.00401 3.54287 2.00499 3.54381 2.00601 3.54478C2.04392 3.58108 2.09831 3.62859 2.20703 3.72281L8.60281 9.26582C8.61659 9.27777 8.6308 9.29001 8.64535 9.30254C8.80317 9.43852 9.00185 9.60969 9.151 9.82785C9.28026 10.0169 9.37608 10.2268 9.43429 10.4483C9.50147 10.7038 9.5007 10.9661 9.5001 11.1744C9.50004 11.1936 9.49999 11.2124 9.49999 11.2306V17.523L12.5 16.323V11.2306C12.5 11.2124 12.4999 11.1936 12.4999 11.1744C12.4993 10.9661 12.4985 10.7038 12.5657 10.4483C12.6239 10.2268 12.7197 10.0169 12.849 9.82785C12.9981 9.60969 13.1968 9.43851 13.3546 9.30254C13.3692 9.29 13.3834 9.27777 13.3972 9.26582L19.7929 3.72281C19.9017 3.62859 19.9561 3.58108 19.994 3.54479C19.995 3.54381 19.996 3.54287 19.9969 3.54197C19.997 3.54067 19.997 3.53931 19.9971 3.5379C19.9997 3.48549 20 3.41327 20 3.2694V2.60001C20 2.30348 19.9992 2.14122 19.9897 2.02464C19.9893 2.02 19.9889 2.0156 19.9885 2.01145C19.9844 2.01107 19.98 2.01069 19.9754 2.01031C19.8588 2.00078 19.6965 2.00001 19.4 2.00001H2.59999C2.30346 2.00001 2.1412 2.00078 2.02462 2.01031C2.01998 2.01069 2.01558 2.01107 2.01143 2.01145Z"
      fill="#030712"
    />
  </svg>
);

export const option = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5 text-gray-500"
    viewBox="0 0 20 20"
    fill="currentColor"
  >
    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
  </svg>
);

export const person = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.0001 2.66663C9.42275 2.66663 7.33341 4.75596 7.33341 7.33329C7.33341 9.91062 9.42275 12 12.0001 12C14.5774 12 16.6667 9.91062 16.6667 7.33329C16.6667 4.75596 14.5774 2.66663 12.0001 2.66663ZM9.33342 7.33329C9.33342 5.86053 10.5273 4.66663 12.0001 4.66663C13.4728 4.66663 14.6667 5.86053 14.6667 7.33329C14.6667 8.80605 13.4728 9.99996 12.0001 9.99996C10.5273 9.99996 9.33342 8.80605 9.33342 7.33329Z"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.312 17.6357C17.8368 14.9802 15.0378 13.3333 12.0001 13.3333C8.96233 13.3333 6.16338 14.9802 4.68812 17.6357L4.10591 18.6836C3.44549 19.8724 4.30508 21.3333 5.66497 21.3333H18.3352C19.6951 21.3333 20.5546 19.8724 19.8942 18.6836L19.312 17.6357ZM6.43644 18.607C7.55895 16.5864 9.68867 15.3333 12.0001 15.3333C14.3115 15.3333 16.4412 16.5864 17.5637 18.607L17.9672 19.3333H6.03292L6.43644 18.607Z"
    />
  </svg>
);

export const store = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.653 2.00012H7.34729C6.79932 2.00012 6.28169 2.25171 5.94315 2.68258L2.53433 7.02109C2.18825 7.46155 2.00012 8.00548 2.00012 8.56564V9.50012C2.00012 11.0268 2.87095 12.3501 4.14298 13.0003V19.5001C4.14298 20.8808 5.26227 22.0001 6.64298 22.0001H17.3573C18.738 22.0001 19.8573 20.8808 19.8573 19.5001V13.0003C21.1293 12.3501 22.0001 11.0268 22.0001 9.50012V8.81269C22.0001 8.09248 21.7582 7.39314 21.3133 6.82683L18.0571 2.68258C17.7185 2.25171 17.2009 2.00012 16.653 2.00012ZM14.5001 19.8573H17.3573C17.5545 19.8573 17.7144 19.6974 17.7144 19.5001V13.4287C16.5887 13.4287 15.5735 12.9552 14.8573 12.1965C14.141 12.9552 13.1259 13.4287 12.0001 13.4287C10.8744 13.4287 9.85924 12.9552 9.14298 12.1965C8.42671 12.9552 7.41157 13.4287 6.28584 13.4287V19.5001C6.28584 19.6974 6.44573 19.8573 6.64298 19.8573H9.61905V16.2858C9.61905 15.4969 10.2586 14.8573 11.0476 14.8573H13.0715C13.8605 14.8573 14.5001 15.4969 14.5001 16.2858V19.8573ZM6.28584 11.2858H5.92869C4.94247 11.2858 4.14298 10.4863 4.14298 9.50012V8.56564C4.14298 8.48562 4.16985 8.40791 4.21929 8.34499L7.52087 4.14298H16.4794L19.6283 8.15073C19.7766 8.3395 19.8573 8.57262 19.8573 8.81269V9.50012C19.8573 10.4863 19.0578 11.2858 18.0716 11.2858H17.7144C16.7282 11.2858 15.9287 10.4863 15.9287 9.50012V8.78584C15.9287 8.1941 15.449 7.71441 14.8573 7.71441C14.2655 7.71441 13.7858 8.1941 13.7858 8.78584V9.50012C13.7858 10.4863 12.9863 11.2858 12.0001 11.2858C11.0139 11.2858 10.2144 10.4863 10.2144 9.50012V8.78584C10.2144 8.1941 9.73471 7.71441 9.14298 7.71441C8.55124 7.71441 8.07155 8.1941 8.07155 8.78584V9.50012C8.07155 10.4863 7.27206 11.2858 6.28584 11.2858Z"
    />
  </svg>
);

export const smile = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M14 7.99996C13.4477 7.99996 13 8.44767 13 8.99996V11C13 11.5522 13.4477 12 14 12C14.5522 12 15 11.5522 15 11V8.99996C15 8.44767 14.5522 7.99996 14 7.99996Z" />
    <path d="M8.99996 8.99996C8.99996 8.44767 9.44767 7.99996 9.99996 7.99996C10.5522 7.99996 11 8.44767 11 8.99996V11C11 11.5522 10.5522 12 9.99996 12C9.44767 12 8.99996 11.5522 8.99996 11V8.99996Z" />
    <path d="M14.0292 14.6445C13.447 15.0912 12.7337 15.3333 12 15.3333C11.2662 15.3333 10.5529 15.0912 9.97075 14.6445C9.38861 14.1978 8.97012 13.5715 8.78021 12.8627C8.63726 12.3292 8.08893 12.0126 7.55546 12.1556C7.02199 12.2985 6.70541 12.8469 6.84835 13.3803C7.15222 14.5144 7.82179 15.5165 8.75323 16.2312C9.68467 16.9459 10.8259 17.3333 12 17.3333C13.174 17.3333 14.3152 16.9459 15.2467 16.2312C16.1781 15.5165 16.8477 14.5144 17.1516 13.3803C17.2945 12.8469 16.9779 12.2985 16.4445 12.1556C15.911 12.0126 15.3627 12.3292 15.2197 12.8627C15.0298 13.5715 14.6113 14.1978 14.0292 14.6445Z" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.3333 12C21.3333 17.1546 17.1546 21.3333 12 21.3333C6.8453 21.3333 2.66663 17.1546 2.66663 12C2.66663 6.8453 6.8453 2.66663 12 2.66663C17.1546 2.66663 21.3333 6.8453 21.3333 12ZM19.3333 12C19.3333 16.05 16.05 19.3333 12 19.3333C7.94987 19.3333 4.66663 16.05 4.66663 12C4.66663 7.94987 7.94987 4.66663 12 4.66663C16.05 4.66663 19.3333 7.94987 19.3333 12Z"
    />
  </svg>
);

export const columnCustomize = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7587 2H16.2413C17.0463 1.99999 17.7106 1.99998 18.2518 2.04419C18.8139 2.09012 19.3306 2.18868 19.816 2.43598C20.5686 2.81947 21.1805 3.43139 21.564 4.18404C21.8113 4.66937 21.9099 5.18608 21.9558 5.74817C22 6.28936 22 6.95372 22 7.75868V16.2413C22 17.0463 22 17.7106 21.9558 18.2518C21.9099 18.8139 21.8113 19.3306 21.564 19.816C21.1805 20.5686 20.5686 21.1805 19.816 21.564C19.3306 21.8113 18.8139 21.9099 18.2518 21.9558C17.7106 22 17.0463 22 16.2413 22H7.75868C6.95372 22 6.28936 22 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43598 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413V7.7587C1.99999 6.95373 1.99998 6.28937 2.04419 5.74817C2.09012 5.18608 2.18868 4.66937 2.43597 4.18404C2.81947 3.43139 3.43139 2.81947 4.18404 2.43597C4.66937 2.18868 5.18608 2.09012 5.74817 2.04419C6.28937 1.99998 6.95373 1.99999 7.7587 2ZM10 20H14V4H10V20ZM8 4V20H7.8C6.94342 20 6.36113 19.9992 5.91104 19.9624C5.47262 19.9266 5.24842 19.8617 5.09202 19.782C4.7157 19.5903 4.40973 19.2843 4.21799 18.908C4.1383 18.7516 4.07337 18.5274 4.03755 18.089C4.00078 17.6389 4 17.0566 4 16.2V7.8C4 6.94342 4.00078 6.36113 4.03755 5.91104C4.07337 5.47262 4.1383 5.24842 4.21799 5.09202C4.40973 4.7157 4.7157 4.40973 5.09202 4.21799C5.24842 4.1383 5.47262 4.07337 5.91104 4.03755C6.36113 4.00078 6.94342 4 7.8 4H8ZM16 4V20H16.2C17.0566 20 17.6389 19.9992 18.089 19.9624C18.5274 19.9266 18.7516 19.8617 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C19.8617 18.7516 19.9266 18.5274 19.9624 18.089C19.9992 17.6389 20 17.0566 20 16.2V7.8C20 6.94342 19.9992 6.36113 19.9624 5.91104C19.9266 5.47262 19.8617 5.24842 19.782 5.09202C19.5903 4.7157 19.2843 4.40973 18.908 4.21799C18.7516 4.1383 18.5274 4.07337 18.089 4.03755C17.6389 4.00078 17.0566 4 16.2 4H16Z"
      fill="#030712"
    />
  </svg>
);
export const calendar = (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
    />
  </svg>
);
export const previous = (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path
      fillRule="evenodd"
      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
      clipRule="evenodd"
    />
  </svg>
);

export const next = (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path
      fillRule="evenodd"
      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
      clipRule="evenodd"
    />
  </svg>
);

export const checkCircle = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_40001552_27230)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.66797 10C2.66797 5.94993 5.95121 2.66669 10.0013 2.66669C14.0514 2.66669 17.3346 5.94993 17.3346 10C17.3346 14.0501 14.0514 17.3334 10.0013 17.3334C5.95121 17.3334 2.66797 14.0501 2.66797 10ZM13.4727 7.52862C13.7331 7.78897 13.7331 8.21108 13.4727 8.47143L9.47271 12.4714C9.21236 12.7318 8.79025 12.7318 8.5299 12.4714L6.5299 10.4714C6.26955 10.2111 6.26955 9.78897 6.5299 9.52862C6.79025 9.26827 7.21236 9.26827 7.47271 9.52862L9.0013 11.0572L12.5299 7.52862C12.7902 7.26827 13.2124 7.26827 13.4727 7.52862Z"
        fill="#15803D"
      />
    </g>
    <defs>
      <clipPath id="clip0_40001552_27230">
        <rect width="16" height="16" fill="white" transform="translate(2 2)" />
      </clipPath>
    </defs>
  </svg>
);

export const circleEmpty = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_40001552_27242)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.66797 10C2.66797 5.94993 5.95121 2.66669 10.0013 2.66669C14.0514 2.66669 17.3346 5.94993 17.3346 10C17.3346 14.0501 14.0514 17.3334 10.0013 17.3334C5.95121 17.3334 2.66797 14.0501 2.66797 10ZM9.33464 12.6667C9.33464 12.2985 9.63311 12 10.0013 12H10.008C10.3762 12 10.6746 12.2985 10.6746 12.6667C10.6746 13.0349 10.3762 13.3334 10.008 13.3334H10.0013C9.63311 13.3334 9.33464 13.0349 9.33464 12.6667ZM10.0013 6.66669C10.3695 6.66669 10.668 6.96516 10.668 7.33335V10C10.668 10.3682 10.3695 10.6667 10.0013 10.6667C9.63311 10.6667 9.33464 10.3682 9.33464 10V7.33335C9.33464 6.96516 9.63311 6.66669 10.0013 6.66669Z"
        fill="#CBD5E1"
      />
    </g>
    <defs>
      <clipPath id="clip0_40001552_27242">
        <rect width="16" height="16" fill="white" transform="translate(2 2)" />
      </clipPath>
    </defs>
  </svg>
);

export const info = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.99998 14C9.58577 14 9.24999 13.6642 9.25 13.25L9.25006 9.74999C9.25007 9.33577 9.58586 8.99999 10.0001 9C10.4143 9.00001 10.7501 9.3358 10.7501 9.75001L10.75 13.25C10.75 13.6642 10.4142 14 9.99998 14Z"
      fill="#030712"
    />
    <path
      d="M9 7C9 6.44772 9.44772 6 10 6C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8C9.44772 8 9 7.55228 9 7Z"
      fill="#030712"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM15.5 10C15.5 13.0376 13.0376 15.5 10 15.5C6.96243 15.5 4.5 13.0376 4.5 10C4.5 6.96243 6.96243 4.5 10 4.5C13.0376 4.5 15.5 6.96243 15.5 10Z"
      fill="#030712"
    />
  </svg>
);

export const imageUpload = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19 1C19.5523 1 20 1.44772 20 2V4H22C22.5523 4 23 4.44772 23 5C23 5.55228 22.5523 6 22 6H20V8C20 8.55228 19.5523 9 19 9C18.4477 9 18 8.55228 18 8V6H16C15.4477 6 15 5.55228 15 5C15 4.44772 15.4477 4 16 4H18V2C18 1.44772 18.4477 1 19 1ZM7.7587 2H12.5C13.0523 2 13.5 2.44772 13.5 3C13.5 3.55228 13.0523 4 12.5 4H7.8C6.94342 4 6.36113 4.00078 5.91104 4.03755C5.47262 4.07337 5.24842 4.1383 5.09202 4.21799C4.7157 4.40973 4.40973 4.71569 4.21799 5.09202C4.1383 5.24842 4.07337 5.47262 4.03755 5.91104C4.00078 6.36113 4 6.94342 4 7.8V16.2C4 17.0566 4.00078 17.6389 4.03755 18.089C4.07337 18.5274 4.1383 18.7516 4.21799 18.908C4.39721 19.2597 4.6762 19.55 5.01918 19.743C5.10557 19.6076 5.19801 19.5052 5.25886 19.4393C5.41056 19.2752 5.61693 19.0876 5.83041 18.8936L14.3385 11.159C14.4994 11.0127 14.6586 10.8679 14.8043 10.7544C14.9663 10.6281 15.1671 10.4949 15.4236 10.4116C15.7843 10.2945 16.1708 10.2823 16.5381 10.3764C16.7994 10.4434 17.0082 10.5637 17.1778 10.6795C17.3304 10.7836 17.4984 10.918 17.6682 11.054L20.4878 13.3096C20.5095 13.327 20.5311 13.3443 20.5526 13.3614C20.8668 13.6123 21.1434 13.8331 21.3599 14.109C21.6295 14.4524 21.8208 14.8506 21.9206 15.2756C22.0007 15.617 22.0004 15.9709 22 16.3731C22 16.4005 22 16.4281 22 16.456V16.4913C22 16.6577 22 16.817 21.9995 16.9696C21.9998 16.9797 22 16.9898 22 17C22 17.0465 22 17.0924 22 17.1376C22.0005 17.933 22.0008 18.5236 21.8637 19.0353C21.7019 19.6392 21.4045 20.1853 21.0055 20.6395C20.8626 20.8022 20.7067 20.9531 20.5393 21.0907C20.1058 21.4469 19.5954 21.7136 19.0353 21.8637C18.5236 22.0008 17.933 22.0005 17.1376 22C17.0924 22 17.0465 22 17 22H16.552C16.5319 22 16.5116 22 16.4913 22H7.75868C7.73455 22 7.71056 22 7.68669 22L7.03139 22C6.77557 22 6.52731 22.0001 6.31909 21.9865C6.11522 21.98 5.92511 21.9703 5.74817 21.9558C5.18608 21.9099 4.66937 21.8113 4.18404 21.564C3.43139 21.1805 2.81947 20.5686 2.43598 19.816C2.18868 19.3306 2.09012 18.8139 2.04419 18.2518C1.99998 17.7106 1.99999 17.0463 2 16.2413V7.7587C1.99999 6.95373 1.99998 6.28937 2.04419 5.74817C2.09012 5.18608 2.18868 4.66937 2.43597 4.18404C2.81947 3.43139 3.43139 2.81947 4.18404 2.43597C4.66937 2.18868 5.18608 2.09012 5.74817 2.04419C6.28937 1.99998 6.95373 1.99999 7.7587 2ZM19.59 19.2132C19.6895 19.0829 19.7733 18.9398 19.8388 18.7866C19.8974 18.6496 19.9457 18.4536 19.9722 18.0757C19.9994 17.6884 20 17.1897 20 16.456C20 15.9243 19.9938 15.819 19.9735 15.7327C19.9403 15.591 19.8765 15.4583 19.7866 15.3438C19.7319 15.2741 19.6535 15.2035 19.2384 14.8713L16.4411 12.6335C16.2401 12.4727 16.1323 12.3873 16.0502 12.3313C16.0473 12.3293 16.0445 12.3274 16.0418 12.3256C16.0393 12.3276 16.0366 12.3296 16.0338 12.3318C15.9554 12.3929 15.8532 12.4849 15.6627 12.6581L7.58665 20C7.65583 20 7.72692 20 7.8 20H16.456C17.1897 20 17.6884 19.9994 18.0757 19.9722C18.4536 19.9457 18.6496 19.8974 18.7866 19.8388C18.9423 19.7722 19.0876 19.6867 19.2196 19.5851C19.3589 19.478 19.4834 19.3529 19.59 19.2132ZM8.5 7.5C7.94772 7.5 7.5 7.94772 7.5 8.5C7.5 9.05228 7.94772 9.5 8.5 9.5C9.05229 9.5 9.5 9.05228 9.5 8.5C9.5 7.94772 9.05229 7.5 8.5 7.5ZM5.5 8.5C5.5 6.84315 6.84315 5.5 8.5 5.5C10.1569 5.5 11.5 6.84315 11.5 8.5C11.5 10.1569 10.1569 11.5 8.5 11.5C6.84315 11.5 5.5 10.1569 5.5 8.5Z"
      fill="#030712"
    />
  </svg>
);

export const listingSectionLocked = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6 8C6 4.68629 8.68629 2 12 2C15.3137 2 18 4.68629 18 8V9.15032C18.2826 9.21225 18.5539 9.30243 18.816 9.43597C19.5686 9.81947 20.1805 10.4314 20.564 11.184C20.8113 11.6694 20.9099 12.1861 20.9558 12.7482C21 13.2894 21 13.9537 21 14.7587V16.2413C21 17.0463 21 17.7106 20.9558 18.2518C20.9099 18.8139 20.8113 19.3306 20.564 19.816C20.1805 20.5686 19.5686 21.1805 18.816 21.564C18.3306 21.8113 17.8139 21.9099 17.2518 21.9558C16.7106 22 16.0463 22 15.2413 22H8.75868C7.95372 22 7.28936 22 6.74817 21.9558C6.18608 21.9099 5.66937 21.8113 5.18404 21.564C4.43139 21.1805 3.81947 20.5686 3.43597 19.816C3.18868 19.3306 3.09012 18.8139 3.04419 18.2518C2.99998 17.7106 2.99999 17.0463 3 16.2413V14.7587C2.99999 13.9537 2.99998 13.2894 3.04419 12.7482C3.09012 12.1861 3.18868 11.6694 3.43597 11.184C3.81947 10.4314 4.43139 9.81947 5.18404 9.43597C5.44614 9.30243 5.71739 9.21225 6 9.15032V8ZM8 9.00163C8.23771 8.99999 8.4904 9 8.7587 9H15.2413C15.5096 9 15.7623 8.99999 16 9.00163V8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8V9.00163ZM6.91104 11.0376C6.47262 11.0734 6.24842 11.1383 6.09202 11.218C5.7157 11.4097 5.40973 11.7157 5.21799 12.092C5.1383 12.2484 5.07337 12.4726 5.03755 12.911C5.00078 13.3611 5 13.9434 5 14.8V16.2C5 17.0566 5.00078 17.6389 5.03755 18.089C5.07337 18.5274 5.1383 18.7516 5.21799 18.908C5.40973 19.2843 5.7157 19.5903 6.09202 19.782C6.24842 19.8617 6.47262 19.9266 6.91104 19.9624C7.36113 19.9992 7.94342 20 8.8 20H15.2C16.0566 20 16.6389 19.9992 17.089 19.9624C17.5274 19.9266 17.7516 19.8617 17.908 19.782C18.2843 19.5903 18.5903 19.2843 18.782 18.908C18.8617 18.7516 18.9266 18.5274 18.9624 18.089C18.9992 17.6389 19 17.0566 19 16.2V14.8C19 13.9434 18.9992 13.3611 18.9624 12.911C18.9266 12.4726 18.8617 12.2484 18.782 12.092C18.5903 11.7157 18.2843 11.4097 17.908 11.218C17.7516 11.1383 17.5274 11.0734 17.089 11.0376C16.6389 11.0008 16.0566 11 15.2 11H8.8C7.94342 11 7.36113 11.0008 6.91104 11.0376ZM12 13.5C12.5523 13.5 13 13.9477 13 14.5V16.5C13 17.0523 12.5523 17.5 12 17.5C11.4477 17.5 11 17.0523 11 16.5V14.5C11 13.9477 11.4477 13.5 12 13.5Z"
      fill="#CBD5E1"
    />
  </svg>
);

export const tipAndTrick = (
  <svg
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.75">
      <path
        d="M17.2 27.9221V31.2001C17.2 32.7465 18.4536 34.0001 20 34.0001C21.5464 34.0001 22.8 32.7465 22.8 31.2001V27.9221M20 6.00006V7.40006M7.4 20.0001H6M10.9 10.9001L10.0599 10.0599M29.1 10.9001L29.9403 10.0599M34 20.0001H32.6M28.4 20.0001C28.4 24.6393 24.6392 28.4001 20 28.4001C15.3608 28.4001 11.6 24.6393 11.6 20.0001C11.6 15.3609 15.3608 11.6001 20 11.6001C24.6392 11.6001 28.4 15.3609 28.4 20.0001Z"
        stroke="#1D4ED8"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
  </svg>
);

export const success = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM17.2071 8.29289C17.5976 8.68342 17.5976 9.31658 17.2071 9.70711L11.2071 15.7071C10.8166 16.0976 10.1834 16.0976 9.79289 15.7071L6.79289 12.7071C6.40237 12.3166 6.40237 11.6834 6.79289 11.2929C7.18342 10.9024 7.81658 10.9024 8.20711 11.2929L10.5 13.5858L15.7929 8.29289C16.1834 7.90237 16.8166 7.90237 17.2071 8.29289Z"
      fill="#15803D"
    />
  </svg>
);

export const addMore = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 4C12.5523 4 13 4.44772 13 5V11H19C19.5523 11 20 11.4477 20 12C20 12.5523 19.5523 13 19 13H13V19C13 19.5523 12.5523 20 12 20C11.4477 20 11 19.5523 11 19V13H5C4.44772 13 4 12.5523 4 12C4 11.4477 4.44772 11 5 11H11V5C11 4.44772 11.4477 4 12 4Z"
      fill="#1E40AF"
    />
  </svg>
);
export const proman = (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M25.5996 0C29.1342 0 32 2.86577 32 6.40039V25.5996C32 29.1342 29.1342 32 25.5996 32H6.40039C2.86577 32 0 29.1342 0 25.5996V6.40039C0 2.86577 2.86577 0 6.40039 0H25.5996ZM5.33398 16C5.33398 21.891 10.1091 26.6669 16 26.667C21.891 26.667 26.667 21.891 26.667 16H22.4004C22.4004 19.5346 19.5346 22.4004 16 22.4004C12.4655 22.4002 9.60059 19.5345 9.60059 16H5.33398Z"
      fill="white"
    />
  </svg>
);
export const downChevron = (
  <svg
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L7 5.58579L12.2929 0.292893C12.6834 -0.0976311 13.3166 -0.0976311 13.7071 0.292893C14.0976 0.683417 14.0976 1.31658 13.7071 1.70711L7.70711 7.70711C7.31658 8.09763 6.68342 8.09763 6.29289 7.70711L0.292893 1.70711C-0.0976311 1.31658 -0.0976311 0.683417 0.292893 0.292893Z"
      fill="#030712"
    />
  </svg>
);
export const qr = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.56811 2C4.57873 2 4.58936 2.00001 4.60001 2.00001H8.40001C8.41065 2.00001 8.42128 2 8.4319 2C8.68429 1.99997 8.93008 1.99994 9.13824 2.01695C9.36683 2.03563 9.63656 2.07969 9.90799 2.21799C10.2843 2.40974 10.5903 2.7157 10.782 3.09202C10.9203 3.36345 10.9644 3.63318 10.9831 3.86178C11.0001 4.06994 11 4.31574 11 4.56812V8.43189C11 8.68427 11.0001 8.93007 10.9831 9.13824C10.9644 9.36683 10.9203 9.63656 10.782 9.90799C10.5903 10.2843 10.2843 10.5903 9.90799 10.782C9.63656 10.9203 9.36683 10.9644 9.13824 10.9831C8.93007 11.0001 8.68427 11 8.43189 11H4.56812C4.31574 11 4.06994 11.0001 3.86178 10.9831C3.63318 10.9644 3.36345 10.9203 3.09202 10.782C2.7157 10.5903 2.40974 10.2843 2.21799 9.90799C2.07969 9.63656 2.03563 9.36683 2.01695 9.13824C1.99994 8.93008 1.99997 8.68429 2 8.4319C2 8.42128 2.00001 8.41065 2.00001 8.40001V4.60001C2.00001 4.58936 2 4.57873 2 4.56811C1.99997 4.31572 1.99994 4.06993 2.01695 3.86178C2.03563 3.63318 2.07969 3.36345 2.21799 3.09202C2.40974 2.7157 2.7157 2.40974 3.09202 2.21799C3.36345 2.07969 3.63318 2.03563 3.86178 2.01695C4.06993 1.99994 4.31572 1.99997 4.56811 2ZM4.01145 4.01145C4.01107 4.0156 4.01069 4.02 4.01031 4.02464C4.00078 4.14122 4.00001 4.30348 4.00001 4.60001V8.40001C4.00001 8.69653 4.00078 8.85879 4.01031 8.97537C4.01069 8.98001 4.01107 8.98441 4.01145 8.98856C4.0156 8.98894 4.02 8.98932 4.02464 8.9897C4.14122 8.99923 4.30348 9.00001 4.60001 9.00001H8.40001C8.69653 9.00001 8.85879 8.99923 8.97537 8.9897C8.98001 8.98932 8.98441 8.98894 8.98856 8.98856C8.98894 8.98441 8.98932 8.98001 8.9897 8.97537C8.99923 8.85879 9.00001 8.69653 9.00001 8.40001V4.60001C9.00001 4.30348 8.99923 4.14122 8.9897 4.02464C8.98932 4.02 8.98894 4.0156 8.98856 4.01145C8.98441 4.01107 8.98001 4.01069 8.97537 4.01031C8.85879 4.00078 8.69653 4.00001 8.40001 4.00001H4.60001C4.30348 4.00001 4.14122 4.00078 4.02464 4.01031C4.02 4.01069 4.0156 4.01107 4.01145 4.01145ZM15.5681 2H19.4319C19.6843 1.99997 19.9301 1.99994 20.1382 2.01695C20.3668 2.03563 20.6366 2.07969 20.908 2.21799C21.2843 2.40974 21.5903 2.7157 21.782 3.09202C21.9203 3.36345 21.9644 3.63318 21.9831 3.86178C22.0001 4.06993 22 4.31571 22 4.56809V8.43192C22 8.6843 22.0001 8.93008 21.9831 9.13824C21.9644 9.36683 21.9203 9.63656 21.782 9.90799C21.5903 10.2843 21.2843 10.5903 20.908 10.782C20.6366 10.9203 20.3668 10.9644 20.1382 10.9831C19.9301 11.0001 19.6843 11 19.4319 11H15.5681C15.3157 11 15.0699 11.0001 14.8618 10.9831C14.6332 10.9644 14.3634 10.9203 14.092 10.782C13.7157 10.5903 13.4097 10.2843 13.218 9.90799C13.0797 9.63656 13.0356 9.36683 13.0169 9.13824C12.9999 8.93007 13 8.68428 13 8.43189V4.56812C13 4.31574 12.9999 4.06994 13.0169 3.86178C13.0356 3.63318 13.0797 3.36345 13.218 3.09202C13.4097 2.7157 13.7157 2.40974 14.092 2.21799C14.3634 2.07969 14.6332 2.03563 14.8618 2.01695C15.0699 1.99994 15.3157 1.99997 15.5681 2ZM15.0115 4.01145C15.0111 4.0156 15.0107 4.02 15.0103 4.02464C15.0008 4.14122 15 4.30348 15 4.60001V8.40001C15 8.69653 15.0008 8.85879 15.0103 8.97537C15.0107 8.98001 15.0111 8.98441 15.0115 8.98856C15.0156 8.98894 15.02 8.98932 15.0246 8.9897C15.1412 8.99923 15.3035 9.00001 15.6 9.00001H19.4C19.6965 9.00001 19.8588 8.99923 19.9754 8.9897C19.98 8.98932 19.9844 8.98893 19.9886 8.98855C19.9889 8.9844 19.9893 8.98001 19.9897 8.97537C19.9992 8.85879 20 8.69653 20 8.40001V4.60001C20 4.30348 19.9992 4.14122 19.9897 4.02464C19.9893 4.02 19.9889 4.0156 19.9885 4.01145C19.9844 4.01107 19.98 4.01069 19.9754 4.01031C19.8588 4.00078 19.6965 4.00001 19.4 4.00001H15.6C15.3035 4.00001 15.1412 4.00078 15.0246 4.01031C15.02 4.01069 15.0156 4.01107 15.0115 4.01145ZM5.50001 6.50001C5.50001 5.94772 5.94772 5.50001 6.50001 5.50001H6.51001C7.06229 5.50001 7.51001 5.94772 7.51001 6.50001C7.51001 7.05229 7.06229 7.50001 6.51001 7.50001H6.50001C5.94772 7.50001 5.50001 7.05229 5.50001 6.50001ZM16.5 6.50001C16.5 5.94772 16.9477 5.50001 17.5 5.50001H17.51C18.0623 5.50001 18.51 5.94772 18.51 6.50001C18.51 7.05229 18.0623 7.50001 17.51 7.50001H17.5C16.9477 7.50001 16.5 7.05229 16.5 6.50001ZM12 13C12 12.4477 12.4477 12 13 12H13.01C13.5623 12 14.01 12.4477 14.01 13C14.01 13.5523 13.5623 14 13.01 14H13C12.4477 14 12 13.5523 12 13ZM4.56812 13H8.43189C8.68428 13 8.93007 12.9999 9.13824 13.0169C9.36683 13.0356 9.63656 13.0797 9.90799 13.218C10.2843 13.4097 10.5903 13.7157 10.782 14.092C10.9203 14.3634 10.9644 14.6332 10.9831 14.8618C11.0001 15.0699 11 15.3157 11 15.5681V19.4319C11 19.6843 11.0001 19.9301 10.9831 20.1382C10.9644 20.3668 10.9203 20.6366 10.782 20.908C10.5903 21.2843 10.2843 21.5903 9.90799 21.782C9.63656 21.9203 9.36683 21.9644 9.13824 21.9831C8.93008 22.0001 8.6843 22 8.43192 22H4.56809C4.31571 22 4.06993 22.0001 3.86178 21.9831C3.63318 21.9644 3.36345 21.9203 3.09202 21.782C2.7157 21.5903 2.40974 21.2843 2.21799 20.908C2.07969 20.6366 2.03563 20.3668 2.01695 20.1382C1.99994 19.9301 1.99997 19.6843 2 19.4319V15.5681C1.99997 15.3157 1.99994 15.0699 2.01695 14.8618C2.03563 14.6332 2.07969 14.3634 2.21799 14.092C2.40974 13.7157 2.7157 13.4097 3.09202 13.218C3.36345 13.0797 3.63318 13.0356 3.86178 13.0169C4.06994 12.9999 4.31574 13 4.56812 13ZM4.01145 15.0115C4.01107 15.0156 4.01069 15.02 4.01031 15.0246C4.00078 15.1412 4.00001 15.3035 4.00001 15.6V19.4C4.00001 19.6965 4.00078 19.8588 4.01031 19.9754C4.01069 19.98 4.01107 19.9844 4.01145 19.9885C4.0156 19.9889 4.02 19.9893 4.02464 19.9897C4.14122 19.9992 4.30348 20 4.60001 20H8.40001C8.69653 20 8.85879 19.9992 8.97537 19.9897C8.98001 19.9893 8.98441 19.9889 8.98856 19.9885C8.98894 19.9844 8.98932 19.98 8.9897 19.9754C8.99923 19.8588 9.00001 19.6965 9.00001 19.4V15.6C9.00001 15.3035 8.99923 15.1412 8.9897 15.0246C8.98932 15.02 8.98894 15.0156 8.98856 15.0115C8.98441 15.0111 8.98001 15.0107 8.97537 15.0103C8.85879 15.0008 8.69653 15 8.40001 15H4.60001C4.30348 15 4.14122 15.0008 4.02464 15.0103C4.02 15.0107 4.0156 15.0111 4.01145 15.0115ZM15.5 14C15.5 13.4477 15.9477 13 16.5 13H21C21.5523 13 22 13.4477 22 14C22 14.5523 21.5523 15 21 15H16.5C15.9477 15 15.5 14.5523 15.5 14ZM14 15.5C14.5523 15.5 15 15.9477 15 16.5V21C15 21.5523 14.5523 22 14 22C13.4477 22 13 21.5523 13 21V16.5C13 15.9477 13.4477 15.5 14 15.5ZM21 16C21.5523 16 22 16.4477 22 17V21C22 21.5523 21.5523 22 21 22H17C16.4477 22 16 21.5523 16 21C16 20.4477 16.4477 20 17 20H20V17C20 16.4477 20.4477 16 21 16ZM5.50001 17.5C5.50001 16.9477 5.94772 16.5 6.50001 16.5H6.51001C7.06229 16.5 7.51001 16.9477 7.51001 17.5C7.51001 18.0523 7.06229 18.5 6.51001 18.5H6.50001C5.94772 18.5 5.50001 18.0523 5.50001 17.5ZM16.5 17.5C16.5 16.9477 16.9477 16.5 17.5 16.5H17.51C18.0623 16.5 18.51 16.9477 18.51 17.5C18.51 18.0523 18.0623 18.5 17.51 18.5H17.5C16.9477 18.5 16.5 18.0523 16.5 17.5Z"
      fill="#030712"
    />
  </svg>
);
export const phone = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 17.5H12.01M8.2 22H15.8C16.9201 22 17.4802 22 17.908 21.782C18.2843 21.5903 18.5903 21.2843 18.782 20.908C19 20.4802 19 19.9201 19 18.8V5.2C19 4.07989 19 3.51984 18.782 3.09202C18.5903 2.71569 18.2843 2.40973 17.908 2.21799C17.4802 2 16.9201 2 15.8 2H8.2C7.0799 2 6.51984 2 6.09202 2.21799C5.71569 2.40973 5.40973 2.71569 5.21799 3.09202C5 3.51984 5 4.0799 5 5.2V18.8C5 19.9201 5 20.4802 5.21799 20.908C5.40973 21.2843 5.71569 21.5903 6.09202 21.782C6.51984 22 7.07989 22 8.2 22ZM12.5 17.5C12.5 17.7761 12.2761 18 12 18C11.7239 18 11.5 17.7761 11.5 17.5C11.5 17.2239 11.7239 17 12 17C12.2761 17 12.5 17.2239 12.5 17.5Z"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const rightChevron = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.29289 5.29289C8.68342 4.90237 9.31658 4.90237 9.70711 5.29289L15.7071 11.2929C16.0976 11.6834 16.0976 12.3166 15.7071 12.7071L9.70711 18.7071C9.31658 19.0976 8.68342 19.0976 8.29289 18.7071C7.90237 18.3166 7.90237 17.6834 8.29289 17.2929L13.5858 12L8.29289 6.70711C7.90237 6.31658 7.90237 5.68342 8.29289 5.29289Z"
      fill="#030712"
    />
  </svg>
);
export const complete = (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_40000222_15746)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.833496 10.0002C0.833496 4.93755 4.93755 0.833496 10.0002 0.833496C15.0628 0.833496 19.1668 4.93755 19.1668 10.0002C19.1668 15.0628 15.0628 19.1668 10.0002 19.1668C4.93755 19.1668 0.833496 15.0628 0.833496 10.0002ZM14.3394 6.91091C14.6649 7.23634 14.6649 7.76398 14.3394 8.08942L9.33942 13.0894C9.01398 13.4149 8.48634 13.4149 8.16091 13.0894L5.66091 10.5894C5.33547 10.264 5.33547 9.73634 5.66091 9.41091C5.98634 9.08547 6.51398 9.08547 6.83942 9.41091L8.75016 11.3217L13.1609 6.91091C13.4863 6.58547 14.014 6.58547 14.3394 6.91091Z"
        fill="#15803D"
      />
    </g>
    <defs>
      <clipPath id="clip0_40000222_15746">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const aPlusIc = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.17214 1.33319H10.8272C11.3639 1.33318 11.8068 1.33318 12.1676 1.36265C12.5423 1.39327 12.8868 1.45898 13.2103 1.62384C13.7121 1.8795 14.12 2.28745 14.3757 2.78922C14.5406 3.11277 14.6063 3.45724 14.6369 3.83197C14.6664 4.19277 14.6663 4.63567 14.6663 5.17231V10.8274C14.6663 11.364 14.6664 11.8069 14.6369 12.1677C14.6063 12.5425 14.5406 12.8869 14.3757 13.2105C14.12 13.7123 13.7121 14.1202 13.2103 14.3759C12.8868 14.5407 12.5423 14.6064 12.1676 14.6371C11.8068 14.6665 11.3639 14.6665 10.8272 14.6665H5.17213C4.63549 14.6665 4.19258 14.6665 3.83179 14.6371C3.45706 14.6064 3.11259 14.5407 2.78903 14.3759C2.28727 14.1202 1.87932 13.7123 1.62366 13.2105C1.4588 12.8869 1.39309 12.5425 1.36247 12.1677C1.33299 11.8069 1.333 11.364 1.33301 10.8274V5.17232C1.333 4.63568 1.33299 4.19277 1.36247 3.83197C1.39309 3.45724 1.4588 3.11277 1.62366 2.78922C1.87932 2.28745 2.28727 1.8795 2.78903 1.62384C3.11259 1.45898 3.45706 1.39327 3.83179 1.36265C4.19259 1.33318 4.6355 1.33318 5.17214 1.33319ZM3.94037 2.69156C3.64809 2.71544 3.49862 2.75872 3.39435 2.81185C3.14347 2.93968 2.9395 3.14365 2.81167 3.39454C2.75854 3.4988 2.71526 3.64827 2.69138 3.94055C2.66686 4.24061 2.66634 4.6288 2.66634 5.19986V10.7999C2.66634 11.3709 2.66686 11.7591 2.69138 12.0592C2.71526 12.3514 2.75854 12.5009 2.81167 12.6052C2.9395 12.8561 3.14347 13.06 3.39435 13.1879C3.49862 13.241 3.64809 13.2843 3.94037 13.3082C4.24043 13.3327 4.62862 13.3332 5.19967 13.3332H10.7997C11.3707 13.3332 11.7589 13.3327 12.059 13.3082C12.3513 13.2843 12.5007 13.241 12.605 13.1879C12.8559 13.06 13.0599 12.8561 13.1877 12.6052C13.2408 12.5009 13.2841 12.3514 13.308 12.0592C13.3325 11.7591 13.333 11.3709 13.333 10.7999V5.19986C13.333 4.62881 13.3325 4.24061 13.308 3.94055C13.2841 3.64827 13.2408 3.4988 13.1877 3.39454C13.0599 3.14365 12.8559 2.93968 12.605 2.81185C12.5007 2.75872 12.3513 2.71544 12.059 2.69156C11.7589 2.66704 11.3707 2.66652 10.7997 2.66652H5.19968C4.62862 2.66652 4.24043 2.66704 3.94037 2.69156Z"
      fill="#030712"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.55831 3.9084C7.8369 3.77118 8.16343 3.77118 8.44202 3.9084C8.68683 4.02898 8.81592 4.24035 8.8785 4.35052C8.94556 4.4686 9.01428 4.61986 9.08263 4.77029C9.08595 4.77761 9.08928 4.78493 9.0926 4.79224L11.9404 11.0574C12.0928 11.3926 11.9446 11.7878 11.6094 11.9402C11.2742 12.0925 10.8789 11.9443 10.7266 11.6091L9.6921 9.33328H6.30823L5.27374 11.6091C5.12138 11.9443 4.72615 12.0925 4.39096 11.9402C4.05577 11.7878 3.90756 11.3926 4.05992 11.0574L6.90772 4.79224C6.91105 4.78492 6.91437 4.7776 6.9177 4.77028C6.98605 4.61985 7.05476 4.4686 7.12183 4.35052C7.18441 4.24035 7.3135 4.02898 7.55831 3.9084ZM6.91429 7.99995H9.08604L8.00016 5.61102L6.91429 7.99995Z"
      fill="#030712"
    />
  </svg>
);
export const homeUpChevron = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.0103 14.2981L11.9998 8.28766L5.98944 14.2981L7.40365 15.7123L11.9998 11.1161L16.596 15.7123L18.0103 14.2981Z"
      fill="#A3A8AF"
    />
  </svg>
);
export const homeDownChevron = (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.98975 9.70193L12.0002 15.7123L18.0106 9.70193L16.5963 8.28772L12.0002 12.8839L7.40396 8.28772L5.98975 9.70193Z"
      fill="#A3A8AF"
    />
  </svg>
);
