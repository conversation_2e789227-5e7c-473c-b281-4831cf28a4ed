import NextAuth from 'next-auth';
import { OAuthConfig } from 'next-auth/providers';
import GoogleProvider from 'next-auth/providers/google';

interface AmazonProfile {
  user_id: string;
  name: string;
  email: string;
  picture: string;
}

const AmazonProvider = (): OAuthConfig<AmazonProfile> => ({
  id: 'amazon',
  name: 'Amazon',
  type: 'oauth',
  authorization: {
    url: 'https://sellercentral.amazon.com/ap/oa',
    params: {
      client_id: process.env.NEXT_PUBLIC_AUTH_AMAZON_ID!,
      scope: 'profile postal_code',
      response_type: 'code',
      redirect_uri:
        process.env.NEXT_PUBLIC_BASE_URL + '/api/auth/callback/amazon',
    },
  },
  token: 'https://sellercentral.amazon.com/auth/o2/token',
  userinfo: 'https://sellercentral.amazon.com/user/profile',
  clientId: process.env.NEXT_PUBLIC_AUTH_AMAZON_ID!,
  clientSecret: process.env.NEXT_PUBLIC_AUTH_AMAZON_SECRET!,
  async profile(profile, tokens) {
    return {
      id: profile.user_id,
      name: profile.name,
      email: profile.email,
      image: profile.picture,
      id_token: tokens.id_token,
    };
  },
});

export const { auth, handlers, signIn, signOut } = NextAuth({
  secret: process.env.AUTH_SECRET,
  trustHost: true,
  providers: [
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID!,
      clientSecret: process.env.NEXT_PUBLIC_AUTH_GOOGLE_SECRET!,
      authorization: {
        params: { scope: 'openid email profile' }, // Ensure full profile access
      },
    }),
    AmazonProvider(),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      if (account && profile) {
        token.id = profile.sub; // Google User ID
        token.idToken = account.id_token;
        token.accessToken = account.access_token; // OAuth Access Token
        token.refreshToken = account.refresh_token; // OAuth Refresh Token
        token.picture = profile.picture; // Profile Image
        token.locale = profile.locale; // Language Preference
        token.given_name = profile.given_name; // First Name
        token.family_name = profile.family_name;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.idToken = token.idToken as string;
        session.user.id = token.id as string;
        session.user.accessToken = token.accessToken as string;
        session.user.refreshToken = token.refreshToken as string;
        session.user.picture = token.picture as string;
        session.user.locale = token.locale as string;
        session.user.given_name = token.given_name as string;
        session.user.family_name = token.family_name as string;
        session.user.provider = token.provider as string;
      }
      return session;
    },
  },
});
