'use client';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <html>
      <body className="flex h-screen items-center justify-center bg-gray-900 text-white">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-red-500 animate-bounce">
            Oops!
          </h1>
          <p className="mt-4 text-lg text-gray-300">Something went wrong...</p>
          <p className="mt-2 text-sm text-gray-500">{error.message}</p>

          <button
            onClick={() => reset()}
            className="mt-6 rounded-lg bg-red-600 px-6 py-3 text-lg font-semibold text-white transition-all duration-300 hover:bg-red-500 focus:ring-4 focus:ring-red-300"
          >
            Try Again
          </button>
        </div>
      </body>
    </html>
  );
}
