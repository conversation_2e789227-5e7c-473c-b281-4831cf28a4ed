'use client';
import { verifyEmail } from '@/utils/api';
import dynamic from 'next/dynamic';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import LanguagePicker from '../../components/LanguagePicker';
const LottieAnimation = dynamic(
  () => import('../../components/LottieAnimation'),
  {
    ssr: false,
  }
);
const AccountCreated = () => {
  const AccountCreatedContent = () => {
    const route = useRouter();
    const searchParams = useSearchParams();
    let key = Array.from(searchParams.keys())[0];

    const [second, setSecond] = useState(10);
    const [isSuccessful, setIsSuccessful] = useState(false);

    useEffect(() => {
      if (!key) {
        setIsSuccessful(true);
      } else {
        key = decodeURIComponent(key);
        const verifyAccount = async () => {
          try {
            await verifyEmail(key);
          } catch (error: any) {
            console.log(error);
            if (
              error.response.status === 401 &&
              error.response.data.meta.is_authenticated === false
            ) {
              setIsSuccessful(true);
            }
          }
        };
        verifyAccount();
      }
    }, []);

    useEffect(() => {
      if (isSuccessful) {
        if (second === 0) {
          route.push('/auth/login');
          return;
        }
        const interval = setInterval(() => {
          setSecond((prevSecond) => prevSecond - 1);
        }, 1000);
        return () => clearInterval(interval);
      }
    }, [isSuccessful, second]);

    const backtoLoginHandler = () => {
      route.push('/auth/login');
    };

    return (
      <div className="min-h-screen text-gray-900 flex justify-center items-center">
        {isSuccessful ? (
          <div className="m-0 min-h-screen bg-white shadow sm:rounded-lg flex justify-center flex-1">
            <LanguagePicker />
            <div className="z-1000 flex flex-col justify-center items-center gap-8 max-w-2xs">
              <div>
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 64 64"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M32 0C14.35 0 0 14.35 0 32C0 49.65 14.35 64 32 64C49.65 64 64 49.65 64 32C64 14.35 49.65 0 32 0Z"
                    fill="#22C55E"
                  />
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M47.4746 21.2117C48.2496 21.9867 48.2496 23.2617 47.4746 24.0367L28.7246 42.7867C28.3371 43.1742 27.8246 43.3742 27.3121 43.3742C26.7996 43.3742 26.2871 43.1742 25.8996 42.7867L16.5246 33.4117C15.7496 32.6367 15.7496 31.3617 16.5246 30.5867C17.2996 29.8117 18.5746 29.8117 19.3496 30.5867L27.3121 38.5492L44.6496 21.2117C45.4246 20.4242 46.6996 20.4242 47.4746 21.2117Z"
                    fill="white"
                  />
                </svg>
              </div>
              <div className="flex flex-col justify-center items-center gap-3">
                <h1 className="font-semibold font-sans text-2xl text-[#020617]">
                  Account created
                </h1>
                <p className="text-sm font-normal font-sans text-[#64748B] text-center">
                  Congratulations! Log in and explore the features of APM now.
                </p>
              </div>
              <button
                onClick={backtoLoginHandler}
                className="mt-5 tracking-wide font-semibold bg-[#FACC15] hover:bg-[#FACC15] text-gray-100 w-full py-4 rounded-lg transition-all duration-300 ease-in-out flex items-center justify-center focus:shadow-outline focus:outline-none"
              >
                <span className="ml-3 text-[#0F172A]">Log in</span>
              </button>
              <div className="w-full border border-solid border-[#E2E8F0]"></div>
              <p className="text-sm font-sans font-normal text-[#64748B] text-center">
                You will be automatically redirected in
                <br />
                <span className="text-sm font-medium text-[#2563EB] font-sans">
                  {second}
                </span>{' '}
                seconds
              </p>
            </div>
            <LottieAnimation />
          </div>
        ) : (
          <div role="status">
            <svg
              aria-hidden="true"
              className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        )}
      </div>
    );
  };
  return (
    <Suspense fallback="Loading...">
      <AccountCreatedContent />
    </Suspense>
  );
};
export default AccountCreated;
