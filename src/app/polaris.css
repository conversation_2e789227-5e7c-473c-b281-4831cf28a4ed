:root {
  --p-color-bg-inverse: #1a1a1a;
  --p-color-text-inverse: #ffffff;
  --p-color-bg-fill-critical: #b91c1c;
  --p-color-text-critical-on-bg-fill: #ffffff;

  --p-color-bg-fill-magic-secondary: #f3f0ff;
  --p-color-text-magic: #5c6ac4;
  --p-color-bg-fill-magic-secondary-hover: #ebe7fc;
  --p-color-bg-fill-magic-secondary-active: #dfd9f7;

  --p-space-200: 8px;
  --p-space-300: 12px;
  --p-space-400: 16px;
  --p-space-500: 20px;
  --p-space-150: 6px;

  --p-border-radius-100: 4px;
  --p-border-radius-200: 8px;

  --p-shadow-400: 0 1px 3px rgba(0, 0, 0, 0.2);
  --p-shadow-500: 0 2px 6px rgba(0, 0, 0, 0.3);
  --p-shadow-bevel-100: 0 0 0 1px rgba(0, 0, 0, 0.05);

  --p-z-index-12: 512;

  --p-motion-duration-200: 100ms;
  --p-motion-duration-400: 200ms;
  --p-motion-ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --p-motion-ease-in: cubic-bezier(0.4, 0, 1, 1);

  --pc-frame-global-ribbon-height: 24px;
}

:root {
  --pc-toast-manager-translate-y-out: 9.375rem;
  --pc-toast-manager-translate-y-in: 0;
  --pc-toast-manager-scale-in: 1;
  --pc-toast-manager-scale-out: 0.9;
  --pc-toast-manager-blur-in: 0;
  --pc-toast-manager-transition-delay-in: 0s;
}

.Polaris-Text--root,
.Polaris-Heading,
.Polaris-TextStyle,
.Polaris-DisplayText {
  font-family: sans-serif !important;
}
.Polaris-Button span {
  text-decoration: underline;
}
.Polaris-Frame-Toast {
  display: inline-flex;
  max-width: 31.25rem;
  padding: var(--p-space-200) var(--p-space-300);
  border-radius: var(--p-border-radius-100);
  background: var(--p-color-bg-inverse);
  color: var(--p-color-text-inverse);
  margin-bottom: var(--p-space-500);
  box-shadow: var(--p-shadow-500);

  position: relative;

  box-shadow: var(--p-shadow-400);

  border-radius: var(--p-border-radius-200);
}

.Polaris-Frame-Toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  box-shadow: var(--p-shadow-bevel-100);
  border-radius: var(--p-border-radius-200);
  pointer-events: none;
  mix-blend-mode: luminosity;
}

@media (min-width: 30.625em) {
  .Polaris-Frame-Toast {
    padding: var(--p-space-300);
  }
}

@media (forced-colors: active) {
  .Polaris-Frame-Toast {
    border: var(--p-border-width-050) solid transparent;
  }
}

.Polaris-Frame-Toast__Action {
  margin-left: var(--p-space-400);
  color: var(--p-color-text-inverse);
}

.Polaris-Frame-Toast--error {
  background: var(--p-color-bg-fill-critical);
  color: var(--p-color-text-critical-on-bg-fill);
}

.Polaris-Frame-Toast--error .Polaris-Frame-Toast__CloseButton {
  color: var(--p-color-text-critical-on-bg-fill);
}

.Polaris-Frame-Toast__LeadingIcon {
  margin-right: var(--p-space-150);
}

.Polaris-Frame-Toast__CloseButton {
  display: flex;
  align-self: center;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0;
  border: none;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  color: var(--p-color-icon-inverse);
  cursor: pointer;
  margin-left: var(--p-space-200);
}

.Polaris-Frame-Toast__CloseButton:focus {
  outline: none;
}

.Polaris-Frame-Toast__CloseButton:focus,
.Polaris-Frame-Toast__CloseButton:hover {
  color: var(--p-color-text-inverse);
}

.Polaris-Frame-Toast--toneMagic {
  background-color: var(--p-color-bg-fill-magic-secondary);
  color: var(--p-color-text-magic);
}

.Polaris-Frame-Toast--toneMagic .Polaris-Frame-Toast__CloseButton {
  color: var(--p-color-text-magic);
}

.Polaris-Frame-Toast--toneMagic .Polaris-Frame-Toast__Action {
  color: var(--p-color-text-magic);
}

.Polaris-Frame-Toast__WithActionOnComponent {
  border: none;
  cursor: pointer;
  padding-right: var(--p-space-500);
}

.Polaris-Frame-Toast__WithActionOnComponent.Polaris-Frame-Toast--toneMagic:focus,
.Polaris-Frame-Toast__WithActionOnComponent.Polaris-Frame-Toast--toneMagic:hover {
  background-color: var(--p-color-bg-fill-magic-secondary-hover);
}

.Polaris-Frame-Toast__WithActionOnComponent.Polaris-Frame-Toast--toneMagic:active {
  background-color: var(--p-color-bg-fill-magic-secondary-active);
}

:root {
  --pc-toast-manager-translate-y-out: 9.375rem;
  --pc-toast-manager-translate-y-in: 0;
  --pc-toast-manager-scale-in: 1;
  --pc-toast-manager-scale-out: 0.9;
  --pc-toast-manager-blur-in: 0;
  --pc-toast-manager-transition-delay-in: 0s;
}

.Polaris-Frame-ToastManager {
  position: fixed;
  z-index: var(--p-z-index-12);
  right: 0;
  left: 0;
  text-align: center;
  bottom: var(--pc-frame-global-ribbon-height);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.Polaris-Frame-ToastManager__ToastWrapper {
  position: absolute;
  display: inline-flex;
  opacity: 0;
  transition:
    transform var(--p-motion-duration-400) var(--p-motion-ease-out),
    opacity var(--p-motion-duration-400) var(--p-motion-ease-out);
  transform: translateY(var(--pc-toast-manager-translate-y-out));
}

.Polaris-Frame-ToastManager__ToastWrapper--enter,
.Polaris-Frame-ToastManager__ToastWrapper--exit {
  transition-timing-function: var(--p-motion-ease-in);
  transform: translateY(var(--pc-toast-manager-translate-y-out))
    scale(var(--pc-toast-manager-scale-out));
  opacity: 0;
}

.Polaris-Frame-ToastManager__ToastWrapper--exit {
  transition-duration: var(--p-motion-duration-200);
}

.Polaris-Frame-ToastManager--toastWrapperEnterDone {
  transform: translateY(var(--pc-toast-manager-translate-y-in))
    scale(var(--pc-toast-manager-scale-in));
  filter: blur(var(--pc-toast-manager-blur-in));
  opacity: 1;
  transition-delay: var(--pc-toast-manager-transition-delay-in);
}

.Polaris-Frame-ToastManager--toastWrapperHoverable {
  cursor: pointer;
}
.Polaris-Frame-Toast__CloseButton svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
  display: inline-block !important;
}
