import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Module {
  id: string;
  type: string;
  data: any;
}

interface APlusContentState {
  currentContent: {
    id?: string;
    name: string;
    status: 'draft' | 'active' | 'pending' | 'rejected';
    language: string;
    modules: Module[];
    asins: string[];
    lastModified?: string;
    created?: string;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: APlusContentState = {
  currentContent: {
    name: 'Create new A+ Content',
    status: 'draft',
    language: 'UK English',
    modules: [],
    asins: [],
  },
  isLoading: false,
  error: null,
};

export const aplusContentSlice = createSlice({
  name: 'aplus-content',
  initialState,
  reducers: {
    setContentName: (state, action: PayloadAction<string>) => {
      state.currentContent.name = action.payload;
    },
    
    addModule: (state, action: PayloadAction<Module>) => {
      state.currentContent.modules.push(action.payload);
    },
    
    updateModule: (state, action: PayloadAction<{ id: string; data: any }>) => {
      const moduleIndex = state.currentContent.modules.findIndex(
        module => module.id === action.payload.id
      );
      if (moduleIndex !== -1) {
        state.currentContent.modules[moduleIndex].data = {
          ...state.currentContent.modules[moduleIndex].data,
          ...action.payload.data
        };
      }
    },
    
    removeModule: (state, action: PayloadAction<string>) => {
      state.currentContent.modules = state.currentContent.modules.filter(
        module => module.id !== action.payload
      );
    },
    
    reorderModules: (state, action: PayloadAction<Module[]>) => {
      state.currentContent.modules = action.payload;
    },
    
    setModules: (state, action: PayloadAction<Module[]>) => {
      state.currentContent.modules = action.payload;
    },
    
    setContentStatus: (state, action: PayloadAction<'draft' | 'active' | 'pending' | 'rejected'>) => {
      state.currentContent.status = action.payload;
    },
    
    setContentLanguage: (state, action: PayloadAction<string>) => {
      state.currentContent.language = action.payload;
    },
    
    addAsin: (state, action: PayloadAction<string>) => {
      if (!state.currentContent.asins.includes(action.payload)) {
        state.currentContent.asins.push(action.payload);
      }
    },
    
    removeAsin: (state, action: PayloadAction<string>) => {
      state.currentContent.asins = state.currentContent.asins.filter(
        asin => asin !== action.payload
      );
    },
    
    setAsins: (state, action: PayloadAction<string[]>) => {
      state.currentContent.asins = action.payload;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    resetContent: (state) => {
      state.currentContent = {
        name: 'Create new A+ Content',
        status: 'draft',
        language: 'UK English',
        modules: [],
        asins: [],
      };
      state.error = null;
    },
    
    loadContent: (state, action: PayloadAction<APlusContentState['currentContent']>) => {
      state.currentContent = action.payload;
    },
    
    saveContentSuccess: (state, action: PayloadAction<{ id: string; lastModified: string }>) => {
      state.currentContent.id = action.payload.id;
      state.currentContent.lastModified = action.payload.lastModified;
      state.isLoading = false;
      state.error = null;
    },
  },
});

export const {
  setContentName,
  addModule,
  updateModule,
  removeModule,
  reorderModules,
  setModules,
  setContentStatus,
  setContentLanguage,
  addAsin,
  removeAsin,
  setAsins,
  setLoading,
  setError,
  resetContent,
  loadContent,
  saveContentSuccess,
} = aplusContentSlice.actions;

export default aplusContentSlice.reducer;
