import { configureStore } from '@reduxjs/toolkit';
import productListingReducer from './product-listing';
import uiReducer from './ui';
import aplusContentReducer from './aplus-content';

export const store = configureStore({
  reducer: {
    ui: uiReducer,
    productListing: productListingReducer,
    aplusContent: aplusContentReducer,
  },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
