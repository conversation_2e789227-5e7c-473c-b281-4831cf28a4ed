import { createSlice } from '@reduxjs/toolkit';

interface ProductListingState {
  hatForm: any;
}
const initialProductListState: ProductListingState = {
  hatForm: {},
};

export const productListingSlice = createSlice({
  name: 'product-listing',
  initialState: initialProductListState,
  reducers: {
    updateHatForm: (state, action) => {
      state.hatForm = action.payload;
      console.log('hatForm: ', action.payload);
    },
  },
});

export const { updateHatForm } = productListingSlice.actions;

export default productListingSlice.reducer;
