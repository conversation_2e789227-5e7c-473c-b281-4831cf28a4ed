import { createSlice } from '@reduxjs/toolkit';

interface UIState {
  isLoading: boolean;
  showSettingSidebar: boolean;
  showHomeSidebar: boolean;
  isUnsaved: boolean;
  selectedAttribute: string;
  attributeFilter: 'all' | 'required' | 'recommended';
}
const initialUIState: UIState = {
  isLoading: false,
  showSettingSidebar: true,
  showHomeSidebar: false,
  isUnsaved: false,
  selectedAttribute: 'all-attributes',
  attributeFilter: 'all',
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState: initialUIState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    showSettingSidebarHandler: (state, action) => {
      state.showSettingSidebar = action.payload;
    },
    showHomeSidebarHandler: (state, action) => {
      state.showHomeSidebar = action.payload;
    },
    setIsUnsaved: (state, action) => {
      state.isUnsaved = action.payload;
    },
    setAttributeFilter: (state, action) => {
      state.attributeFilter = action.payload;
    },
  },
});

export const {
  setLoading,
  showSettingSidebarHandler,
  showHomeSidebarHandler,
  setIsUnsaved,
  setAttributeFilter,
} = uiSlice.actions;

export default uiSlice.reducer;
