'use client';
import { useAppDispatch } from '@/app/hook';
import { showHomeSidebarHandler } from '@/app/store/ui';
import { Button } from '@/components/Button';
import SidebarFilterPanel from '@/components/filters/SidebarFilterPanel';
import Tooltip from '@/components/Tooltip';
import {
  add,
  columnCustomize,
  downArrow,
  dropdown,
  filter,
  flex,
  forwardArrow,
  grid,
  search,
  sorting,
  upArrow,
} from '@/utils/icon';
import { categories, products } from '@/utils/mock';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import ColumnCustomization from './components/ColumnCustomization';
import Empty from './components/Empty';
import ProductCard from './components/ProductCard';
import ProductTable from './components/ProductTable';
import { statusColors } from './const';

export interface Column {
  id: string;
  name: string;
  selected: boolean;
}

const YourStore = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const [viewMode, setViewMode] = useState('grid');
  const [showSortOptions, setShowSortOptions] = useState(false);
  const [showCategoryOptions, setShowCategoryOptions] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([
    'Tools & Home Improvement',
    'Sports and Outdoors',
  ]);
  const [searchCategory, setSearchCategory] = useState('');
  const [sortField, setSortField] = useState('Item name');
  const [sortDirection, setSortDirection] = useState('increase');
  const [showSearchFilter, setShowSearchFilter] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showStatusFilter, setShowStatusFilter] = useState(false);
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedDatePeriod, setSelectedDatePeriod] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [dateOption, setDateOption] = useState('');
  const [showColumnCustomize, setShowColumnCustomize] = useState(false);
  const [availableColumns, setAvailableColumns] = useState<Column[]>([
    { id: 'product', name: 'Product', selected: true },
    { id: 'asin', name: 'ASIN', selected: true },
    { id: 'sku', name: 'SKU', selected: true },
    { id: 'performance', name: 'Performance', selected: true },
    { id: 'status', name: 'Status', selected: true },
    { id: 'createdDate', name: 'Created date', selected: true },
    { id: 'lastUpdate', name: 'Latest update', selected: true },
  ]);
  const [showFilterSidebar, setShowFilterSidebar] = useState(false);
  const [activeFilters, setActiveFilters] = useState({
    statuses: [] as string[],
    performance: [] as string[],
    dateRange: { startDate: '', endDate: '' },
    updatedDateRange: { startDate: '', endDate: '' },
  });

  const sortRef = useRef<HTMLDivElement>(null);
  const categoryRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sortRef.current && !sortRef.current.contains(event.target as Node)) {
        setShowSortOptions(false);
      }
      if (
        categoryRef.current &&
        !categoryRef.current.contains(event.target as Node)
      ) {
        setShowCategoryOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const createNewListingHandler = () => {
    dispatch(showHomeSidebarHandler(false));
    router.push('/home/<USER>/create-listing');
  };

  /**
   * FUNCTIONS
   */
  // Toggle sort options dropdown
  const toggleSortOptions = () => {
    setShowSortOptions(!showSortOptions);
    if (showSearchFilter) setShowSearchFilter(false);
  };

  // Handle sort field selection
  const sortFieldChangeHandler = (field: string) => {
    setSortField(field);
  };

  // Handle sort direction selection
  const sortDirectionChangeHandler = (direction: string) => {
    setSortDirection(direction);
  };

  // Toggle category options dropdown
  const toggleCategoryOptions = () => {
    setShowCategoryOptions(!showCategoryOptions);
    if (showSortOptions) setShowSortOptions(false);
    if (showSearchFilter) setShowSearchFilter(false);
  };

  // Handle category selection
  const categorySelectHandler = (category: string) => {
    if (category === 'All') {
      if (selectedCategories.length == categories.length - 1) {
        setSelectedCategories([]);
      } else {
        setSelectedCategories(categories.slice(1));
      }
    } else {
      if (selectedCategories.includes(category)) {
        setSelectedCategories(
          selectedCategories.filter((cat) => cat !== category)
        );
      } else {
        setSelectedCategories([...selectedCategories, category]);
      }
    }
  };

  const statusFilterHandler = () => {
    setShowStatusFilter(!showStatusFilter);
    setShowDateFilter(false);
  };

  // Update status filter handler
  const selectFilterOptionsHandler = (status: string) => {
    if (status === 'All') {
      // Select all statuses
      setSelectedStatus(['Complete', 'Pending', 'Draft']);
    } else if (selectedStatus.includes(status)) {
      // Remove status if already selected
      setSelectedStatus(selectedStatus.filter((s) => s !== status));
    } else {
      // Add status to selection
      setSelectedStatus([...selectedStatus, status]);
    }
  };

  // Update clear function
  const clearStatusFilterHandler = () => {
    setSelectedStatus([]);
  };

  const dateFilterHandler = () => {
    setShowDateFilter(!showDateFilter);
    setShowStatusFilter(false);
  };

  const cancelFilterAndSearchHandler = () => {
    setSearchQuery('');
    setSelectedStatus([]);
    setSelectedDatePeriod('');
    setDateOption('');
    setStartDate('');
    setEndDate('');
    setShowSearchFilter(false);
    setShowDateFilter(false);
    setShowStatusFilter(false);
  };

  // Handle date option selection
  const selectDateOption = (option: string) => {
    setDateOption(option);
    setSelectedDatePeriod(option);
    // Set date range based on selected option
    const today = new Date();
    let start = new Date();
    let end = new Date();

    switch (option) {
      case 'Today':
        // Start and end are both today
        break;
      case 'Last 7 days':
        start.setDate(today.getDate() - 7);
        break;
      case 'Last 30 days':
        start.setDate(today.getDate() - 30);
        break;
      case 'Last 90 days':
        start.setDate(today.getDate() - 90);
        break;
      case 'Last 12 months':
        start.setMonth(today.getMonth() - 12);
        break;
      case 'Custom':
        return;
      default:
        break;
    }

    // Format dates as DD-MM-YYYY
    setStartDate(formatDateString(start));
    setEndDate(formatDateString(end));
    setShowDateFilter(false);
  };

  // Format date as DD-MM-YYYY
  const formatDateString = (date: Date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Clear date filter
  const clearDateFilter = () => {
    setSelectedDatePeriod('');
    setStartDate('');
    setEndDate('');
    setDateOption('');
    setShowDateFilter(false);
  };

  // Toggle column customize modal
  const toggleColumnCustomize = () => {
    setShowColumnCustomize(!showColumnCustomize);
  };

  // Toggle column selection
  const toggleColumnSelection = (columnId: string) => {
    setAvailableColumns(
      availableColumns.map((col) =>
        col.id === columnId ? { ...col, selected: !col.selected } : col
      )
    );
  };

  // Get selected columns
  const selectedColumns = availableColumns.filter((col) => col.selected);

  // Add this function to the YourStore component
  const updateColumnOrder = (reorderedColumns: Column[]) => {
    setAvailableColumns(reorderedColumns);
  };

  // Add this function to handle applying filters
  const applyFilters = (filters: any) => {
    setActiveFilters(filters);
    // Here you would typically filter your products based on the selected filters
    console.log('Applied filters:', filters);
  };

  // Add this function to clear all filters
  const clearAllFilters = () => {
    setActiveFilters({
      statuses: [],
      performance: [],
      dateRange: { startDate: '', endDate: '' },
      updatedDateRange: { startDate: '', endDate: '' },
    });
  };

  /**
   * VARIABLES
   */
  let sortDirectionIncreaseText = 'Ascending';
  let sortDirectionDecreaseText = 'Descending';
  if (
    sortField === 'Item name' ||
    sortField === 'SKU' ||
    sortField === 'ASIN'
  ) {
    sortDirectionIncreaseText = 'A-Z';
    sortDirectionDecreaseText = 'Z-A';
  }
  if (sortField === 'Latest update' || sortField === 'Created date') {
    sortDirectionIncreaseText = 'Oldest to newest';
    sortDirectionDecreaseText = 'Newest to oldest';
  }
  // Filter categories
  const filteredCategories = categories.filter((category) =>
    category.toLowerCase().includes(searchCategory.toLowerCase())
  );

  /**
   * COMPONENTS
   */
  const ToggleViewButtons = (
    <>
      <div className="relative group inline-block">
        <button
          className={`rounded-l-lg border-1 border-[#CBD5E1] ${viewMode === 'flex' ? 'bg-[#1E293B]' : 'bg-white'} p-1`}
          onClick={() => setViewMode('flex')}
        >
          {flex(viewMode === 'flex' ? '#FFFFFF' : '#000000')}
        </button>
        <Tooltip className="right-1/2 bottom-1/2">Flex</Tooltip>
      </div>
      <div className="relative group inline-block">
        <button
          className={`rounded-r-lg border-1 border-[#CBD5E1] ${viewMode === 'grid' ? 'bg-[#1E293B]' : 'bg-white'} p-1`}
          onClick={() => setViewMode('grid')}
        >
          {grid(viewMode === 'grid' ? '#FFFFFF' : '#000000')}
          <Tooltip className="right-1/2 bottom-1/2">Grid</Tooltip>
        </button>
      </div>
    </>
  );

  return (
    <div className="mt-20 p-6 w-full flex flex-col gap-4 overflow-auto">
      {/* Header */}
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-[#0F172A]">
            Hello APM Holdings Official
          </h1>
          {products.length > 0 && (
            <div className="text-sm font-normal text-[#475569] lg:flex lg:gap-2">
              <p>
                You have total&nbsp;<span className="font-medium">65</span>
                &nbsp;active listings.
              </p>
              <span className="font-medium text-[#1E40AF] flex gap-2 items-center">
                View on Amazon {forwardArrow}
              </span>
            </div>
          )}
          {products.length == 0 && (
            <div className="text-sm font-normal text-[#475569]">
              <p>You have no active listing.</p>
            </div>
          )}
        </div>
        {products.length > 0 && (
          <div className="flex gap-2 items-start">
            <Button className="px-3 text-sm bg-white hover:bg-white !mt-0 border-1 border-[#CBD5E1]">
              Export
            </Button>
            <Button className="px-3 text-sm bg-white hover:bg-white !mt-0  border-1 border-[#CBD5E1]">
              Import
            </Button>
            <Button className="px-3 text-sm bg-white hover:bg-white !mt-0 whitespace-nowrap  border-1 border-[#CBD5E1]">
              <span className="flex gap-1 items-center">
                More actions <span className="ml-1">{dropdown}</span>
              </span>
            </Button>
            <Button
              className="px-3 text-sm whitespace-nowrap !mt-0"
              onClickFunc={createNewListingHandler}
            >
              Create new listing
            </Button>
          </div>
        )}
        {products.length == 0 && <div>{ToggleViewButtons}</div>}
      </div>

      {/* Categories */}
      {products.length > 0 && (
        <>
          <div className="flex justify-between">
            <div className="relative" ref={categoryRef}>
              <button
                className="bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-lg focus:outline-none flex gap-2 items-center"
                onClick={toggleCategoryOptions}
              >
                {selectedCategories.length > 1
                  ? `${selectedCategories[0]}, ${selectedCategories.length - 1} more`
                  : selectedCategories[0] || 'Select Categories'}
                {dropdown}
              </button>

              {showCategoryOptions && (
                <div className="absolute left-0 mt-2 w-[500px] bg-white rounded-lg shadow-lg z-20 p-4">
                  <div className="relative mb-4">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      {search}
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="Search category"
                      value={searchCategory}
                      onChange={(e) => setSearchCategory(e.target.value)}
                    />
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {filteredCategories.map((category) => (
                      <div
                        key={category}
                        onClick={() => categorySelectHandler(category)}
                        className={`px-4 py-2 rounded-full cursor-pointer transition-colors ${
                          selectedCategories.includes(category)
                            ? 'bg-[#1E293B] text-white'
                            : 'bg-white border border-gray-300 hover:bg-gray-100'
                        }`}
                      >
                        {category}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div>
              {viewMode === 'flex' && (
                <div className="relative group inline-block">
                  <button
                    className="rounded-lg border-1 border-[#CBD5E1] bg-white p-1 mr-2"
                    onClick={toggleColumnCustomize}
                  >
                    {columnCustomize}
                  </button>
                  <Tooltip className="right-1/2 bottom-1/2">
                    Column Customize
                  </Tooltip>
                </div>
              )}

              <div className="relative inline-block" ref={sortRef}>
                <div className="relative group inline-block">
                  <button
                    className="rounded-lg border-1 border-[#CBD5E1] bg-white p-1 mr-2"
                    onClick={toggleSortOptions}
                  >
                    {sorting}
                  </button>
                  <Tooltip className="right-1/2 bottom-1/2">Sorting</Tooltip>
                </div>

                {showSortOptions && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg z-20">
                    <div className="p-4">
                      <h2 className="text-sm font-medium mb-4">Sort by</h2>

                      <div className="space-y-4">
                        {/* Sort fields */}
                        {[
                          'Item name',
                          'Stock',
                          'Latest update',
                          'Created date',
                          'Performance',
                          'Status',
                          'ASIN',
                          'SKU',
                        ].map((field) => (
                          <div key={field} className="flex items-center">
                            <div
                              className={`w-5 h-5 rounded-full border-2 border-black flex items-center justify-center cursor-pointer bg-white`}
                              onClick={() => sortFieldChangeHandler(field)}
                            >
                              {sortField === field && (
                                <div className="w-3 h-3 bg-black rounded-full"></div>
                              )}
                            </div>
                            <span className="ml-3 text-sm font-normal">
                              {field}
                            </span>
                          </div>
                        ))}
                      </div>

                      <div className="border-t my-4"></div>

                      {/* Sort direction */}
                      <div className="space-y-2">
                        <div
                          className={`flex items-center py-2 rounded-lg cursor-pointer ${sortDirection === 'increase' ? 'bg-gray-200' : 'bg-white'}`}
                          onClick={() => sortDirectionChangeHandler('increase')}
                        >
                          {upArrow}
                          <span className="ml-3 text-sm">
                            {sortDirectionIncreaseText}
                          </span>
                        </div>

                        <div
                          className={`flex items-center py-2 rounded-lg cursor-pointer ${sortDirection === 'decrease' ? 'bg-gray-200' : 'bg-white'}`}
                          onClick={() => sortDirectionChangeHandler('decrease')}
                        >
                          {downArrow}
                          <span className="ml-3 text-sm">
                            {sortDirectionDecreaseText}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {ToggleViewButtons}
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <ul className="flex gap-2 text-sm font-normal text-[#0F172A]">
                <li className="py-2 px-3 rounded-3xl bg-[#1E293B] text-[#FEF9C3]">
                  All
                </li>
                <li className="py-2 px-3 cursor-pointer">Active</li>
                <li className="py-2 px-3 cursor-pointer">Inactive</li>
                <li className="py-2 px-3 cursor-pointer">Suspend </li>
                <li className="py-2 px-3 cursor-pointer">Archieved</li>
                <li className="py-2 px-3 cursor-pointer">{add}</li>
              </ul>
            </div>
            {!showSearchFilter && (
              <div className="relative inline-block group">
                <div
                  className="bg-white rounded-lg flex items-center px-2 gap-1 border-1 border-[#CBD5E1] h-full"
                  onClick={() => setShowSearchFilter(!showSearchFilter)}
                >
                  <button>{search}</button>
                  <button>{filter}</button>
                </div>
                <Tooltip className="right-1/2 bottom-1/2">
                  Search & Filter
                </Tooltip>
              </div>
            )}
          </div>
        </>
      )}

      {/* Search filter */}
      {showSearchFilter && (
        <div className="right-0 mt-2 w-full rounded-lg z-20 search-filter-container">
          <div className="flex gap-4 justify-between items-center">
            {/* Search input */}
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {search}
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search by ASIN, SKU or product name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Filter options */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <button
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 rounded-lg bg-white"
                  onClick={statusFilterHandler}
                >
                  Status <span className="ml-1">{dropdown}</span>
                </button>
                {showStatusFilter && (
                  <div className="absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg z-30 p-2">
                    {['Complete', 'Pending', 'Draft'].map((status) => (
                      <div
                        key={status}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer rounded-md flex items-center"
                        onClick={() => selectFilterOptionsHandler(status)}
                      >
                        <input
                          type="checkbox"
                          className="mr-2"
                          checked={selectedStatus.includes(status)}
                          readOnly
                        />
                        <div className="flex items-center">
                          <span
                            className={`w-2 h-2 ${statusColors[status].dot} rounded-full mr-2`}
                          ></span>
                          <span className={`${statusColors[status].text}`}>
                            {status}
                          </span>
                        </div>
                      </div>
                    ))}
                    <div
                      className={`pt-2 ${selectedStatus.length > 0 ? 'text-[#1E40AF]' : 'text-[#94A3B8]'}`}
                    >
                      <button
                        onClick={clearStatusFilterHandler}
                        className="px-3 py-1 text-sm"
                      >
                        Clear
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="relative">
                <button
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 rounded-lg bg-white"
                  onClick={dateFilterHandler}
                >
                  Date <span className="ml-1">{dropdown}</span>
                </button>
                {showDateFilter && (
                  <div className="absolute left-0 mt-1 w-64 bg-white rounded-lg shadow-lg z-30 p-3">
                    <div className="space-y-2">
                      {[
                        'Today',
                        'Last 7 days',
                        'Last 30 days',
                        'Last 90 days',
                        'Last 12 months',
                        'Custom',
                      ].map((period) => (
                        <div
                          key={period}
                          className="flex items-center py-2 hover:bg-gray-100 cursor-pointer rounded-md"
                          onClick={() => selectDateOption(period)}
                        >
                          <div className="w-5 h-5 rounded-full border border-[#030712] flex items-center justify-center mr-2">
                            {dateOption === period && (
                              <div className="w-3 h-3 bg-[#030712] rounded-full"></div>
                            )}
                          </div>
                          <span>{period}</span>
                        </div>
                      ))}
                    </div>

                    {dateOption === 'Custom' && (
                      <div className="mt-3 space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Starting
                          </label>
                          <input
                            type="date"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Ending
                          </label>
                          <input
                            type="date"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                          />
                        </div>
                      </div>
                    )}

                    <div className="mt-3 flex">
                      <button
                        onClick={clearDateFilter}
                        className={`py-1 text-sm ${dateOption !== '' ? 'text-[#1E40AF]' : 'text-[#94A3B8]'}`}
                      >
                        Clear
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div className="relative">
                <button
                  className="flex items-center gap-1 px-3 py-2 border border-gray-300 rounded-lg bg-white whitespace-nowrap"
                  onClick={() => setShowFilterSidebar(!showFilterSidebar)}
                >
                  All filter <span className="ml-1">{dropdown}</span>
                </button>
              </div>
            </div>
            <button
              className="text-blue-600 hover:text-blue-800 font-medium"
              onClick={cancelFilterAndSearchHandler}
            >
              Cancel
            </button>
          </div>
          {/* Active filters */}
          {(selectedStatus.length > 0 || selectedDatePeriod) && (
            <div className="flex gap-2 items-center mt-2">
              {selectedStatus.length > 0 && (
                <div className="flex items-center gap-1 px-2 py-1 bg-[#EFF6FF] border border-[#93C5FD] rounded-xl">
                  Status: {selectedStatus.join(', ')}
                  <button
                    className="ml-1 text-gray-500 hover:text-gray-700"
                    onClick={() => setSelectedStatus([])}
                  >
                    ×
                  </button>
                </div>
              )}

              {selectedDatePeriod && (
                <div className="flex items-center gap-1 px-3 py-1 bg-[#EFF6FF] border border-[#93C5FD] rounded-xl">
                  Created date:{' '}
                  {dateOption === 'Custom'
                    ? `${startDate} - ${endDate}`
                    : selectedDatePeriod}
                  <button
                    className="ml-1 text-gray-500 hover:text-gray-700"
                    onClick={() => {
                      setSelectedDatePeriod('');
                      setDateOption('');
                      setStartDate('');
                      setEndDate('');
                    }}
                  >
                    ×
                  </button>
                </div>
              )}

              <button
                className="text-blue-600 hover:text-blue-800"
                onClick={() => {
                  setSelectedStatus([]);
                  setSelectedDatePeriod('');
                }}
              >
                Clear
              </button>
            </div>
          )}
        </div>
      )}

      {/* Render products */}
      {products.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="rounded-lg bg-white shadow-lg">
            <h1 className="text-lg font-medium px-4 pt-4">
              Tools & Home Improvement
            </h1>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mt-4">
                {products.map((product) => (
                  <ProductCard
                    key={product.id}
                    image={product.image}
                    lastUpdate="Apr 4, 2025"
                    title={product.title}
                    status={product.status}
                    completionStatus={product.completionStatus}
                    stock={product.stock}
                    isBestSeller={product.isBestSeller}
                    isAPlusContent={product.isAPlusContent}
                  />
                ))}
              </div>
            </div>
          </div>
        ) : (
          <ProductTable products={products} columns={availableColumns} />
        )
      ) : (
        <Empty viewMode={viewMode} />
      )}

      {/* Column Customize Modal */}
      {showColumnCustomize && (
        <ColumnCustomization
          toggleColumnCustomize={toggleColumnCustomize}
          availableColumns={availableColumns}
          selectedColumns={selectedColumns}
          toggleColumnSelection={toggleColumnSelection}
          updateColumnOrder={updateColumnOrder}
        />
      )}

      {/* Sidebar Filter Panel */}
      {showFilterSidebar && (
        <SidebarFilterPanel
          onClose={() => setShowFilterSidebar(false)}
          onApply={applyFilters}
          initialFilters={activeFilters}
        />
      )}
    </div>
  );
};

export default YourStore;
