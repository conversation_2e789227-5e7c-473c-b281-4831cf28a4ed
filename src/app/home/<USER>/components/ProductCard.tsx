'use client';

import DropDownMenu from '@/components/DropDownMenu';
import DeleteProductModal from '@/components/modal/DeleteProduct';
import ProductPreview from '@/components/modal/PreviewProduct';
import Tooltip from '@/components/Tooltip';
import { aPlusIc, deleteIc, editIc, eyeon, option } from '@/utils/icon';
import Image from 'next/image';
import { useState } from 'react';

interface ProductCardProps {
  image: string;
  lastUpdate: string;
  title: string;
  status: string;
  completionStatus: string;
  stock?: number;
  sku?: string;
  isAPlusContent?: boolean;
  isBestSeller?: boolean;
}

const ProductCard = ({
  image,
  lastUpdate,
  title,
  status,
  completionStatus,
  stock,
  sku,
  isAPlusContent = false,
  isBestSeller = false,
}: ProductCardProps) => {
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Status color mapping
  const statusColors = {
    'Excellent listing': 'bg-green-100 text-green-800',
    'Good listing': 'bg-blue-100 text-blue-800',
    'Critical issues': 'bg-red-100 text-red-800',
    'Need improvement': 'bg-yellow-100 text-yellow-800',
    'Poor listing': 'bg-[#FFD6A4] text-[#5E4200]',
  };

  // Completion status indicator color
  const completionColors = {
    Complete: 'bg-green-500',
    Draft: 'bg-gray-400',
    Pending: 'bg-yellow-500',
  };

  const confirmDelete = () => {
    setShowDeleteModal(false);
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
  };

  // Handle actions
  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleEdit = () => {
    // Handle edit action
  };

  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  const closePreview = () => {
    setShowPreview(false);
  };

  return (
    <>
      <div className="bg-white rounded-lg overflow-hidden shadow-sm">
        <div className="relative">
          {/* Product image */}
          <div className="w-full aspect-square relative">
            {(isAPlusContent || isBestSeller) && (
              <div className="absolute flex gap-1 z-10">
                {isAPlusContent && (
                  <span className="bg-[#EAB308] px-2 py-1 rounded-br-2xl relative group">
                    {aPlusIc}
                    <Tooltip className="left-full top-1/2 whitespace-nowrap">
                      A+ Content Active
                    </Tooltip>
                  </span>
                )}
                {isBestSeller && (
                  <span className="bg-[#1E3A8A] text-white text-xs font-bold px-2 py-1 mt-1 rounded-full">
                    Best Seller
                  </span>
                )}
              </div>
            )}
            <Image
              height={100}
              width={100}
              src={'/assets/products/' + image}
              alt={title}
              className={`w-full h-full object-cover rounded-2xl ${isBestSeller || isAPlusContent ? 'border-2 border-[#EAB308]' : ''}`}
            />

            {/* Options dropdown */}
            <div className="absolute top-2 right-2 z-20">
              <DropDownMenu
                trigger={
                  <div className="bg-white rounded-full p-1 shadow-sm">
                    {option}
                  </div>
                }
                position="top-right"
              >
                <button
                  className="flex items-center justify-between w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={handlePreview}
                >
                  <span className="">Preview</span>
                  {eyeon}
                </button>
                <button
                  className="flex items-center justify-between w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={handleEdit}
                >
                  <span className="text-[#1E40AF]">Edit</span>
                  {editIc()}
                </button>
                <button
                  className="flex items-center justify-between w-full px-4 py-3 text-sm text-red-600 hover:bg-gray-100"
                  onClick={handleDelete}
                >
                  <span className="">Delete</span>
                  {deleteIc()}
                </button>
              </DropDownMenu>
            </div>
          </div>
        </div>

        {/* Product details */}
        <div className="p-3">
          {/* Last update date */}
          <div className="text-xs text-gray-500">
            {'Last update • ' + lastUpdate}
          </div>

          {/* Product title */}
          <h3 className="text-sm font-medium mt-1 line-clamp-3">{title}</h3>

          {/* Status badge */}
          <div
            className={`mt-2 ${statusColors[status]} text-xs px-2 py-1 rounded inline-block`}
          >
            {status}
          </div>

          {/* Completion status */}
          <div className="mt-2 flex items-center gap-2">
            <div
              className={`w-2 h-2 ${completionColors[completionStatus]} rounded-full`}
            ></div>
            <span className="text-xs text-gray-700">{completionStatus}</span>
          </div>

          {/* Stock information */}
          {stock !== undefined && stock <= 10 && (
            <div className="mt-2 text-xs text-red-500">
              Only {stock} left in stock
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <ProductPreview
          product={{
            title,
            image,
            sku,
            price: 32,
            brand: 'Utopia Home',
            color: 'White',
            material: 'Plastic',
            dimensions: '16.1"W x 3.2"H',
            features: [
              'Clothes hanger',
              'Plastic hangers',
              'Heavy Duty',
              'Space Saving Hangers',
              'Closet Organizers and Storage',
            ],
            description:
              'Perfect for clothes hangers, closet organizers and storage',
          }}
          onClose={closePreview}
          onEdit={handleEdit}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <DeleteProductModal
          product={{
            title,
            image,
            sku,
          }}
          onClose={closeDeleteModal}
          onDelete={confirmDelete}
        />
      )}
    </>
  );
};

export default ProductCard;
