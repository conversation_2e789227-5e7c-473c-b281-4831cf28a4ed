interface Props {
  viewMode: string;
}
const Empty = ({ viewMode }: Props) => {
  const createNewListingHandler = () => {};

  return (
    <div className="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center h-full">
      {viewMode === 'grid' ? (
        // Grid view placeholder
        <div className="grid grid-cols-5 gap-4 mb-12 w-full animate-pulse">
          {[1, 2, 3, 4, 5].map((item) => (
            <div
              key={item}
              className="bg-gray-100 rounded-lg h-full min-h-60 w-full"
            ></div>
          ))}
        </div>
      ) : (
        // Flex view placeholder (table)
        <div className="w-full mb-12 animate-pulse">
          {/* Table header */}
          <div className="flex pb-4 mb-4">
            <div className="w-8 mr-2">
              <div className="h-5 w-5 bg-gray-100 rounded"></div>
            </div>
            <div className="flex-1 mr-4">
              <div className="h-5 bg-gray-100 rounded w-32"></div>
            </div>
            <div className="w-24 mr-4">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
            <div className="w-24 mr-4">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
            <div className="w-32 mr-4">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
            <div className="w-24 mr-4">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
            <div className="w-32 mr-4">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
            <div className="w-20">
              <div className="h-5 bg-gray-100 rounded w-full"></div>
            </div>
          </div>

          {/* Table rows */}
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex py-4">
              <div className="w-8 mr-2">
                <div className="h-5 w-5 bg-gray-100 rounded"></div>
              </div>
              <div className="flex-1 mr-4 flex items-center">
                <div className="h-10 w-10 bg-gray-100 rounded mr-3"></div>
                <div className="h-5 bg-gray-100 rounded w-48"></div>
              </div>
              <div className="w-24 mr-4 flex items-center">
                <div className="h-5 bg-gray-100 rounded w-full"></div>
              </div>
              <div className="w-24 mr-4 flex items-center">
                <div className="h-5 bg-gray-100 rounded w-full"></div>
              </div>
              <div className="w-32 mr-4 flex items-center">
                <div className="h-5 bg-gray-100 rounded-full w-24"></div>
              </div>
              <div className="w-24 mr-4 flex items-center">
                <div className="h-5 bg-gray-100 rounded w-full"></div>
              </div>
              <div className="w-32 mr-4 flex items-center">
                <div className="h-5 bg-gray-100 rounded w-full"></div>
              </div>
              <div className="w-20 flex items-center">
                <div className="h-5 bg-gray-100 rounded w-full"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      <h2 className="text-2xl font-semibold text-center mb-2">
        Build your next product listing on Amazon
      </h2>
      <p className="text-gray-600 text-center max-w-lg mb-8">
        Start adding products to efficiently manage your listings and optimize
        your sales on Amazon.
      </p>

      <div className="flex gap-4">
        <button className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 font-medium">
          Tutorial
        </button>
        <button
          className="px-6 py-2 bg-[#FACC15] rounded-lg text-gray-900 font-medium"
          onClick={createNewListingHandler}
        >
          Create new listing
        </button>
      </div>
    </div>
  );
};

export default Empty;
