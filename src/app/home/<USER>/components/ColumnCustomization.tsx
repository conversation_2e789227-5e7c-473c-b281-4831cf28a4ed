import { search } from '@/utils/icon';
import { useState } from 'react';
import { Column } from '../page';

interface Props {
  toggleColumnCustomize: () => void;
  availableColumns: Column[];
  selectedColumns: Column[];
  toggleColumnSelection: (columnId: string) => void;
  updateColumnOrder: (columns: Column[]) => void;
}
const ColumnCustomization = ({
  toggleColumnCustomize,
  availableColumns,
  selectedColumns,
  toggleColumnSelection,
  updateColumnOrder,
}: Props) => {
  const [draggedItem, setDraggedItem] = useState<Column | null>(null);
  const [columns, setColumns] = useState<Column[]>(selectedColumns);

  // Handle drag start
  const handleDragStart = (column: Column) => {
    setDraggedItem(column);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, targetColumn: Column) => {
    e.preventDefault();
    if (!draggedItem || draggedItem.id === targetColumn.id) return;

    // Reorder columns
    const newColumns = [...columns];
    const draggedIndex = newColumns.findIndex(
      (col) => col.id === draggedItem.id
    );
    const targetIndex = newColumns.findIndex(
      (col) => col.id === targetColumn.id
    );

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove dragged item
      const [removed] = newColumns.splice(draggedIndex, 1);
      // Insert at new position
      newColumns.splice(targetIndex, 0, removed);
      setColumns(newColumns);
    }
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedItem(null);
  };

  // Handle confirm button click
  const handleConfirm = () => {
    updateColumnOrder(columns);
    toggleColumnCustomize();
  };

  return (
    <div className="fixed inset-0 bg-opacity-30 bg-black/60 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
        <div className="flex justify-between items-center p-4">
          <h2 className="text-lg font-medium">Column customize</h2>
          <button
            onClick={toggleColumnCustomize}
            className="text-gray-400 hover:text-gray-500"
          >
            ×
          </button>
        </div>

        <div className="p-4 flex gap-4">
          <div className="w-1/2 border border-[#CBD5E1] rounded-lg">
            <div className="p-3 border-b border-[#CBD5E1] bg-[#F3F4F6] rounded-t-lg">
              <h3 className="font-medium">Add column</h3>
            </div>
            <div className="p-3">
              <div className="relative mb-3 ">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {search}
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[#030712] focus:border-[#030712] sm:text-sm"
                  placeholder="Search column"
                />
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {availableColumns.map((column) => (
                  <div
                    key={column.id}
                    className="flex items-center p-2 hover:bg-gray-50 rounded"
                  >
                    <input
                      type="checkbox"
                      id={`col-${column.id}`}
                      checked={column.selected}
                      onChange={() => toggleColumnSelection(column.id)}
                      className="h-4 w-4 accent-[#030712]"
                    />
                    <label
                      htmlFor={`col-${column.id}`}
                      className="ml-2 block text-sm text-gray-900"
                    >
                      {column.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="w-1/2 border border-[#CBD5E1] rounded-lg">
            <div className="p-3 border-b border-[#CBD5E1] bg-[#F3F4F6] rounded-t-lg">
              <h3 className="font-medium">Column display</h3>
            </div>
            <div className="p-3">
              <div className="space-y-2 max-h-72 overflow-y-auto">
                {columns.map((column) => (
                  <div
                    key={column.id}
                    className={`flex items-center justify-between p-2 rounded cursor-move ${
                      draggedItem?.id === column.id
                        ? 'opacity-50'
                        : 'bg-gray-50'
                    }`}
                    draggable
                    onDragStart={() => handleDragStart(column)}
                    onDragOver={(e) => handleDragOver(e, column)}
                    onDragEnd={handleDragEnd}
                  >
                    <div className="flex items-center">
                      <span className="text-gray-500 mr-2">≡</span>
                      <span>{column.name}</span>
                    </div>
                    <button
                      onClick={() => toggleColumnSelection(column.id)}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 p-4">
          <button
            onClick={toggleColumnCustomize}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700"
          >
            Close
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-yellow-400 rounded-lg text-gray-900"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

export default ColumnCustomization;
