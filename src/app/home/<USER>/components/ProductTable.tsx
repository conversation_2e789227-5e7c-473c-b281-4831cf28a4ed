'use client';

import { deleteIc, editIc, eyeon } from '@/utils/icon';
import Image from 'next/image';

interface ProductTableProps {
  products: {
    id: string;
    image: string;
    title: string;
    asin: string;
    sku: string;
    status: string;
    completionStatus: string;
    createdDate: string;
    lastUpdate?: string; // Added latest update field
  }[];
  columns?: {
    id: string;
    name: string;
    selected: boolean;
  }[];
}

const ProductTable = ({ products, columns = [] }: ProductTableProps) => {
  // Use all columns if none provided
  const displayColumns =
    columns.length > 0
      ? columns
      : [
          { id: 'product', name: 'Product', selected: true },
          { id: 'asin', name: 'ASIN', selected: true },
          { id: 'sku', name: 'SKU', selected: true },
          { id: 'performance', name: 'Performance', selected: true },
          { id: 'status', name: 'Status', selected: true },
          { id: 'createdDate', name: 'Created date', selected: true },
          { id: 'lastUpdate', name: 'Latest update', selected: true },
        ];

  // Only show selected columns
  const selectedColumns = displayColumns.filter((col) => col.selected);

  // Status color mapping
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'Excellent listing':
        return 'bg-green-100 text-green-800';
      case 'Good listing':
        return 'bg-blue-100 text-blue-800';
      case 'Need improvement':
        return 'bg-yellow-100 text-yellow-800';
      case 'Critical issues':
        return 'bg-red-100 text-red-800';
      case 'Poor listing':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Completion status indicator
  const getCompletionIcon = (status: string) => {
    switch (status) {
      case 'Complete':
        return (
          <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
        );
      case 'Pending':
        return (
          <span className="inline-block w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
        );
      case 'Draft':
        return (
          <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
        );
      default:
        return null;
    }
  };

  // Function to render cell content based on column ID
  const renderCell = (product: any, columnId: string) => {
    switch (columnId) {
      case 'product':
        return (
          <div className="flex items-center justify-between">
            <div className="flex-shrink-0 h-10 w-10 relative">
              <Image
                height={100}
                width={100}
                className="h-10 w-10 rounded-md object-cover"
                src={'/assets/products/' + product.image}
                alt=""
              />
              <div
                className={`absolute inset-0 ring-1 ring-inset ring-black/10 rounded-md ${product.isBestSeller || product.isAPlusContent ? 'border-2 border-[#EAB308]' : ''}`}
              ></div>
            </div>
            <div className="ml-4 max-w-xs">
              <div
                className="text-sm font-medium text-gray-900 md:truncate md:max-w-[200px] lg:max-w-[300px]"
                title={product.title}
              >
                {product.title}
              </div>
            </div>
            <div className="ml-4 max-w-xs">{eyeon}</div>
          </div>
        );
      case 'asin':
        return <div className="text-sm text-gray-900">{product.asin}</div>;
      case 'sku':
        return <div className="text-sm text-gray-500">{product.sku}</div>;
      case 'performance':
        return (
          <span
            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(
              product.status
            )}`}
          >
            {product.status}
          </span>
        );
      case 'status':
        return (
          <div className="flex items-center">
            {getCompletionIcon(product.completionStatus)}
            <span>{product.completionStatus}</span>
          </div>
        );
      case 'createdDate':
        return (
          <div className="text-sm text-gray-500">{product.createdDate}</div>
        );
      case 'lastUpdate':
        return (
          <div className="text-sm text-gray-500">
            {product.lastUpdate || product.createdDate}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div
      className="bg-white rounded-lg shadow-lg flex flex-col"
      style={{ maxHeight: '70vh' }}
    >
      <h1 className="text-lg font-medium px-4 pt-4">
        Tools & Home Improvement
      </h1>
      <div className="flex-1 overflow-hidden py-4">
        <div className="h-full flex flex-col">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th scope="col" className="px-3 py-3 text-left bg-[#F3F4F6]">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 bg-[#F3F4F6]"
                    />
                  </th>
                  {selectedColumns.map((column) => (
                    <th
                      key={column.id}
                      scope="col"
                      className="bg-[#F3F4F6] px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {column.name} <span className="ml-1">↓</span>
                    </th>
                  ))}
                  <th
                    scope="col"
                    className="bg-[#F3F4F6] px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky right-0 z-10"
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-3 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                      />
                    </td>

                    {selectedColumns.map((column) => (
                      <td
                        key={column.id}
                        className="px-3 py-4 whitespace-nowrap"
                      >
                        {renderCell(product, column.id)}
                      </td>
                    ))}

                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500 sticky right-0 bg-white">
                      <div className="flex space-x-2">
                        <button className="text-indigo-600 hover:text-indigo-900">
                          {editIc()}
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          {deleteIc()}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductTable;
