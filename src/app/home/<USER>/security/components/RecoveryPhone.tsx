import { Button } from '@/components/Button';
import Input from '@/components/Input';
import { updateRecoveryPhone } from '@/utils/api';
import { useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';

const RecoveryPhone = ({
  defaultPhone,
  onUpdated,
}: {
  defaultPhone: string;
  onUpdated?: () => void;
}) => {
  const [isAdding, setIsAdding] = useState(!!defaultPhone);
  const [phone, setPhone] = useState(defaultPhone);

  const saveHandler = async () => {
    try {
      const res = await updateRecoveryPhone(phone);
      if (res.status === 200) {
        toast.success('Phone number updated successfully!');
        onUpdated?.();
      } else {
        toast.error('Failed to update phone number.');
      }
    } catch (error) {}
  };

  const removeHandler = () => {
    setPhone('');
    setIsAdding(false);
  };

  return (
    <>
      <ToastContainer />
      <div className="bg-white shadow-lg rounded-xl mt-4 p-4">
        {!isAdding ? (
          <div className="flex justify-between items-center">
            <p>Add your recovery phone number</p>
            <Button
              className="!w-[100px] bg-white hover:bg-gray-100 border border-[#CBD5E1] !mt-0 !py-2"
              onClickFunc={() => setIsAdding(true)}
            >
              Add
            </Button>
          </div>
        ) : (
          <div className="flex justify-between items-end gap-4">
            <Input
              className="w-full"
              label="Phone number"
              type="text"
              value={phone}
              onChangeFunc={(e) => setPhone(e.target.value)}
            />
            <Button
              className="!w-[100px] bg-white hover:bg-gray-100 border border-[#CBD5E1] !mt-0 !py-2"
              onClickFunc={removeHandler}
            >
              Remove
            </Button>
          </div>
        )}
      </div>

      {isAdding && (
        <div className="flex justify-end">
          <Button onClickFunc={saveHandler} className="!w-[100px]">
            Save
          </Button>
        </div>
      )}
    </>
  );
};

export default RecoveryPhone;
