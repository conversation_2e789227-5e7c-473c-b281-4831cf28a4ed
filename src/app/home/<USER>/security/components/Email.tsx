import { Button } from '@/components/Button';
import Input from '@/components/Input';
import { updateEmail } from '@/utils/api';
import { useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';

const Email = ({
  defaultEmail,
  onUpdated,
}: {
  defaultEmail: string;
  onUpdated?: () => void;
}) => {
  const [email, setEmail] = useState(defaultEmail);
  const [loading, setLoading] = useState(false);

  const emailSaveHandler = async () => {
    try {
      setLoading(true);
      const res = await updateEmail(email);
      if (res.status === 200) {
        toast.success('Email updated successfully!');
        onUpdated?.();
      } else {
        toast.error('Failed to update email.');
      }
    } catch (error) {}
  };

  return (
    <>
      <ToastContainer />
      <div className="bg-white shadow-lg rounded-xl mt-4 p-4">
        <Input
          label="Your Email"
          type="email"
          value={email}
          onChangeFunc={(e) => setEmail(e.target.value)}
        />
      </div>
      <div className="flex justify-end">
        <Button onClickFunc={emailSaveHandler} className="!w-[100px]">
          {loading ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </>
  );
};

export default Email;
