import SecurityActivity from '@/components/modal/SecurityActivity';
import { detailing } from '@/utils/icon';
import { useState } from 'react';

const recentActivities = [
  {
    date: 'March 18, 2025',
    activities: [
      {
        time: '5:52 PM',
        title: 'New sign-in on MacOs',
        value: 'Hanoi, Vietnam',
      },
      {
        time: '5:52 PM',
        title: 'New sign-in on Windows',
        value: 'Hanoi, Vietnam',
      },
      {
        time: '5:52 PM',
        title: 'New sign-in on iOS',
        value: 'Hanoi, Vietnam',
      },
    ],
  },
  {
    date: 'March 10, 2025',
    activities: [
      {
        time: '5:52 PM',
        title: 'New sign-in on MacOs',
        value: 'Hanoi, Vietnam',
      },
    ],
  },
  {
    date: 'March 2, 2025',
    activities: [
      {
        time: '5:52 PM',
        title: 'New sign-in on Windows',
        value: 'Hanoi, Vietnam',
      },
      {
        time: '5:52 PM',
        title: 'New sign-in on iOS',
        value: 'Hanoi, Vietnam',
      },
    ],
  },
];
const Activities = () => {
  const [selectedActivity, setSelectedActivity] = useState(null);
  const selectActivityHandler = (activity: any) => {
    setSelectedActivity(activity);
  };
  const closeHandler = () => {
    setSelectedActivity(null);
  };
  const yesHandler = () => {
    setSelectedActivity(null);
  };
  const noHandler = () => {
    setSelectedActivity(null);
  };
  return (
    <div className="mt-4">
      {recentActivities.map((item, index) => (
        <div key={index} className="mt-4">
          <h3 className="text-base font-semibold text-[#000000] mb-4">
            {item.date}
          </h3>
          <div className="bg-white shadow-lg rounded-xl mt-4">
            {item.activities.map((activity, index) => (
              <div
                className={`grid grid-cols-12 border border-[#CBD5E1] ${
                  index == 0 && 'rounded-t-lg'
                } ${index == item.activities.length - 1 && 'rounded-b-lg'}
                }`}
                key={index}
                onClick={selectActivityHandler}
              >
                <div className="col-span-2 p-4 flex items-center">
                  {activity.time}
                </div>
                <div className="col-span-4 p-4 flex gap-2 items-center">
                  {activity.title}
                </div>
                <div className="col-span-5 p-4 flex gap-2 items-center">
                  {activity.value}
                </div>
                <div className="clol-span-1 p-4 flex items-center justify-center cursor-pointer">
                  {detailing}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
      {selectedActivity && (
        <SecurityActivity
          closeHandler={closeHandler}
          yesHandler={yesHandler}
          noHandler={noHandler}
        />
      )}
    </div>
  );
};

export default Activities;
