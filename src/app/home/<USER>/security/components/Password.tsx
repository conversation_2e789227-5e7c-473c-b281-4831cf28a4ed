import { Button } from '@/components/Button';
import Input from '@/components/Input';
import PasswordValidation from '@/components/PasswordValidation';
import { updatePassword } from '@/utils/api';
import { useEffect, useState } from 'react';
import { toast, ToastContainer } from 'react-toastify';

const Password = ({ onUpdated }: { onUpdated?: () => void }) => {
  const [confirmPassword, setConfirmPassword] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [validation, setValidation] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    noSpace: false,
    specialChar: false,
  });

  useEffect(() => {
    setValidation({
      length: newPassword.length >= 8,
      uppercase: /[A-Z]/.test(newPassword),
      lowercase: /[a-z]/.test(newPassword),
      noSpace: !/\s/.test(newPassword),
      specialChar: /[^A-Za-z0-9]/.test(newPassword),
    });
  }, [newPassword]);

  const saveHandler = async () => {
    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match.');
      return;
    }
    try {
      const res = await updatePassword(currentPassword, newPassword);
      if (res.status === 200) {
        toast.success('Password updated successfully!');
        onUpdated?.();
      } else {
        toast.error('Failed to update password.');
      }
    } catch (error) {}
  };

  return (
    <>
      <ToastContainer />
      <div className="bg-white shadow-lg rounded-xl mt-4 p-4">
        <Input
          label="Current Password"
          type="password"
          value={currentPassword}
          onChangeFunc={(e) => setCurrentPassword(e.target.value)}
        />
        <Input
          label="New Password"
          type="password"
          value={newPassword}
          onChangeFunc={(e) => setNewPassword(e.target.value)}
        />
        <Input
          className="mt-4"
          label="Confirm Password"
          type="password"
          value={confirmPassword}
          onChangeFunc={(e) => setConfirmPassword(e.target.value)}
        />
        <PasswordValidation validation={validation} />
      </div>

      <div className="flex justify-end">
        <Button onClickFunc={saveHandler} className="!w-[100px]">
          Save
        </Button>
      </div>
    </>
  );
};

export default Password;
