import AuthenticatorSetup from '@/components/modal/AuthenticatorSetup';
import RecoveryPhoneSetup from '@/components/modal/RecoveryPhoneSetup';
import { User } from '@/types';
import { get2FAQRCode } from '@/utils/api';
import {
  authenticator,
  detailing,
  done,
  recoveryPhone,
  warning,
} from '@/utils/icon';
import { useEffect, useState } from 'react';

const TwoStepAuth = ({
  user,
  setUser,
}: {
  user: User | null;
  setUser: (u: User) => void;
}) => {
  const [isAddingAuthenticatorApp, setIsAddingAuthenticatorApp] =
    useState(false);
  const [isAddingRecoveryPhone, setIsAddingRecoveryPhone] = useState(false);
  const [QRCodeURL, setQRCodeURL] = useState('');

  useEffect(() => {
    async function get2FAQRCodeHandler() {
      try {
        await get2FAQRCode();
      } catch (error) {
        const response = error.response.data;
        setQRCodeURL(response.meta.totp_url);
      }
    }
    get2FAQRCodeHandler();
  }, []);

  return (
    <div className="bg-white shadow-md rounded-xl p-6 mt-4">
      <div className="flex justify-between">
        <div>
          <h2 className="text-base font-semibold text-[#000000] mb-1">
            Your account is protected with 2-Step Authentication
          </h2>
          <p className="test-sm font-normal text-[#64748B]">
            Prevent hackers from accessing your account with an additional layer
            of security.
          </p>
        </div>
        <label className="inline-flex items-center cursor-pointer">
          <input
            onChange={() => {}}
            type="checkbox"
            checked={true}
            className="sr-only peer"
          />
          <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#374151]"></div>
        </label>
      </div>
      <div className="mt-4">
        <div className="grid grid-cols-12 border border-[#CBD5E1] rounded-t-lg">
          <div className="col-span-1 p-4 flex items-center justify-center">
            {authenticator}
          </div>
          <div className="col-span-5 p-4 flex items-center">Authenticator</div>
          <div className="col-span-5 p-4 flex gap-2 items-center">
            {user?.two_fa_enabled_date ? (
              <>
                {done}
                Added since{' '}
                {new Date(user.two_fa_enabled_date).toLocaleDateString()}
              </>
            ) : (
              <>
                {warning}
                Add authenticator app
              </>
            )}
          </div>
          <div
            className="clol-span-1 p-4 flex items-center justify-center cursor-pointer"
            onClick={() => {
              setIsAddingAuthenticatorApp(true);
            }}
          >
            {detailing}
          </div>
        </div>
        <div className="grid grid-cols-12 border border-[#CBD5E1] rounded-b-lg">
          <div className="col-span-1 p-4 flex items-center justify-center">
            {recoveryPhone}
          </div>
          <div className="col-span-5 p-4 flex items-center">Phone number</div>
          <div className="col-span-5 p-4 flex gap-2 items-center">
            {done}
            0967 909 941
          </div>
          <div
            className="col-span-1 p-4 flex items-center justify-center cursor-pointer"
            onClick={() => {
              setIsAddingRecoveryPhone(true);
            }}
          >
            {detailing}
          </div>
        </div>
      </div>

      {isAddingAuthenticatorApp && (
        <AuthenticatorSetup
          onClose={() => setIsAddingAuthenticatorApp(false)}
          onConfirm={() => {
            setIsAddingAuthenticatorApp(false);
            if (user) {
              setUser({
                ...user,
                two_fa_enabled_date: new Date().toISOString(),
              });
            }
          }}
          QRCodeURL={QRCodeURL}
        />
      )}
      {isAddingRecoveryPhone && (
        <RecoveryPhoneSetup
          onClose={() => setIsAddingRecoveryPhone(false)}
          onConfirm={() => setIsAddingRecoveryPhone(false)}
        />
      )}
    </div>
  );
};

export default TwoStepAuth;
