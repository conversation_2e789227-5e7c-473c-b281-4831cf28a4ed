'use client';

import DeleteAccount from '@/components/modal/DeleteAccount';
import { User } from '@/types';
import { fetchUser } from '@/utils/api';
import {
  back,
  detailing,
  done,
  email,
  password,
  recoveryPhone,
  twoStepAuth,
} from '@/utils/icon';
import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import Activities from './components/Activities';
import Email from './components/Email';
import Password from './components/Password';
import RecoveryPhone from './components/RecoveryPhone';
import TwoStepAuth from './components/TwoStepAuth';

const Security = () => {
  const [isReviewAll, setIsReviewAll] = useState(false);
  const [isUpdateEmail, setIsUpdateEmail] = useState(false);
  const [isUpdatePassword, setIsUpdatePassword] = useState(false);
  const [isUpdateRecoveryPhone, setIsUpdateRecoveryPhone] = useState(false);
  const [isUpdateTwoStepAuth, setIsUpdateTwoStepAuth] = useState(false);
  const [isDeleteAccount, setIsDeleteAccount] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  const fetchAccount = async () => {
    const res = await fetchUser();
    if (res && res.data) {
      setUser(res.data);
    }
  };

  useEffect(() => {
    fetchAccount();
  }, []);

  const reviewAllHandler = () => {
    setIsReviewAll(true);
  };
  const reviewAllBackHandler = () => {
    setIsReviewAll(false);
  };

  const updateEmailHandler = () => {
    setIsUpdateEmail(true);
  };
  const updateEmailBackHandler = () => {
    setIsUpdateEmail(false);
  };
  const updatePasswordHandler = () => {
    setIsUpdatePassword(true);
  };
  const updatePasswordBackHandler = () => {
    setIsUpdatePassword(false);
  };
  const updateRecoveryPhoneHandler = () => {
    setIsUpdateRecoveryPhone(true);
  };
  const updateRecoveryPhoneBackHandler = () => {
    setIsUpdateRecoveryPhone(false);
  };
  const updateTwoStepAuthHandler = () => {
    setIsUpdateTwoStepAuth(true);
  };
  const updateTwoStepAuthBackHandler = () => {
    setIsUpdateTwoStepAuth(false);
  };
  const deleteAccountHandler = () => {
    setIsDeleteAccount(true);
  };
  const cancelDeleteAccountHandler = () => {
    setIsDeleteAccount(false);
  };

  const twoStepItems = [
    {
      icon: twoStepAuth,
      title: '2-step Authentication',
      value: user?.two_fa_enabled_date
        ? `ON since ${new Date(user.two_fa_enabled_date).toLocaleDateString()}`
        : '2-step Authentication is off',
      action: updateTwoStepAuthHandler,
    },
    {
      icon: email,
      title: 'Email',
      value: user?.email ?? 'Loading...',
      action: updateEmailHandler,
    },
    {
      icon: password,
      title: 'Password',
      value: user?.last_password_changed
        ? `Last changed ${new Date(user.last_password_changed).toLocaleDateString()}`
        : 'Never changed',
      action: updatePasswordHandler,
    },
    {
      icon: recoveryPhone,
      title: 'Recovery Phone',
      value: user?.phone_number ?? 'Loading...',
      action: updateRecoveryPhoneHandler,
    },
  ];

  const securityActivities = [
    {
      title: 'New sign-in on MacOs',
      value: '17/03/2025 Hanoi, Vietnam',
    },
    {
      title: 'New sign-in on Windows',
      value: '17/03/2025 Hanoi, Vietnam',
    },
    {
      title: 'New sign-in on iOS',
      value: '17/03/2025 Hanoi, Vietnam',
    },
  ];
  let context = (
    <>
      <h1 className="text-2xl font-semibold">Security</h1>
      <div className="bg-white shadow-md rounded-xl p-6 mt-4">
        <div className="flex justify-between">
          <div>
            <h2 className="text-base font-semibold text-[#000000] mb-1">
              Your account is protected with 2-Step Authentication
            </h2>
            <p className="test-sm font-normal text-[#64748B]">
              Prevent hackers from accessing your account with an additional
              layer of security.
            </p>
          </div>
          <label className="inline-flex items-center cursor-pointer">
            <input
              onChange={() => {}}
              type="checkbox"
              checked={true}
              className="sr-only peer"
            />
            <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#374151]"></div>
          </label>
        </div>
        <div className="mt-4">
          {twoStepItems.map((item, index) => (
            <div
              className={`grid grid-cols-12 border border-[#CBD5E1] ${
                index == 0
                  ? 'rounded-t-lg'
                  : index == twoStepItems.length - 1
                    ? 'rounded-b-lg'
                    : ''
              }`}
              key={index}
            >
              <div className="col-span-1 p-4 flex items-center justify-center">
                {item.icon}
              </div>
              <div className="col-span-5 p-4 flex items-center">
                {item.title}
              </div>
              <div className="col-span-5 p-4 flex gap-2 items-center">
                {item.title === '2-step Authentication' &&
                  user?.two_fa_enabled_date &&
                  done}
                {item.value}
              </div>
              <div
                className="col-span-1 p-4 flex items-center justify-center cursor-pointer"
                onClick={item.action}
              >
                {detailing}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-white shadow-lg rounded-xl p-6 mt-4">
        <h3 className="text-base font-semibold text-gray-800 mb-4">
          Recent security activities
        </h3>
        <div className="mt-4">
          {securityActivities.map((item, index) => (
            <div
              className={`grid grid-cols-12 border border-[#CBD5E1] ${
                index == 0
                  ? 'rounded-t-lg'
                  : index == securityActivities.length - 1
                    ? 'rounded-b-lg'
                    : ''
              }`}
              key={index}
            >
              <div className="col-span-6 p-4 flex items-center">
                {item.title}
              </div>
              <div className="col-span-5 p-4 flex gap-2 items-center">
                {item.title === '2-step Authentication' && done}
                {item.value}
              </div>
              <div className="col-span-1 p-4 flex items-center justify-center cursor-pointer">
                {detailing}
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-between items-center">
          <p
            onClick={reviewAllHandler}
            className="mt-4 text-sm font-medium text-[#1E40AF] cursor-pointer"
          >
            Review all security activities (5)
          </p>
          <p className="mt-4 text-sm font-medium text-[#DC2626] cursor-pointer">
            Terminate all activities
          </p>
        </div>
      </div>

      <div className="bg-white shadow-lg rounded-xl p-6 mt-4">
        <h3 className="text-base font-semibold text-gray-800">
          Account removal
        </h3>
        <p className="text-sm font-normal text-[#64748B]">
          After deleting account, your listings and information will be
          permanently removed after 30 days.
        </p>
        <p
          onClick={deleteAccountHandler}
          className="mt-4 text-sm font-medium text-[#B91C1C] cursor-pointer"
        >
          Delete account
        </p>
      </div>

      {isDeleteAccount && (
        <DeleteAccount onClose={cancelDeleteAccountHandler} />
      )}
    </>
  );
  if (isReviewAll) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span className="cursor-pointer" onClick={reviewAllBackHandler}>
            {back}
          </span>
          Recent security activities
        </h1>
        <Activities />
      </>
    );
  }
  if (isUpdateEmail) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span className="cursor-pointer" onClick={updateEmailBackHandler}>
            {back}
          </span>
          Email
        </h1>
        <Email defaultEmail={user?.email ?? ''} onUpdated={fetchAccount} />
      </>
    );
  }
  if (isUpdatePassword) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span className="cursor-pointer" onClick={updatePasswordBackHandler}>
            {back}
          </span>
          Password
        </h1>
        <Password onUpdated={() => setIsUpdatePassword(false)} />
      </>
    );
  }

  if (isUpdateRecoveryPhone) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span
            className="cursor-pointer"
            onClick={updateRecoveryPhoneBackHandler}
          >
            {back}
          </span>
          Recovery Phone
        </h1>
        <RecoveryPhone
          defaultPhone={user?.phone_number ?? ''}
          onUpdated={() => setIsUpdateRecoveryPhone(false)}
        />
      </>
    );
  }
  if (isUpdateTwoStepAuth) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span
            className="cursor-pointer"
            onClick={updateTwoStepAuthBackHandler}
          >
            {back}
          </span>
          2-step Authentication
        </h1>
        <TwoStepAuth user={user} setUser={setUser} />
      </>
    );
  }
  return (
    <div className="w-full max-w-4xl p-4">
      {context} <ToastContainer />
    </div>
  );
};

export default Security;
