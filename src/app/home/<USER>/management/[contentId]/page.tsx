'use client';

import Image from 'next/image';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  FiAlertTriangle,
  FiArrowLeft,
  FiCheckCircle,
  FiClock,
  FiEdit2,
  FiPlus,
} from 'react-icons/fi';

interface ContentDetail {
  id: number;
  name: string;
  asins: { id: string; title: string; image: string }[];
  status: 'Draft' | 'Active' | 'Pending' | 'Rejected';
  language: string;
  lastModified: string;
  created: string;
  description?: string;
}

// Mock function to fetch content detail
const fetchContentDetail = async (id: string): Promise<ContentDetail> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 500));

  const mockData: Record<string, ContentDetail> = {
    '1': {
      id: 1,
      name: 'Premium Product X',
      asins: [
        {
          id: 'B08N5WRWNW',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '0caf5aded4ba862dc5ce8ab820f6d5c231587d6e.png',
        },
        {
          id: 'B08N5WRWNY',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '3cee324c413a3a429ffe4060e321a8893b8749c6.png',
        },
        {
          id: 'B08N5WRWNZ',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '4ea92733bf1b63b6d2f7e71fbe960e321cb967c6.png',
        },
        {
          id: 'B08N5WRWNA',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '8d86934017c9e54d0f813441acfe19d1bb9f3c21.png',
        },
        {
          id: 'B08N5WRWNB',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: 'b083458dde5f4966bd26a2f4ecc9e4d9f3545f89.png',
        },
      ],
      status: 'Draft',
      language: 'UK English',
      lastModified: '27/12/2024',
      created: '25/12/2024',
      description:
        'Detailed product description with enhanced content for Premium Product X.',
    },
    '2': {
      id: 2,
      name: 'Deluxe Bundle',
      asins: [
        {
          id: 'B08N5WRWNW',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: 'd7f3d587f78a8f50d63a2ce27a17c7b595f637b1.png',
        },
        {
          id: 'B08N5WRWNY',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: 'eedcf23615ea0218e5f8d54c51a81c74633db57c.png',
        },
      ],
      status: 'Active',
      language: 'UK English',
      lastModified: '26/12/2024',
      created: '24/12/2024',
      description: 'Comprehensive content for the Deluxe Bundle package.',
    },
    '3': {
      id: 3,
      name: 'Starter Pack',
      asins: [
        {
          id: 'B08N5WRWNW',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: 'f12ddc26e9f8a7048b2b4b2506eb008ea2414db0.png',
        },
        {
          id: 'B08N5WRWNY',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: 'f6ed094a655d947abb394b7fe3b08f6cbaf8d401.png',
        },
        {
          id: 'B08N5WRWNZ',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '0caf5aded4ba862dc5ce8ab820f6d5c231587d6e.png',
        },
      ],
      status: 'Pending',
      language: 'UK English',
      lastModified: '25/12/2024',
      created: '23/12/2024',
      description: 'Starter pack content for new product line.',
    },
  };

  return mockData[id] || mockData['1'];
};

export default function ContentDetailPage() {
  const router = useRouter();
  const { contentId } = useParams();
  const [content, setContent] = useState<ContentDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadContent = async () => {
      try {
        const data = await fetchContentDetail(contentId as string);
        setContent(data);
      } catch (error) {
        console.error('Failed to load content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContent();
  }, [contentId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <FiCheckCircle className="h-4 w-4 text-green-500" />;
      case 'Pending':
        return <FiClock className="h-4 w-4 text-yellow-500" />;
      case 'Rejected':
        return <FiAlertTriangle className="h-4 w-4 text-red-500" />;
      default: // Draft
        return <FiEdit2 className="h-4 w-4 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid gap-4">
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded w-full"></div>
              ))}
          </div>
        </div>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="p-6 text-center">
        <p>Content not found</p>
        <button
          onClick={() => router.back()}
          className="mt-4 text-indigo-600 hover:text-indigo-800"
        >
          Go back
        </button>
      </div>
    );
  }

  return (
    <div className="mt-20 flex-1 overflow-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <FiArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-semibold text-gray-900">
              {content.name}
            </h1>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {content.status}
            </span>
          </div>
          <div className="flex space-x-3">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <FiEdit2 className="-ml-1 mr-2 h-4 w-4" />
              Edit
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
              Publish Now
            </button>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Preview Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Content Preview
                </h2>
              </div>
              <div className="p-6 bg-gray-50 min-h-[400px] flex items-center justify-center">
                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100">
                    <FiEye className="h-6 w-6 text-indigo-600" />
                  </div>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Content Preview
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    A preview of your A+ Content will appear here.
                  </p>
                </div>
              </div>
            </div>

            {/* Modules Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Content Modules
                </h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {[1, 2, 3].map((module) => (
                    <div
                      key={module}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            Module {module}
                          </h4>
                          <p className="mt-1 text-sm text-gray-500">
                            Description of module {module}
                          </p>
                        </div>
                        <button className="text-gray-400 hover:text-gray-500">
                          <FiEdit2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                <button className="mt-4 inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500">
                  <FiPlus className="mr-1.5 h-4 w-4" />
                  Add Module
                </button>
              </div>
            </div>

            {/* Applied ASINs Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Applied ASINs ({content.asins.length})
                </h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {content.asins.map((asin) => (
                    <div
                      key={asin.id}
                      className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-md mr-4 overflow-hidden">
                        <Image
                          src={`/assets/products/${asin.image}`}
                          alt={asin.title}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                          onError={() => {
                            // Handle error silently
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {asin.title}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          ASIN: {asin.id}
                        </p>
                      </div>
                      <button className="text-gray-400 hover:text-gray-500 ml-2">
                        <FiEdit2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
                {content.asins.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-sm text-gray-500">
                      No ASINs applied to this content.
                    </p>
                    <button className="mt-2 inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500">
                      <FiPlus className="mr-1.5 h-4 w-4" />
                      Add ASIN
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status Card */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getStatusIcon(content.status)}
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {content.status}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {content.status === 'Draft' &&
                        'This content is currently in draft mode.'}
                      {content.status === 'Active' &&
                        'This content is currently live.'}
                      {content.status === 'Pending' &&
                        'This content is pending approval.'}
                      {content.status === 'Rejected' &&
                        'This content was rejected. Please make changes.'}
                    </p>
                  </div>
                </div>
                <div className="mt-5">
                  <button
                    type="button"
                    className="inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {content.status === 'Draft'
                      ? 'Submit for Review'
                      : 'Update Content'}
                  </button>
                </div>
              </div>
            </div>

            {/* Details Card */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Content Details
                </h3>
                <dl className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-1">
                  <div className="px-4 py-2 bg-gray-50 rounded-lg overflow-hidden sm:p-4">
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Last Modified
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {content.lastModified}
                    </dd>
                  </div>
                  <div className="px-4 py-2 bg-gray-50 rounded-lg overflow-hidden sm:p-4">
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Created
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {content.created}
                    </dd>
                  </div>
                  <div className="px-4 py-2 bg-gray-50 rounded-lg overflow-hidden sm:p-4">
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Language
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {content.language}
                    </dd>
                  </div>
                  <div className="px-4 py-2 bg-gray-50 rounded-lg overflow-hidden sm:p-4">
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Applied ASINs
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {content.asins.length} ASINs
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Add missing FiEye icon
const FiEye = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
    />
  </svg>
);
