'use client';

import { deleteIc, editIc } from '@/utils/icon';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiChevronDown, FiFilter, FiSearch } from 'react-icons/fi';

// Mock data fetching function
const fetchAPlusContents = async (): Promise<
  Array<{
    id: number;
    name: string;
    asins: { id: string; title: string; image: string }[];
    status: string;
    language: string;
    lastModified: string;
  }>
> => {
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return [
    {
      id: 1,
      name: 'Shoes_SneakerMen_Version1',
      status: 'Draft',
      language: 'UK English',
      asins: [],
      lastModified: '27/12/2024',
    },
    {
      id: 2,
      name: 'Coffee_Hazelnut_A+_Jun2025',
      status: 'Active',
      language: 'UK English',
      asins: [
        {
          id: 'B001',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '0caf5aded4ba862dc5ce8ab820f6d5c231587d6e.png',
        },
        {
          id: 'B002',
          title:
            '24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for 4',
          image: '3cee324c413a3a429ffe4060e321a8893b8749c6.png',
        },
      ],
      lastModified: '27/12/2024',
    },
    {
      id: 3,
      name: 'Skincare_RetinoidNightSerum_V2',
      status: 'Active',
      language: 'UK English',
      asins: Array.from({ length: 24 }, (_, i) => ({
        id: `B${String(i + 1).padStart(3, '0')}`,
        title: `24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for ${i + 1}`,
        image: [
          '0caf5aded4ba862dc5ce8ab820f6d5c231587d6e.png',
          '3cee324c413a3a429ffe4060e321a8893b8749c6.png',
          '4ea92733bf1b63b6d2f7e71fbe960e321cb967c6.png',
          '8d86934017c9e54d0f813441acfe19d1bb9f3c21.png',
          'b083458dde5f4966bd26a2f4ecc9e4d9f3545f89.png',
        ][i % 5],
      })),
      lastModified: '27/12/2024',
    },
    {
      id: 4,
      name: 'LaptopStand_Aluminum_Foldable_ENG',
      status: 'Active',
      language: 'UK English',
      asins: [],
      lastModified: '27/12/2024',
    },
    {
      id: 5,
      name: 'FinalVersion_02_Shoes',
      status: 'Pending',
      language: 'UK English',
      asins: Array.from({ length: 17 }, (_, i) => ({
        id: `B${String(i + 1).padStart(3, '0')}`,
        title: `24-Piece Black Silverware Set with Steak Knives, Black Flatware Set for ${i + 1}`,
        image: [
          'd7f3d587f78a8f50d63a2ce27a17c7b595f637b1.png',
          'eedcf23615ea0218e5f8d54c51a81c74633db57c.png',
          'f12ddc26e9f8a7048b2b4b2506eb008ea2414db0.png',
          'f6ed094a655d947abb394b7fe3b08f6cbaf8d401.png',
        ][i % 4],
      })),
      lastModified: '27/12/2024',
    },
    {
      id: 6,
      name: 'A+_Draft_01_Coffee',
      status: 'Draft',
      language: 'UK English',
      asins: [],
      lastModified: '27/12/2024',
    },
  ];
};

const statusStyles = {
  Draft: 'bg-gray-100 text-gray-700 border border-gray-200',
  Active: 'bg-green-100 text-green-700 border border-green-200',
  Pending: 'bg-yellow-100 text-yellow-700 border border-yellow-200',
} as const;

export function APlusContentTable() {
  const router = useRouter();
  const [aplusContents, setAplusContents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('All');
  const tabs = ['All', 'Active', 'Pending', 'Draft'];
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [openAsinId, setOpenAsinId] = useState<number | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const data = await fetchAPlusContents();
        setAplusContents(data);
      } catch (error) {
        console.error('Error loading A+ content:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const filteredContent = aplusContents.filter((content) => {
    const matchesTab = activeTab === 'All' || content.status === activeTab;
    const matchesSearch = content.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return matchesTab && (searchQuery === '' || matchesSearch);
  });

  const totalPages = Math.ceil(filteredContent.length / itemsPerPage);
  const currentItems = filteredContent.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Skeleton loading component
  const TableSkeleton = () => (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col"
      style={{ maxHeight: '70vh' }}
    >
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th scope="col" className="px-4 py-3 text-left bg-[#F9FAFB]">
                    <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-18 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                  <th scope="col" className="bg-[#F9FAFB] px-4 py-3 text-left">
                    <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Array.from({ length: 3 }, (_, i) => (
                  <tr
                    key={i}
                    className="animate-pulse"
                    style={{ animationDelay: `${i * 100}ms` }}
                  >
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-4 w-4 bg-gray-200 rounded"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-4 w-48 bg-gray-200 rounded"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-4 w-8 bg-gray-200 rounded"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-6 w-16 bg-gray-200 rounded-full"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-4 w-20 bg-gray-200 rounded"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-block h-4 w-20 bg-gray-200 rounded"></span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-flex space-x-3">
                        <span className="inline-block h-4 w-4 bg-gray-200 rounded"></span>
                        <span className="inline-block h-4 w-4 bg-gray-200 rounded"></span>
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );

  // Empty state content component (text and buttons only)
  const EmptyStateContent = () => (
    <div className="flex flex-col items-center justify-center py-16 px-8 text-center">
      <div className="max-w-md mx-auto">
        <h3 className="text-xl font-semibold text-gray-900 mb-3">
          Build your next A+ Content on Amazon
        </h3>
        <p className="text-gray-500 mb-8 leading-relaxed">
          Start adding products to efficiently manage your listings and optimize
          your sales on Amazon.
        </p>
        <div className="flex items-center justify-center space-x-3">
          <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors">
            Tutorial
          </button>
          <button
            onClick={() => router.push('/home/<USER>/create')}
            className="px-6 py-2 bg-yellow-400 text-gray-900 rounded-lg font-medium hover:bg-yellow-500 transition-colors"
          >
            Create new A+ Content
          </button>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <h1 className="text-2xl font-semibold text-gray-900 mb-8">
          A+ Content Management
        </h1>

        {/* Tabs skeleton */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex space-x-1">
            {Array.from({ length: 4 }, (_, i) => (
              <div
                key={i}
                className="h-8 w-16 bg-gray-200 rounded-full animate-pulse"
              ></div>
            ))}
          </div>
          <div className="flex items-center bg-white border border-gray-300 rounded-lg px-3 py-2">
            <div className="h-5 w-5 bg-gray-200 rounded mr-2 animate-pulse"></div>
            <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-px h-5 bg-gray-300 mx-2"></div>
            <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        <TableSkeleton />
        <EmptyStateContent />
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-semibold text-gray-900 mb-8">
        A+ Content Management
      </h1>

      {/* Tabs */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex space-x-1">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => {
                setActiveTab(tab);
                setCurrentPage(1);
              }}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                activeTab === tab
                  ? 'bg-gray-900 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        <div className="flex items-center space-x-3">
          <div className="flex items-center bg-white border border-gray-300 rounded-lg px-3 py-2">
            <FiSearch className="h-5 w-5 text-gray-400 mr-2" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border-none outline-none text-sm bg-transparent placeholder-gray-400 w-48"
              placeholder="Search content"
            />
            <div className="w-px h-5 bg-gray-300 mx-2"></div>
            <button className="p-1 hover:bg-gray-50 rounded transition-colors">
              <FiFilter className="h-4 w-4 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Table or Empty State */}
      {filteredContent.length === 0 ? (
        <div>
          <TableSkeleton />
          <EmptyStateContent />
        </div>
      ) : (
        <div
          className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col"
          style={{ maxHeight: '70vh' }}
        >
          <div className="flex-1 overflow-hidden">
            <div className="h-full flex flex-col">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                      <th
                        scope="col"
                        className="px-4 py-3 text-left bg-[#F9FAFB]"
                      >
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Content name{' '}
                        <FiChevronDown className="inline ml-1 h-3 w-3" />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Applied ASINs{' '}
                        <FiChevronDown className="inline ml-1 h-3 w-3" />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Status <FiChevronDown className="inline ml-1 h-3 w-3" />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Language{' '}
                        <FiChevronDown className="inline ml-1 h-3 w-3" />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Last modified{' '}
                        <FiChevronDown className="inline ml-1 h-3 w-3" />
                      </th>
                      <th
                        scope="col"
                        className="bg-[#F9FAFB] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky right-0 z-10"
                      >
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentItems.map((content) => (
                      <tr
                        key={content.id}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <button
                            onClick={() =>
                              router.push(
                                `/home/<USER>/management/${content.id}`
                              )
                            }
                            className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors"
                          >
                            {content.name}
                          </button>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="relative">
                            <button
                              onClick={() =>
                                setOpenAsinId(
                                  openAsinId === content.id ? null : content.id
                                )
                              }
                              className="flex items-center text-sm text-gray-900 hover:text-blue-600 transition-colors"
                            >
                              <span className="font-medium">
                                {content.asins.length}
                              </span>
                              {content.asins.length > 0 && (
                                <FiChevronDown className="ml-1 h-4 w-4" />
                              )}
                            </button>

                            {/* ASIN Dropdown */}
                            {openAsinId === content.id &&
                              content.asins.length > 0 && (
                                <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                                  <div className="p-2">
                                    {content.asins.map((asin: any) => (
                                      <button
                                        key={asin.id}
                                        onClick={() =>
                                          router.push(
                                            `/home/<USER>/management/${content.id}`
                                          )
                                        }
                                        className="w-full flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors text-left"
                                      >
                                        <div className="flex-shrink-0 w-10 h-10 bg-gray-200 rounded-md mr-3 overflow-hidden">
                                          <Image
                                            src={`/assets/products/${asin.image}`}
                                            alt={asin.title}
                                            width={40}
                                            height={40}
                                            className="w-full h-full object-cover"
                                            onError={() => {
                                              // Handle error silently
                                            }}
                                          />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                          <p className="text-sm font-medium text-gray-900 truncate">
                                            {asin.title}
                                          </p>
                                          <p className="text-xs text-gray-500">
                                            ASIN: {asin.id}
                                          </p>
                                        </div>
                                      </button>
                                    ))}
                                  </div>
                                </div>
                              )}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span
                            className={`px-3 py-1 inline-flex text-xs leading-5 font-medium rounded-full ${statusStyles[content.status as keyof typeof statusStyles]}`}
                          >
                            {content.status}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                          {content.language}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                          {content.lastModified}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 sticky right-0 bg-white">
                          <div className="flex space-x-3">
                            <button className="text-blue-600 hover:text-blue-800 transition-colors">
                              {editIc()}
                            </button>
                            <button className="text-red-600 hover:text-red-800 transition-colors">
                              {deleteIc()}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pagination */}
      {filteredContent.length > 0 && (
        <div className="flex items-center justify-between mt-6">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
            >
              <FiChevronDown className="w-4 h-4 rotate-90" />
            </button>

            <div className="flex items-center space-x-2">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`w-8 h-8 flex items-center justify-center text-sm font-medium rounded-full transition-colors ${
                      currentPage === page
                        ? 'bg-black text-white'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              {totalPages > 5 && (
                <span className="px-2 text-gray-400">...</span>
              )}
              {totalPages > 5 && (
                <button
                  onClick={() => setCurrentPage(totalPages)}
                  className="w-8 h-8 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
                >
                  {totalPages}
                </button>
              )}
            </div>

            <button
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
              className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
            >
              <FiChevronDown className="w-4 h-4 -rotate-90" />
            </button>
          </div>

          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-700 font-medium">
              Row per page
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[60px]"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
}

export default APlusContentTable;
