export const CurrentPlan = ({
  change<PERSON><PERSON><PERSON>and<PERSON>,
}: {
  changePlanHandler: () => void;
}) => {
  return (
    <div className="mt-4 rounded-xl shadow-lg flex flex-col gap-4">
      <div className="bg-[#FFFFFF] flex flex-col gap-4 p-4 rounded-xl">
        <h2 className="font-semibold text-base">Current plan</h2>
        <div className="bg-[#F3F4F6] rounded-lg p-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold text-[#0F172A]">Pro</h1>
            <h3 className="text-sm font-normal text-[#64748B]">
              Renewal date: 10/08/2025
            </h3>
          </div>
          <button
            onClick={changePlanHandler}
            className="px-4 py-2 border-1 border-[#EAB308] bg-[#FFFFFF] text-[#CA8A04] rounded-lg"
          >
            Change plan
          </button>
        </div>
      </div>
    </div>
  );
};
