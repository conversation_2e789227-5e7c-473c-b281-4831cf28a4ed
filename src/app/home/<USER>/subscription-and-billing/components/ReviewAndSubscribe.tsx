import { Button } from '@/components/Button';
import { cardIc, editIc, fromTo, logo } from '@/utils/icon';

export const ReviewAndSubcribe = () => {
  return (
    <div className="mt-8 border-1 border-[#CBD5E1] rounded-xl shadow-lg flex gap-4">
      <div className="w-[60%] bg-[#FFFFFF] p-4 flex flex-col gap-4 rounded-l-xl">
        <h1 className="text-base font-semibold text-[#000000]">
          Payment method
        </h1>
        <div className="border-1 border-[#CBD5E1] rounded-lg p-4 flex justify-between items-center">
          <div className="flex gap-4 items-stretch">
            <div className="flex items-center">{cardIc}</div>
            <div className="min-w-[135px]">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Credit Card</p>
                {/* <div className="bg-[#FFE600] p-1 text-xs font-medium rounded-lg">
                  Primary
                </div> */}
              </div>
              <h1 className="font-normal text-sm text-[#0F172A]">
                **** **** **** 1234
              </h1>
            </div>
            <div className="flex items-end">
              <h3 className="text-sm font-normal text-[#64748B]">
                Expire 08/2025
              </h3>
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <button>{editIc()}</button>
          </div>
        </div>
        <div className="border-1 border-[#CBD5E1] rounded-lg p-4 flex justify-between items-center">
          <div className="flex gap-4 items-center">
            <div className="text-sm font-medium text-[#000000]">
              Save with yearly billing
            </div>
            <div className="bg-[#29845A] rounded-lg p-2 text-[#F8FFFB] text-xs font-medium">
              $120 discount
            </div>
          </div>
          <label className="inline-flex items-center cursor-pointer">
            <input type="checkbox" value="" className="sr-only peer" />
            <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>
      <div className="w-[40%] p-4 ">
        <div className="bg-white rounded-xl">
          <div className="p-4 flex justify-between border-b-1 ">
            <div className="flex gap-4 items-center">
              {logo}
              <div>
                <h3 className="text-base font-semibold text-[#000000]">
                  Pro plan
                </h3>
                <p className="text-sm font-normal text-[#0F172A]">Yearly</p>
              </div>
            </div>
            <div>
              <h3 className="text-base font-semibold text-[#000000]">
                $120.00
              </h3>
            </div>
          </div>
          <div className="p-4 border-b-1">
            <div className="flex gap-4 ">
              {fromTo}
              <div className="w-full">
                <div className="flex justify-between">
                  <div>
                    <h3 className="text-base font-semibold text-[#000000]">
                      Today
                    </h3>
                    <p className="text-sm font-normal text-[#0F172A]">Basic</p>
                  </div>
                  <h3 className="text-base font-semibold text-[#000000]">
                    $0.00
                  </h3>
                </div>
                <div className="mt-4 flex justify-between">
                  <div>
                    <h3 className="text-base font-semibold text-[#000000]">
                      Sep 14,2025
                    </h3>
                    <p className="text-sm font-normal text-[#0F172A]">Pro</p>
                  </div>
                  <h3 className="text-base font-semibold text-[#000000]">
                    $140.00
                  </h3>
                </div>
              </div>
            </div>
          </div>
          <div className="p-4">
            <div className="flex justify-between">
              <h3 className="text-base font-semibold text-[#000000]">
                Amount due
              </h3>
              <h3 className="text-base font-semibold text-[#000000]">
                $140.00
              </h3>
            </div>
            <p>Total</p>
            <Button>Subscribe</Button>
            <p className="mt-2 text-center text-sm font-normal text-[#94A3B8]">
              Change or cancel your plan at any time.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
