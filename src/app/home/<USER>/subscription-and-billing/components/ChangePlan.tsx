import { Button } from '@/components/Button';
import { Plan } from '@/types';
import { diamond } from '@/utils/icon';

export const ChangePlan = ({ onSelectPlan }: { onSelectPlan: () => void }) => {
  const plans: Plan[] = [
    {
      id: 1,
      name: 'Free',
      price: 0,
      for: 'starter',
      isSelected: true,
      features: [
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
      ],
    },
    {
      id: 2,
      name: 'Pro',
      price: 140,
      for: 'small merchant',
      isSelected: false,
      features: [
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
      ],
    },
    {
      id: 3,
      name: 'Premium',
      price: 299,
      for: 'enterprise',
      isSelected: false,
      features: [
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
        'Main feature',
      ],
    },
  ];
  return (
    <div className="mt-8 flex gap-6 items-strech">
      {plans.map((plan) => (
        <div key={plan.id} className="flex-1 place-content-end">
          {plan.isSelected && (
            <div className="bg-[#E5E7EB] text-[#64748B] text-xs rounded-t-xl flex items-center justify-center p-3 -mb-2">
              Current plan
            </div>
          )}
          <div className="bg-[#FFFFFF] rounded-xl p-4 shadow-lg flex flex-col gap-4">
            <div className="flex flex-col gap-1">
              <h3 className="text-base font-medium text-[#0F172A] flex justify-between items-center">
                {plan.name}
                {plan.name === 'Pro' && diamond}
              </h3>
              <h4 className="text-xs font-medium text-[#94A3B8]">
                For {plan.for}
              </h4>
            </div>
            <h1 className="text-xl font-bold text-[#0F172A] flex items-center gap-2">
              {plan.price == 0 ? 'Free' : `${plan.price}`}{' '}
              {plan.price != 0 && (
                <span className="text-sm font-medium text-[#64748B]">
                  USD/month
                </span>
              )}
            </h1>
            <Button
              onClickFunc={onSelectPlan}
              className={`${plan.isSelected && '!bg-white border-1 border-[#CBD5E1]'}`}
            >
              {plan.isSelected ? 'Keep current plan' : 'Select plan'}
            </Button>
            <div className="bg-[#E5E7EB] rounded-lg p-4">
              <ul className="flex flex-col gap-2 list-disc p-2">
                {plan.features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
