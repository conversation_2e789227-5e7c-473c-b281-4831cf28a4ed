import AddEditPaymentMethod from '@/components/modal/AddEditPaymentMethod';
import DeletePaymentMethod from '@/components/modal/DeletePaymentMethod';
import { cardIc, deleteIc, editIc } from '@/utils/icon';
import { useState } from 'react';
export const PaymentMethod = () => {
  const [changePaymentMethod, setChangePaymentMethod] = useState(false);
  const [deletePaymentMethod, setDeletePaymentMethod] = useState(false);

  const closeAddEditModal = () => {
    setChangePaymentMethod(false);
  };
  const openAddEditModal = () => {
    setChangePaymentMethod(true);
  };
  const openDeleteModal = () => {
    setDeletePaymentMethod(true);
  };
  const closeDeleteModal = () => {
    setDeletePaymentMethod(false);
  };
  const confirmHandler = () => {
    alert('Add/Edit successfully!!!');
    setChangePaymentMethod(false);
  };
  const deleteHandler = () => {
    alert('Delete successfully!!!');
    setDeletePaymentMethod(false);
  };
  return (
    <div className="mt-4 rounded-xl shadow-lg flex flex-col gap-4">
      <div className="bg-[#FFFFFF] flex flex-col gap-4 p-4 rounded-xl">
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-base">Payment method</h2>
          <a
            className="text-sm font-medium text-[#1E40AF] cursor-pointer"
            onClick={openAddEditModal}
          >
            Add payment method
          </a>
        </div>
        <div className="border-1 border-[#CBD5E1] rounded-lg p-4 flex justify-between items-center">
          <div className="flex gap-4 items-stretch">
            <div className="flex items-center">{cardIc}</div>
            <div className="min-w-[135px]">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">Credit Card</p>
                {/* <div className="bg-[#FFE600] p-1 text-xs font-medium rounded-lg">
                  Primary
                </div> */}
              </div>
              <h1 className="font-normal text-sm text-[#0F172A]">
                **** **** **** 1234
              </h1>
            </div>
            <div className="flex items-end">
              <h3 className="text-sm font-normal text-[#64748B]">
                Expire 08/2025
              </h3>
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <button onClick={openDeleteModal}>{deleteIc()}</button>
            <button onClick={openAddEditModal}>{editIc()}</button>
          </div>
        </div>
      </div>
      {changePaymentMethod && (
        <AddEditPaymentMethod
          onClose={closeAddEditModal}
          onConfirm={confirmHandler}
        />
      )}
      {deletePaymentMethod && (
        <DeletePaymentMethod
          onClose={closeDeleteModal}
          onConfirm={deleteHandler}
        />
      )}
    </div>
  );
};
