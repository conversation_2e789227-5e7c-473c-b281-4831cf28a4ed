import { Billing } from '@/types';
import { download } from '@/utils/icon';
import { useState } from 'react';

const data: Billing[] = [
  {
    id: 1,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
  {
    id: 2,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
  {
    id: 3,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
  {
    id: 4,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
  {
    id: 5,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
  {
    id: 6,
    date: '10/08/2024',
    billCode: '21654127654',
    paid: 140,
    isChecked: false,
  },
];
export const BillingHistory = () => {
  const [billingHistory, setBillingHistory] = useState(data);
  const selectBillHandler = (
    index: number,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setBillingHistory((prev) => {
      return prev.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            isChecked: event.target.checked,
          };
        }
        return item;
      });
    });
  };

  const selectAllBillsHandler = () => {
    setBillingHistory(
      billingHistory.map((item) => {
        return {
          ...item,
          isChecked: true,
        };
      })
    );
  };

  const deselectAllBillsHandler = () => {
    setBillingHistory(
      billingHistory.map((item) => {
        return {
          ...item,
          isChecked: false,
        };
      })
    );
  };

  const numberOfSelected = billingHistory.filter(
    (item) => item.isChecked
  ).length;
  return (
    <div className="mt-4 rounded-xl shadow-lg flex flex-col gap-4">
      <div className="bg-[#FFFFFF] flex flex-col gap-4 p-4 rounded-xl">
        <div className="flex justify-between items-center">
          <h2 className="font-semibold text-base">Billing history</h2>
          <a className="text-sm font-medium text-[#1E40AF] cursor-pointer">
            Download all invoice
          </a>
        </div>

        <div className="relative overflow-x-auto shadow-md sm:rounded-lg">
          {numberOfSelected > 0 && (
            <div className="p-4 flex gap-10 items-center">
              <button
                className="w-4 h-4 flex items-center justify-center rounded-sm bg-blue-600 text-white"
                onClick={deselectAllBillsHandler}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M5 12h14"
                  />
                </svg>
              </button>

              <p className="text-sm font-medium text-[#0F172A]">
                {numberOfSelected} selected
              </p>
              <a className="text-sm font-medium text-[#1E40AF] cursor-pointer">
                Download invoices
              </a>
            </div>
          )}
          <table className="w-full text-sm text-left rtl:text-right text-gray-500 ">
            {numberOfSelected === 0 && (
              <thead className="text-xs text-[#0F172A] font-semibold bg-gray-50 ">
                <tr>
                  <th scope="col" className="p-4">
                    <div className="flex items-center">
                      <input
                        id="checkbox-all-search"
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm"
                        onChange={selectAllBillsHandler}
                      />
                      <label htmlFor="checkbox-all-search" className="sr-only">
                        checkbox
                      </label>
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Bill Code
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Paid
                  </th>
                  <th scope="col" className="px-6 py-3"></th>
                </tr>
              </thead>
            )}
            <tbody>
              {billingHistory.map((item, index) => (
                <tr
                  key={index}
                  className="text-gray-900 font-medium bg-white border-b  border-gray-200 hover:bg-gray-50 "
                >
                  <td className="w-4 p-4">
                    <div className="flex items-center">
                      <input
                        id="checkbox-table-search-1"
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-lg checked:text-white"
                        onChange={(e) => {
                          selectBillHandler(index, e);
                        }}
                        checked={item.isChecked}
                      />
                      <label
                        htmlFor="checkbox-table-search-1"
                        className="sr-only"
                      >
                        checkbox
                      </label>
                    </div>
                  </td>
                  <th
                    scope="row"
                    className="px-6 py-4 font-medium whitespace-nowrap"
                  >
                    {item.date}
                  </th>
                  <td className="px-6 py-4 ">#{item.billCode}</td>
                  <td className="px-6 py-4">{item.paid} $</td>
                  <td className="px-6 py-4">
                    <a
                      href="#"
                      className="font-medium text-blue-600 hover:underline"
                    >
                      {download}
                    </a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <nav
            className="flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
            aria-label="Table navigation"
          >
            <span className="text-sm font-normal text-gray-500  mb-4 md:mb-0 block w-full md:inline md:w-auto">
              Showing <span className="font-semibold text-gray-900 ">1-10</span>{' '}
              of <span className="font-semibold text-gray-900 ">1000</span>
            </span>
            <ul className="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 "
                >
                  Previous
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
                >
                  1
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
                >
                  2
                </a>
              </li>
              <li>
                <a
                  href="#"
                  aria-current="page"
                  className="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 "
                >
                  3
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
                >
                  4
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
                >
                  5
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 "
                >
                  Next
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  );
};
