'use client';
import { useAppDispatch } from '@/app/hook';
import { showSettingSidebarHandler } from '@/app/store/ui';
import { back } from '@/utils/icon';
import { useState } from 'react';
import { BillingHistory } from './components/BillingHistory';
import { ChangePlan } from './components/ChangePlan';
import { CurrentPlan } from './components/CurrentPlan';
import { PaymentMethod } from './components/PaymentMethod';
import { ReviewAndSubcribe } from './components/ReviewAndSubscribe';

const SubscriptionAndBilling = () => {
  const dispatch = useAppDispatch();
  const [isChangePlan, setIsChangePlan] = useState(false);
  const [isSelectAndReview, setIsSelectAndReview] = useState(false);

  const changePlanHandler = () => {
    setIsChangePlan(true);
  };
  const selectPlanHandler = () => {
    setIsSelectAndReview(true);
    setIsChangePlan(false);
    dispatch(showSettingSidebarHandler(false));
  };
  const reviewAndSubscribeBackHandler = () => {
    setIsSelectAndReview(false);
    setIsChangePlan(true);
    dispatch(showSettingSidebarHandler(true));
  };
  const changePlanBackHandler = () => {
    setIsChangePlan(false);
  };

  let context = (
    <>
      <h1 className="text-2xl font-semibold ">Subscription & Billing</h1>
      <CurrentPlan changePlanHandler={changePlanHandler} />
      <PaymentMethod />
      <BillingHistory />
    </>
  );
  if (isSelectAndReview) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span
            className="cursor-pointer"
            onClick={reviewAndSubscribeBackHandler}
          >
            {back}
          </span>
          Review and subcribe
        </h1>
        <ReviewAndSubcribe />
      </>
    );
  }
  if (isChangePlan) {
    context = (
      <>
        <h1 className="text-2xl font-semibold flex gap-4 items-center">
          <span className="cursor-pointer" onClick={changePlanBackHandler}>
            {back}
          </span>
          Plan
        </h1>
        <ChangePlan onSelectPlan={selectPlanHandler} />
      </>
    );
  }

  return <div className="w-full max-w-4xl p-4 font-sans">{context}</div>;
};

export default SubscriptionAndBilling;
