'use client';
import DropDownMenu from '@/components/DropDownMenu';
import Modal from '@/components/modal/Modal';
import PermissionMatrixModal from '@/components/modal/PermissionMatrixModal';
import { back, deleteIc, eyeon, option } from '@/utils/icon';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

// Types
interface UserDetail {
  id: number;
  name: string;
  email: string;
  activeSince: string;
  role: string;
  permissions: Permission[];
}

interface Permission {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  category:
    | 'listings'
    | 'analytics'
    | 'billing'
    | 'users'
    | 'content'
    | 'marketplaces';
}

// Mock user data
const mockUserDetail: UserDetail = {
  id: 2,
  name: '<PERSON>',
  email: '<EMAIL>',
  activeSince: '17/03/2025 15:24',
  role: 'Admin',
  permissions: [
    {
      id: 'create-listings',
      name: 'Create Listings',
      description:
        'Can create new listings within assigned marketplaces and categories.',
      enabled: true,
      category: 'listings',
    },
    {
      id: 'edit-listings',
      name: 'Edit Listings',
      description:
        'Can edit existing listings within assigned marketplaces and categories.',
      enabled: true,
      category: 'listings',
    },
    {
      id: 'publish-listings',
      name: 'Publish Listings',
      description:
        'Can publish listings within assigned marketplaces and categories.',
      enabled: true,
      category: 'listings',
    },
    {
      id: 'approve-listings',
      name: 'Approve Listings',
      description:
        'Can approve listings within assigned marketplaces and categories.',
      enabled: true,
      category: 'listings',
    },
    {
      id: 'delete-listings',
      name: 'Delete Listings',
      description:
        'Can delete existing listings within assigned marketplaces and categories.',
      enabled: true,
      category: 'listings',
    },
    {
      id: 'manage-aplus-content',
      name: 'Manage A+ Content',
      description: 'Manage or approve A+ Content for all product listings.',
      enabled: true,
      category: 'content',
    },
    {
      id: 'view-analytics',
      name: 'View Analytics',
      description:
        'Can view sales data, performance reports, and other metrics for assigned marketplaces.',
      enabled: true,
      category: 'analytics',
    },
    {
      id: 'view-audit-logs',
      name: 'View Audit Logs',
      description:
        'Can see logs for actions taken by team members in the assigned regions, categories, or marketplaces.',
      enabled: true,
      category: 'analytics',
    },
    {
      id: 'manage-billing',
      name: 'Manage Billing',
      description:
        'View and update payment methods, subscription plans, and invoices.',
      enabled: false,
      category: 'billing',
    },
    {
      id: 'invite-users',
      name: 'Invite Users',
      description:
        'Can add new users, assign role and access to marketplaces and categories.',
      enabled: true,
      category: 'users',
    },
    {
      id: 'access-all-marketplaces',
      name: 'Access to all Marketplaces',
      description: '',
      enabled: true,
      category: 'marketplaces',
    },
    {
      id: 'assign-roles-permissions',
      name: 'Assign Roles/Permissions',
      description:
        'Can assign roles and permissions to users within assigned marketplaces and categories.',
      enabled: true,
      category: 'users',
    },
  ],
};

export default function UserDetailPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.userId as string;
  const [user] = useState(mockUserDetail);
  const [showMoreActions, setShowMoreActions] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showPermissionMatrix, setShowPermissionMatrix] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const handleAssignOtherRole = () => {
    // Handle assign other role logic
    console.log('Assign other role');
  };

  const handleViewPermission = (permissionId: string) => {
    // Handle view permission logic
    console.log('View permission:', permissionId);
  };

  const handleRemovePermission = (permissionId: string) => {
    // Handle remove permission logic
    console.log('Remove permission:', permissionId);
  };

  const handleViewPermissionRole = () => {
    setShowRoleModal(true);
  };

  const handleViewPermissionMatrix = () => {
    setShowPermissionMatrix(true);
  };

  return (
    <div className="flex-1 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <button
            onClick={handleBack}
            className="mr-3 p-1 hover:bg-gray-100 rounded"
          >
            {back}
          </button>
          <h1 className="text-xl font-semibold text-gray-900">{user.name}</h1>
        </div>
        <div className="relative">
          <button
            onClick={() => setShowMoreActions(!showMoreActions)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            More actions
            <span className="ml-2">▼</span>
          </button>
          {showMoreActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Edit User
                </button>
                <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Reset Password
                </button>
                <button className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                  Deactivate User
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Info Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email address
            </label>
            <div className="bg-gray-50 px-3 py-2 rounded-md text-sm text-gray-900">
              {user.email}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Active since
            </label>
            <div className="bg-gray-50 px-3 py-2 rounded-md text-sm text-gray-900">
              {user.activeSince}
            </div>
          </div>
        </div>
      </div>

      {/* Roles Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Roles</h2>
            <p className="text-sm text-gray-500">
              Assign roles to grant permissions.
            </p>
          </div>
          <button
            onClick={handleViewPermissionRole}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            View Permission/Role
          </button>
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
              {user.role}
            </span>
            <button
              onClick={handleAssignOtherRole}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Assign other role
            </button>
          </div>
          <DropDownMenu
            trigger={
              <div className="bg-white rounded-full p-1 shadow-sm">
                {option}
              </div>
            }
            position="top-right"
          >
            <button className="flex items-center justify-between w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100">
              <span className="text-justify">View permission</span>
              {eyeon}
            </button>

            <button className="flex items-center justify-between w-full px-4 py-3 text-sm text-red-600 hover:bg-gray-100">
              <span className="text-justify">Remove</span>
              {deleteIc()}
            </button>
          </DropDownMenu>
        </div>
      </div>

      {/* Permissions Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-2">
              Permissions
            </h2>
            <p className="text-sm text-gray-500">
              A list of permissions from all assigned custom roles.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {user.permissions.map((permission) => (
            <div
              key={permission.id}
              className="p-4 border border-gray-200 rounded-lg"
            >
              <div className="flex items-start">
                <div className="mr-3 mt-1">
                  {permission.enabled ? (
                    <div className="w-5 h-5rounded flex items-center justify-center">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M20.7071 5.29289C21.0976 5.68342 21.0976 6.31658 20.7071 6.70711L9.70711 17.7071C9.31658 18.0976 8.68342 18.0976 8.29289 17.7071L3.29289 12.7071C2.90237 12.3166 2.90237 11.6834 3.29289 11.2929C3.68342 10.9024 4.31658 10.9024 4.70711 11.2929L9 15.5858L19.2929 5.29289C19.6834 4.90237 20.3166 4.90237 20.7071 5.29289Z"
                          fill="#030712"
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="w-5 h-5 flex items-center justify-center">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M21 12C21 7.02944 16.9706 3 12 3C9.87498 3 7.92249 3.73712 6.38281 4.96875L19.0303 17.6162C20.2619 16.0766 21 14.125 21 12ZM3 12C3 16.9706 7.02944 21 12 21C14.125 21 16.0766 20.2619 17.6162 19.0303L4.96875 6.38281C3.73712 7.92249 3 9.87498 3 12ZM23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12Z"
                          fill="#030712"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">
                    {permission.name}
                  </h3>
                  {permission.description && (
                    <p className="text-sm text-gray-500 mt-1">
                      {permission.description}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Role Modal */}
      {showRoleModal && (
        <Modal
          onClose={() => setShowRoleModal(false)}
          className="sm:w-full sm:max-w-4xl"
        >
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900">Super Admin</h3>
          </div>

          <div className="mb-6">
            <p className="text-sm text-gray-600 mb-2">
              • Manages day-to-day operations and team members but has a more
              restricted scope compared to the Super Admin.
            </p>
            <p className="text-sm text-gray-600">
              • Can manage listings, content, and approval workflows but cannot
              modify global settings or billing.
            </p>
          </div>

          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Permissions
            </h4>
            <div className="grid grid-cols-2 gap-x-8 gap-y-4 max-h-80 overflow-y-auto">
              {user.permissions.map((permission) => (
                <div
                  key={permission.id}
                  className="p-4 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-start">
                    <div className="mr-3 mt-1">
                      {permission.enabled ? (
                        <div className="w-5 h-5rounded flex items-center justify-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M20.7071 5.29289C21.0976 5.68342 21.0976 6.31658 20.7071 6.70711L9.70711 17.7071C9.31658 18.0976 8.68342 18.0976 8.29289 17.7071L3.29289 12.7071C2.90237 12.3166 2.90237 11.6834 3.29289 11.2929C3.68342 10.9024 4.31658 10.9024 4.70711 11.2929L9 15.5858L19.2929 5.29289C19.6834 4.90237 20.3166 4.90237 20.7071 5.29289Z"
                              fill="#030712"
                            />
                          </svg>
                        </div>
                      ) : (
                        <div className="w-5 h-5 flex items-center justify-center">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M21 12C21 7.02944 16.9706 3 12 3C9.87498 3 7.92249 3.73712 6.38281 4.96875L19.0303 17.6162C20.2619 16.0766 21 14.125 21 12ZM3 12C3 16.9706 7.02944 21 12 21C14.125 21 16.0766 20.2619 17.6162 19.0303L4.96875 6.38281C3.73712 7.92249 3 9.87498 3 12ZM23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12Z"
                              fill="#030712"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-gray-900">
                        {permission.name}
                      </h3>
                      {permission.description && (
                        <p className="text-sm text-gray-500 mt-1">
                          {permission.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={() => setShowRoleModal(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Close
            </button>
          </div>
        </Modal>
      )}

      {/* Permission Matrix Modal */}
      {showPermissionMatrix && (
        <PermissionMatrixModal onClose={() => setShowPermissionMatrix(false)} />
      )}
    </div>
  );
}
