'use client';
import Modal from '@/components/modal/Modal';
import PermissionMatrixModal from '@/components/modal/PermissionMatrixModal';
import { add, deleteIc, editIc, eyeon } from '@/utils/icon';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Types
interface User {
  id: number;
  name: string;
  status: 'Active' | 'Inactive';
  role: string;
  email: string;
}

interface Role {
  name: string;
  users: number;
}

interface ActivityLog {
  id: number;
  event: string;
  resource: string;
  date: string;
  user: string;
}

// Mock data
const mockUsers: User[] = [
  {
    id: 1,
    name: 'Phuong <PERSON>',
    status: 'Active',
    role: 'Super Admin',
    email: '<EMAIL>',
  },
  {
    id: 2,
    name: '<PERSON>',
    status: 'Inactive',
    role: 'Admin',
    email: '<EMAIL>',
  },
  {
    id: 3,
    name: 'Viet Tran',
    status: 'Inactive',
    role: 'Admin',
    email: '<EMAIL>',
  },
  {
    id: 4,
    name: '<PERSON><PERSON>',
    status: 'Active',
    role: 'Team Member',
    email: '<EMAIL>',
  },
  {
    id: 5,
    name: '<PERSON><PERSON><PERSON>en',
    status: 'Active',
    role: 'Team Member',
    email: '<EMAIL>',
  },
  {
    id: 6,
    name: 'Truc Nguyen',
    status: 'Inactive',
    role: 'Team Member',
    email: '<EMAIL>',
  },
];

const mockRoles: Role[] = [
  { name: 'Super Admin', users: 1 },
  { name: 'Admin', users: 2 },
  { name: 'Regional Admin', users: 0 },
  { name: 'Support Admin', users: 0 },
  { name: 'Team Member', users: 3 },
];

const mockActivityLogs: ActivityLog[] = [
  {
    id: 1,
    event: 'New Listing was created',
    resource: 'Product Listing',
    date: '10/08/2024 5:40',
    user: 'Phuong C',
  },
  {
    id: 2,
    event: 'A+ Content was updated',
    resource: 'A+ Content',
    date: '10/08/2024 5:40',
    user: 'Tam Nguyen',
  },
  {
    id: 3,
    event: 'A+ Content was removed',
    resource: 'Settings',
    date: '10/08/2024 5:40',
    user: 'Viet Tran',
  },
  {
    id: 4,
    event: 'Test was edited',
    resource: 'Notifications',
    date: '10/08/2024 5:40',
    user: 'Huy To',
  },
];

// Components
interface UsersTableProps {
  users: User[];
  onSort: (key: string) => void;
  getSortIcon: (key: string) => string;
  onInviteUser: () => void;
  onViewUser: (userId: number) => void;
  onDeleteUser: (user: User) => void;
}

const UsersTable = ({
  users,
  onSort,
  getSortIcon,
  onInviteUser,
  onViewUser,
  onDeleteUser,
}: UsersTableProps) => {
  const getStatusBadge = (status: string) => {
    if (status === 'Active') {
      return 'bg-green-100 text-green-700';
    }
    return 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-gray-900">Users</h2>
        <button
          onClick={onInviteUser}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          <span className="mr-2">{add}</span>
          Invite user
        </button>
      </div>

      <div className="overflow-hidden">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300"
                />
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                onClick={() => onSort('name')}
              >
                User{getSortIcon('name')}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                onClick={() => onSort('status')}
              >
                Status{getSortIcon('status')}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50"
                onClick={() => onSort('role')}
              >
                Role{getSortIcon('role')}
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr
                key={user.id}
                className="border-b border-gray-100 hover:bg-gray-50"
              >
                <td className="px-4 py-4">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300"
                  />
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm font-medium text-gray-900">
                    {user.name}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(user.status)}`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full mr-2 ${user.status === 'Active' ? 'bg-green-500' : 'bg-gray-400'}`}
                    ></span>
                    {user.status}
                  </span>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm text-gray-900">{user.role}</div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => onViewUser(user.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {eyeon}
                    </button>
                    {user.role !== 'Super Admin' && (
                      <>
                        <button className="text-blue-600 hover:text-blue-800">
                          {editIc()}
                        </button>
                        <button
                          className="text-red-600 hover:text-red-800"
                          onClick={() => onDeleteUser(user)}
                        >
                          {deleteIc()}
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

interface RolesTableProps {
  roles: Role[];
}

const RolesTable = ({ roles }: RolesTableProps) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Roles</h2>

      <div className="overflow-hidden">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                Name ↓
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                Users
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {roles.map((role, index) => (
              <tr
                key={index}
                className="border-b border-gray-100 hover:bg-gray-50"
              >
                <td className="px-4 py-4">
                  <div className="text-sm font-medium text-gray-900">
                    {role.name}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="text-sm text-gray-900">{role.users}</div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-3">
                    <button className="text-gray-400 hover:text-gray-600">
                      {eyeon}
                    </button>
                    {role.name !== 'Super Admin' && (
                      <button className="text-blue-600 hover:text-blue-800">
                        {editIc()}
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const PermissionMatrix = () => {
  const permissions = [
    'Create Listings',
    'Edit Listings',
    'Publish Listings',
    'Approve Listings',
    'Delete Listings',
    'Manage A+ Content',
    'View Analytics',
    'View Audit Logs',
    'Manage Billings',
    'Invite Users',
    'Access to All Marketplaces',
    'Assign Roles/Permissions',
  ];

  const roles = [
    'Super Admin',
    'Admin',
    'Regional Admin',
    'Support Admin',
    'Team Member',
  ];

  // Permission matrix data - true means enabled, false means disabled
  const permissionMatrix = {
    'Create Listings': [false, true, true, false, true],
    'Edit Listings': [false, true, true, false, true],
    'Publish Listings': [false, true, false, false, false],
    'Approve Listings': [false, true, true, false, false],
    'Delete Listings': [false, true, false, false, false],
    'Manage A+ Content': [false, true, true, false, true],
    'View Analytics': [false, true, true, true, true],
    'View Audit Logs': [false, true, true, true, false],
    'Manage Billings': [false, false, false, false, false],
    'Invite Users': [false, true, true, false, false],
    'Access to All Marketplaces': [false, false, true, false, false],
    'Assign Roles/Permissions': [false, true, true, false, false],
  };

  const getCheckboxIcon = (enabled: boolean, isDisabled: boolean = false) => {
    if (isDisabled) {
      return (
        <div className="w-5 h-5 bg-gray-300 rounded border border-gray-400 flex items-center justify-center">
          <svg
            className="w-3 h-3 text-gray-500"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    }

    if (enabled) {
      return (
        <div className="w-5 h-5 bg-black rounded border border-black flex items-center justify-center">
          <svg
            className="w-3 h-3 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    }

    return <div className="w-5 h-5 border-2 border-gray-300 rounded"></div>;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">
        Permission Matrix
      </h2>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-700"></th>
              {roles.map((role) => (
                <th
                  key={role}
                  className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                >
                  <div className="flex flex-col items-center">
                    <span>{role.split(' ')[0]}</span>
                    <span>{role.split(' ')[1] || ''}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {permissions.map((permission) => (
              <tr
                key={permission}
                className="border-b border-gray-100 hover:bg-gray-50"
              >
                <td className="px-4 py-4 text-sm text-gray-900 font-medium">
                  {permission}
                </td>
                {roles.map((role, roleIndex) => (
                  <td
                    key={`${permission}-${role}`}
                    className="px-4 py-4 text-center"
                  >
                    {getCheckboxIcon(
                      permissionMatrix[
                        permission as keyof typeof permissionMatrix
                      ][roleIndex],
                      roleIndex === 0 // Super Admin is disabled (grayed out)
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

interface ActivityLogsProps {
  onViewLogs: () => void;
}

const ActivityLogs = ({ onViewLogs }: ActivityLogsProps) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-lg font-medium text-gray-900 mb-2">
            Activity logs
          </h2>
          <p className="text-sm text-gray-500">
            Monitor and review user management activities within the
            organization.
          </p>
        </div>
        <button
          onClick={onViewLogs}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View
        </button>
      </div>
    </div>
  );
};

interface ActivityLogsViewProps {
  onClose: () => void;
}

const ActivityLogsView = ({ onClose }: ActivityLogsViewProps) => {
  const [sortConfig, setSortConfig] = useState({ key: '', direction: '' });
  const [activityLogs] = useState(mockActivityLogs);

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const getSortIcon = (key: string) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'asc' ? ' ↑' : ' ↓';
    }
    return ' ↓';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <button
            onClick={onClose}
            className="mr-3 p-1 hover:bg-gray-100 rounded"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h3 className="text-lg font-semibold text-gray-900">Activity logs</h3>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded-full"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {/* Activity Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="">
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('event')}
              >
                Event {getSortIcon('event')}
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('resource')}
              >
                Resource {getSortIcon('resource')}
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('date')}
              >
                Date {getSortIcon('date')}
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('user')}
              >
                User {getSortIcon('user')}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activityLogs.map((log) => (
              <tr key={log.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.event}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.resource}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.date}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {log.user}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

interface InviteUserFormProps {
  onClose: () => void;
}

const InviteUserForm = ({ onClose }: InviteUserFormProps) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [selectedRole, setSelectedRole] = useState('');
  const [showRoleDropdown, setShowRoleDropdown] = useState(false);
  const [showPermissionMatrix, setShowPermissionMatrix] = useState(false);

  const roles = ['Admin', 'Regional Admin', 'Support Admin', 'Team Member'];

  const handleSendInvite = () => {
    // Handle send invite logic
    console.log('Sending invite to:', email, 'with role:', selectedRole);
    onClose();
  };

  const handleDiscard = () => {
    onClose();
  };

  const handleAssignRole = (role: string) => {
    setSelectedRole(role);
    setShowRoleDropdown(false);
  };

  const handleViewPermissionMatrix = () => {
    setShowPermissionMatrix(true);
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm p-6 mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button
              onClick={onClose}
              className="mr-3 p-1 hover:bg-gray-100 rounded"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <h3 className="text-lg font-semibold text-gray-900">Invite user</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Email Section */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email *
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter email address"
          />
        </div>

        {/* Roles Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              Roles
            </label>
            <button
              onClick={handleViewPermissionMatrix}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View Permission/Role
            </button>
          </div>
          <p className="text-sm text-gray-500 mb-3">
            Assign roles to grant permissions.
          </p>

          <div className="relative">
            <button
              onClick={() => setShowRoleDropdown(!showRoleDropdown)}
              className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-gray-400 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span className="text-sm text-gray-700">
                  {selectedRole || 'Assign role'}
                </span>
              </div>
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {showRoleDropdown && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                {roles.map((role) => (
                  <button
                    key={role}
                    onClick={() => handleAssignRole(role)}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-md last:rounded-b-md"
                  >
                    {role}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleDiscard}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Discard
          </button>
          <button
            onClick={handleSendInvite}
            className="px-4 py-2 bg-yellow-500 text-white rounded-md text-sm font-medium hover:bg-yellow-600"
            disabled={!email || !selectedRole}
          >
            Send invite
          </button>
        </div>
      </div>

      {/* Permission Matrix Modal */}
      {showPermissionMatrix && (
        <PermissionMatrixModal onClose={() => setShowPermissionMatrix(false)} />
      )}
    </>
  );
};

export default function UserManagement() {
  const router = useRouter();
  const [users, setUsers] = useState(mockUsers);
  const [roles] = useState(mockRoles);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [showActivityLogs, setShowActivityLogs] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [sortConfig, setSortConfig] = useState({ key: '', direction: '' });

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });

    const sortedUsers = [...users].sort((a, b) => {
      if (a[key as keyof typeof a] < b[key as keyof typeof b]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key as keyof typeof a] > b[key as keyof typeof b]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    setUsers(sortedUsers);
  };

  const getSortIcon = (key: string) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'asc' ? ' ↑' : ' ↓';
    }
    return ' ↓';
  };

  const handleViewUser = (userId: number) => {
    router.push(`/home/<USER>/user-management/${userId}`);
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const confirmDeleteUser = () => {
    if (userToDelete) {
      setUsers(users.filter((u) => u.id !== userToDelete.id));
      setShowDeleteModal(false);
      setUserToDelete(null);
    }
  };

  const cancelDeleteUser = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  return (
    <div className="flex-1 max-w-4xl">
      {!showInviteForm && !showActivityLogs ? (
        <>
          <div className="mb-6">
            <h1 className="text-xl font-semibold text-gray-900 mb-6">
              User Management
            </h1>
          </div>

          <UsersTable
            users={users}
            onSort={handleSort}
            getSortIcon={getSortIcon}
            onInviteUser={() => setShowInviteForm(true)}
            onViewUser={handleViewUser}
            onDeleteUser={handleDeleteUser}
          />

          <RolesTable roles={roles} />

          <PermissionMatrix />

          <ActivityLogs onViewLogs={() => setShowActivityLogs(true)} />
        </>
      ) : showInviteForm ? (
        <InviteUserForm onClose={() => setShowInviteForm(false)} />
      ) : (
        <ActivityLogsView onClose={() => setShowActivityLogs(false)} />
      )}

      {/* Delete User Modal */}
      {showDeleteModal && userToDelete && (
        <Modal onClose={cancelDeleteUser} className="sm:max-w-md">
          <div className="flex flex-col items-start">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Remove User
            </h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to remove user{' '}
              <span className="font-semibold">{userToDelete.name}</span>? This
              action cannot be undone.
            </p>
            <div className="flex justify-end w-full gap-3">
              <button
                onClick={cancelDeleteUser}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Not now
              </button>
              <button
                onClick={confirmDeleteUser}
                className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
