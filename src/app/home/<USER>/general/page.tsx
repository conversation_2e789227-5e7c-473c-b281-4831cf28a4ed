'use client';
import { useAppDispatch, useAppSelector } from '@/app/hook';
import { setIsUnsaved } from '@/app/store/ui';
import Input from '@/components/Input';
import MultipleToast from '@/components/MultipleToast';
import { Field } from '@/types';
import { fetchUser, updateProfile } from '@/utils/api';
import { formatDate } from '@/utils/date';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';

interface BasicInfo {
  username: Field;
  password: Field;
  activeSince: Field;
  email: Field;
}

interface Other {
  displayName: Field;
  phoneNumber: Field;
  address: Field;
}

const defaultBasicInfo: BasicInfo = {
  username: { value: '', errorMsg: '' },
  password: { value: '********', errorMsg: '' },
  activeSince: { value: '', errorMsg: '' },
  email: { value: '', errorMsg: '' },
};

const defaultOther: Other = {
  displayName: { value: '', errorMsg: '' },
  phoneNumber: { value: '', errorMsg: '' },
  address: { value: '', errorMsg: '' },
};

const General = () => {
  const dispatch = useAppDispatch();
  const isUnsaved = useAppSelector((state) => state.ui.isUnsaved);

  const [basicInfo, setBasicInfo] = useState(defaultBasicInfo);
  const [other, setOther] = useState(defaultOther);
  const [originalBasicInfo, setOriginalBasicInfo] = useState<BasicInfo | null>(
    null
  );
  const [originalOther, setOriginalOther] = useState<Other | null>(null);
  const [showToast, setShowToast] = useState(false);

  // validate unsaved changes
  useEffect(() => {
    if (!originalBasicInfo || !originalOther) return;

    const hasBasicInfoChanges =
      basicInfo.username.value !== originalBasicInfo.username.value;

    const hasOtherChanges =
      other.displayName.value !== originalOther.displayName.value ||
      other.phoneNumber.value !== originalOther.phoneNumber.value ||
      other.address.value !== originalOther.address.value;

    dispatch(setIsUnsaved(hasBasicInfoChanges || hasOtherChanges));
  }, [basicInfo, other, originalBasicInfo, originalOther, dispatch]);

  // fetch user profile api
  useEffect(() => {
    async function getUserProfileFunc() {
      const response = await fetchUser();
      const {
        username,
        email,
        date_joined,
        phone_number,
        display_name,
        address,
      } = response.data;
      const newBasicInfo = {
        ...basicInfo,
        username: { ...basicInfo.username, value: username || '' },
        email: { ...basicInfo.email, value: email || '' },
        activeSince: {
          ...basicInfo.activeSince,
          value: formatDate(date_joined) || '',
        },
      };

      const newOther = {
        ...other,
        displayName: {
          ...other.displayName,
          value: display_name || '',
        },
        phoneNumber: {
          ...other.phoneNumber,
          value: phone_number || '',
        },
        address: { ...other.address, value: address || '' },
      };

      setBasicInfo(newBasicInfo);
      setOther(newOther);
      setOriginalBasicInfo(newBasicInfo);
      setOriginalOther(newOther);
    }
    getUserProfileFunc();
  }, []);

  const validateOther = (): boolean => {
    let isValid = true;
    const updatedErrors: Other = {
      displayName: { ...other.displayName, errorMsg: '' },
      phoneNumber: { ...other.phoneNumber, errorMsg: '' },
      address: { ...other.address, errorMsg: '' },
    };

    if (!other.displayName.value.trim()) {
      updatedErrors.displayName.errorMsg = 'Display name is required';
      isValid = false;
    }

    if (!other.phoneNumber.value.trim()) {
      updatedErrors.phoneNumber.errorMsg = 'Phone number is required';
      isValid = false;
    } else if (!/^\+?\d[\d\s]{7,14}$/.test(other.phoneNumber.value.trim())) {
      updatedErrors.phoneNumber.errorMsg = 'Invalid phone number format';
      isValid = false;
    }

    if (!other.address.value.trim()) {
      updatedErrors.address.errorMsg = 'Address is required';
      isValid = false;
    }

    setOther(updatedErrors);
    return isValid;
  };

  const saveHandler = (event: any) => {
    event.preventDefault();

    if (!validateOther()) return;

    async function updateProfileFunc() {
      try {
        const userId = Cookies.get('user_id');
        if (!userId) return;

        const response = await updateProfile(
          other.displayName.value,
          other.phoneNumber.value,
          other.address.value
        );
        if (response.status === 200) {
          setShowToast(true);
          dispatch(setIsUnsaved(false));
        }
      } catch (error) {
        const errorData = error.response?.data;

        setOther((prev) => ({
          ...prev,
          displayName: {
            ...prev.displayName,
            errorMsg: errorData?.display_name?.[0] || '',
          },
          phoneNumber: {
            ...prev.phoneNumber,
            errorMsg: errorData?.phone_number?.[0] || '',
          },
          address: {
            ...prev.address,
            errorMsg: errorData?.address?.[0] || '',
          },
        }));

        console.log(errorData);
      }
    }

    updateProfileFunc();
  };

  return (
    <div className="w-full max-w-4xl p-4">
      <h1 className="text-2xl font-semibold ">General</h1>
      <form className="mt-4 flex flex-col gap-4">
        <div className="rounded-lg bg-[#FFFFFF] shadow-lg">
          <div className="flex flex-col gap-4 p-4">
            <h2 className="font-semibold text-base">Basic information</h2>
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Username"
                value={basicInfo.username.value}
                onChangeFunc={(e) =>
                  setBasicInfo({
                    ...basicInfo,
                    username: {
                      ...basicInfo.username,
                      value: e.target.value,
                      errorMsg: '',
                    },
                  })
                }
                required
                errorMessage={basicInfo.username.errorMsg}
              />
              <Input
                label="Password"
                value={basicInfo.password.value}
                inputClassName="bg-gray-300"
                disabled
              />
              <Input
                label="Active since"
                value={basicInfo.activeSince.value}
                inputClassName="bg-gray-300"
                disabled
              />
              <Input
                label="Email address"
                value={basicInfo.email.value}
                inputClassName="bg-gray-300"
                disabled
              />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-[#FFFFFF] shadow-lg">
          <div className="flex flex-col gap-4 p-4">
            <h2 className="font-semibold text-base">Other</h2>
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Display name"
                value={other.displayName.value}
                onChangeFunc={(e) =>
                  setOther({
                    ...other,
                    displayName: {
                      ...other.displayName,
                      value: e.target.value,
                      errorMsg: '',
                    },
                  })
                }
                errorMessage={other.displayName.errorMsg}
              />
              <Input
                label="Phone number"
                value={other.phoneNumber.value}
                onChangeFunc={(e) =>
                  setOther({
                    ...other,
                    phoneNumber: {
                      ...other.phoneNumber,
                      value: e.target.value,
                      errorMsg: '',
                    },
                  })
                }
                errorMessage={other.phoneNumber.errorMsg}
              />
              <Input
                label="Address"
                value={other.address.value}
                onChangeFunc={(e) =>
                  setOther({
                    ...other,
                    address: {
                      ...other.address,
                      value: e.target.value,
                      errorMsg: '',
                    },
                  })
                }
                errorMessage={other.address.errorMsg}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            className={` px-6 py-2 rounded-md font-sans text-sm font-medium ${
              isUnsaved
                ? 'bg-[#FACC15] text-[#0F172A]'
                : 'bg-[#E5E7EB] text-[#94A3B8]'
            }`}
            type="submit"
            disabled={!isUnsaved}
            onClick={saveHandler}
          >
            Save
          </button>
        </div>
      </form>
      {showToast && (
        <MultipleToast
          content="Setting saved"
          onDismiss={() => setShowToast(false)}
        />
      )}
    </div>
  );
};
export default General;
