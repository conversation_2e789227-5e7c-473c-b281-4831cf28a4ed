'use client';
import { useAppDispatch, useAppSelector } from '@/app/hook';
import { setIsUnsaved } from '@/app/store/ui';
import { navItems } from '@/components/Sidebar';
import { NavItem } from '@/types';
import { close, logout } from '@/utils/icon';
import Cookies from 'js-cookie';
import { signOut } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import React, { useState } from 'react';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();

  const currentPath = '/' + pathname.split('/')[3];
  const currentItem = navItems[0].children?.find(
    (item) => item.path === currentPath
  );
  const settingItems = navItems[0].children || [];
  const [selectedItem, setSelectedItem] = useState(currentItem);
  const [draftItem, setDraftItem] = useState(settingItems[0]);
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);

  const isUnsaved = useAppSelector((state) => state.ui.isUnsaved);
  const showSettingSidebar = useAppSelector(
    (state) => state.ui.showSettingSidebar
  );

  const changeTabHandler = (item: NavItem) => {
    if (isUnsaved) {
      setShowUnsavedModal(true);
      setDraftItem(item);
    } else {
      setSelectedItem(item);
      router.push(`/home/<USER>/${item.path}`);
    }
  };

  const discardHandler = () => {
    dispatch(setIsUnsaved(false));
    setShowUnsavedModal(false);
    setSelectedItem(draftItem);
    router.push(`/home/<USER>/${draftItem.path}`);
  };

  const saveHandler = () => {
    dispatch(setIsUnsaved(false));
    // call save api
    window.alert('Save info successfully');
    setShowUnsavedModal(false);
    setSelectedItem(draftItem);
    router.push(`/home/<USER>/${draftItem.path}`);
  };

  const closeHandler = () => {
    setShowUnsavedModal(false);
  };

  const hideSettingScreen = () => {
    router.push('/home');
  };

  const logOutHandler = () => {
    signOut({ callbackUrl: '/auth/login' });
    Cookies.remove('session_token');
    Cookies.remove('user_id');
  };

  return (
    <>
      <div
        className="fixed inset-0 bg-gray-500/75 transition-opacity"
        aria-hidden="true"
      ></div>
      <div
        className={`absolute overflow-auto w-full top-[30px] transition-all duration-1000 ease-out transform bottom-0 transform-translate-x-1/2 translate-y-0 opacity-100
        bg-[#F3F4F6] rounded-lg shadow-lg`}
      >
        <div
          className="p-2 flex justify-end cursor-pointer"
          onClick={hideSettingScreen}
        >
          {close}
        </div>
        <div className="container justify-center flex mx-auto gap-6 items-start">
          {showSettingSidebar && (
            <div className="min-w-[25%] rounded-xl shadow-lg">
              <div className="bg-[#F3F4F6] px-6 py-4 rounded-t-xl">
                <p className="text-sm font-medium font-sans text-[#0F172A]">
                  apm-user
                </p>
                <p className="text-xs font-sans font-normal text-[#00000096]">
                  <EMAIL>
                </p>
              </div>
              <ul className="bg-[#FFFFFF] rounded-b-xl p-4">
                {settingItems.map((item) => (
                  <li
                    className={`flex gap-3 px-2 py-3 rounded-lg cursor-pointer hover:bg-[#F3F4F6] ${item.title === selectedItem?.title ? 'bg-[#FACC15]' : ''}`}
                    key={item.title}
                    onClick={() => changeTabHandler(item)}
                  >
                    <span className="text-lg w-[24px] h-[24px] flex justify-center items-center">
                      {item.icon}
                    </span>
                    <span className="text-sm font-sans font-medium text-[##0F172A]">
                      {item.title}
                    </span>
                  </li>
                ))}
                <hr className="my-4" />
                <li
                  className={`flex gap-3 px-2 py-3 cursor-pointer hover:bg-[#F3F4F6]`}
                  onClick={logOutHandler}
                >
                  <span className="text-lg w-[24px] h-[24px] flex justify-center items-center">
                    {logout}
                  </span>
                  <span className="text-[#DC2626] text-sm font-sans font-medium">
                    Logout
                  </span>
                </li>
              </ul>
            </div>
          )}
          {children}

          {showUnsavedModal && (
            <div
              className="relative z-10"
              aria-labelledby="modal-title"
              role="dialog"
              aria-modal="true"
            >
              <div
                className="fixed inset-0 bg-gray-500/75 transition-opacity"
                aria-hidden="true"
              ></div>
              <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                  <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl p-6">
                    <div
                      className="flex justify-end cursor-pointer"
                      onClick={closeHandler}
                    >
                      {close}
                    </div>
                    <div className="bg-white">
                      <div className="sm:flex sm:items-start">
                        <div className="mt-3 text-center sm:mt-0 sm:text-left">
                          <p
                            className="text-lg font-sans font-medium text-[#0F172A]"
                            id="modal-title"
                          >
                            Unsaved changes
                          </p>
                          <div className="mt-4">
                            <div className="flex flex-col gap-4">
                              <p className="text-sm font-sans font-normal text-[#1E293B]">
                                You have unsaved changes. Do you want to save
                                before saving?
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="sm:flex sm:flex-row-reverse mt-6">
                      <button
                        onClick={saveHandler}
                        type="button"
                        className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
                      >
                        Save
                      </button>
                      <button
                        onClick={discardHandler}
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
                      >
                        Discard
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
