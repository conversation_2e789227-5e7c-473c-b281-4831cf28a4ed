'use client';
import { Button } from '@/components/Button';
import { homeDownChevron, homeUpChevron } from '@/utils/icon';
import Image from 'next/image';
import { useState } from 'react';

const steps = [
  {
    title: 'Build your next product listing on Amazon',
    description: 'What kind of product are you selling?',
    subDescription: 'Create powerful, high-converting listings with ease.',
    buttonText: 'Create now',
  },
  {
    title: 'Evaluate and improve listing performance',
  },
  {
    title: 'Update your profile information',
  },
  {
    title: 'Download product listing app',
  },
];

export default function Home() {
  const [expandedIndex, setExpandedIndex] = useState(0);
  const completedStep = 1;

  return (
    <div className="w-full h-screen flex items-center justify-center bg-gray-50 flex-col font-inter px-4">
      <div className="w-full max-w-2xl flex gap-6 flex-col">
        <h1 className="text-xl font-semibold mb-4 self-start">
          Hello Username
        </h1>
        <div className=" bg-white rounded-xl shadow p-4 flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium text-[#0F1824]">
              Welcome to Product Listing Management
            </p>
            <p className="text-xs text-[#4D555D]">
              Let&apos;s start your seller journey and grow Your Amazon Business
            </p>
            <div className="flex flex-row gap-6 items-center">
              <div className="w-[82%] bg-[#EEEFEF] h-2 rounded">
                <div
                  className="bg-[#FACC15] h-2 rounded"
                  style={{ width: `${(completedStep / steps.length) * 100}%` }}
                ></div>
              </div>
              <p className="text-xs text-[#4D555D]">
                Complete {completedStep} of {steps.length}
              </p>
            </div>
          </div>
          {steps.map((step, index) => (
            <div
              key={index}
              className={`${
                index !== steps.length - 1
                  ? 'border-b border-[#E8EAEB] pb-3'
                  : ''
              } flex flex-col justify-center`}
            >
              <button
                className="w-full   flex justify-between items-center"
                onClick={() =>
                  setExpandedIndex(index === expandedIndex ? -1 : index)
                }
              >
                <span className="font-medium text-sm text-[#0F1824]">
                  {step.title}
                </span>
                <span className="shrink-0">
                  {index === expandedIndex ? homeUpChevron : homeDownChevron}
                </span>
              </button>
              {index === expandedIndex && step.description && (
                <div className="relative bg-[#F9FAFB] rounded p-4 text-sm overflow-hidden">
                  <div className="flex flex-row gap-2 ">
                    <span className="w-4 h-4 rounded-full border-2 border-gray-400 border-t-transparent animate-spin"></span>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2 text-[#4D555D]">
                        <span>{step.description}</span>
                      </div>
                      {step.subDescription && (
                        <p className="text-[#4D555D] text-sm">
                          {step.subDescription}
                        </p>
                      )}
                      <Button className="!w-fit px-4 !py-2 !mt-0 !font-normal">
                        {step.buttonText}
                      </Button>
                    </div>
                  </div>
                  <Image
                    src="/assets/home/<USER>"
                    alt="Illustration"
                    width={178}
                    height={136}
                    className="absolute bottom-0 right-0"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
