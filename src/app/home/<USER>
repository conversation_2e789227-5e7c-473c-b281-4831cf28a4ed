import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // const router = useRouter();
  // useEffect(() => {
  //   const sessionToken = Cookies.get('session_token');
  //   if (!sessionToken) {
  //     router.push('/auth/login');
  //   }
  // }, [router]);
  return (
    <div className="min-h-screen text-gray-900 ">
      <div className="m-0 bg-gray-100 shadow sm:rounded-lg flex flex-col">
        <Header />
        <div className="flex h-screen">
          <Sidebar />
          {children}
        </div>
      </div>
    </div>
  );
}
