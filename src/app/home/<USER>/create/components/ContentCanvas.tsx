'use client';

import { useState } from 'react';
import CompanyLogoModule from './modules/CompanyLogoModule';

interface Module {
  id: string;
  type: string;
  data: any;
}

interface ContentBuilderCanvasProps {
  modules: Module[];
  setModules: (modules: Module[]) => void;
  contentData: any;
  setContentData: (data: any) => void;
}

function ContentBuilderCanvas({
  modules,
  setModules,
  contentData,
  setContentData,
}: ContentBuilderCanvasProps) {
  const [expandedModules, setExpandedModules] = useState<string[]>([]);

  const toggleModuleExpansion = (moduleId: string) => {
    setExpandedModules((prev) =>
      prev.includes(moduleId)
        ? prev.filter((id) => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const updateModuleData = (moduleId: string, data: any) => {
    setModules(
      modules.map((module) =>
        module.id === moduleId
          ? { ...module, data: { ...module.data, ...data } }
          : module
      )
    );
  };

  const removeModule = (moduleId: string) => {
    setModules(modules.filter((module) => module.id !== moduleId));
  };

  const moveModule = (dragIndex: number, hoverIndex: number) => {
    const draggedModule = modules[dragIndex];
    const newModules = [...modules];
    newModules.splice(dragIndex, 1);
    newModules.splice(hoverIndex, 0, draggedModule);
    setModules(newModules);
  };

  const renderModule = (module: Module, index: number) => {
    const isExpanded = expandedModules.includes(module.id);

    switch (module.type) {
      case 'company-logo':
        return (
          <CompanyLogoModule
            key={module.id}
            module={module}
            isExpanded={isExpanded}
            onToggleExpansion={() => toggleModuleExpansion(module.id)}
            onUpdateData={(data) => updateModuleData(module.id, data)}
            onRemove={() => removeModule(module.id)}
            onMove={moveModule}
            index={index}
          />
        );
      case 'comparison-chart':
        const ComparisonChartModuleComponent =
          require('./modules/ComparisonChartModule').default;
        return (
          <ComparisonChartModuleComponent
            key={module.id}
            module={module}
            isExpanded={isExpanded}
            onToggleExpansion={() => toggleModuleExpansion(module.id)}
            onUpdateData={(data) => updateModuleData(module.id, data)}
            onRemove={() => removeModule(module.id)}
            onMove={moveModule}
            index={index}
          />
        );
      case 'technical-specification':
        const TechnicalSpecificationModuleComponent =
          require('./modules/TechnicalSpecificationModule').default;
        return (
          <TechnicalSpecificationModuleComponent
            key={module.id}
            module={module}
            isExpanded={isExpanded}
            onToggleExpansion={() => toggleModuleExpansion(module.id)}
            onUpdateData={(data) => updateModuleData(module.id, data)}
            onRemove={() => removeModule(module.id)}
            onMove={moveModule}
            index={index}
          />
        );
      default:
        return (
          <div
            key={module.id}
            className="bg-white rounded-lg border border-gray-200 p-4 mb-4"
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-900">
                {module.type
                  .replace('-', ' ')
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
              </h3>
              <button
                onClick={() => removeModule(module.id)}
                className="text-red-400 hover:text-red-600"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </div>
            <div className="text-sm text-gray-500">
              Module configuration will be implemented here
            </div>
          </div>
        );
    }
  };
  return (
    <div className="flex-1 p-6">
      {modules.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Start building your A+ Content
          </h3>
          <p className="text-gray-500 max-w-md">
            Select modules from the sidebar to start creating your A+ content.
            You can drag and drop modules to rearrange them.
          </p>
        </div>
      ) : (
        <div className="max-w-4xl mx-auto">
          {modules.map((module, index) => renderModule(module, index))}
        </div>
      )}
    </div>
  );
}

export default ContentBuilderCanvas;
