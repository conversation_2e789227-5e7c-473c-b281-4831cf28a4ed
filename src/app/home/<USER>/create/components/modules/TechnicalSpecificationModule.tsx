'use client';

import { useAppDispatch } from '@/app/hook';
import { updateModule } from '@/app/store/aplus-content';
import Input from '@/components/Input';
import { collapse, deleteIc, expand } from '@/utils/icon';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';

interface TechnicalSpecificationModuleProps {
  module: {
    id: string;
    type: string;
    data?: any;
  };
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onUpdateData: (data: any) => void;
  onRemove: () => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  index: number;
}

export default function TechnicalSpecificationModule({
  module,
  isExpanded,
  onToggleExpansion,
  onUpdateData,
  onRemove,
  onMove,
  index,
}: TechnicalSpecificationModuleProps) {
  const dispatch = useAppDispatch();

  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'module',
    item: { index, type: 'module' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'module',
    hover: (item: { index: number; type: string }) => {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      onMove(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  const handleTextChange = (field: string, value: string) => {
    const newData = { [field]: value };
    onUpdateData(newData);
    dispatch(updateModule({ id: module.id, data: newData }));
  };

  const addSpecification = () => {
    const specifications = module.data?.specifications || [];
    const newSpec = {
      id: Date.now().toString(),
      specification: '',
      definition: ''
    };
    onUpdateData({ specifications: [...specifications, newSpec] });
  };

  const updateSpecification = (specIndex: number, field: string, value: string) => {
    const specifications = module.data?.specifications || [];
    const updatedSpecs = [...specifications];
    updatedSpecs[specIndex] = {
      ...updatedSpecs[specIndex],
      [field]: value
    };
    onUpdateData({ specifications: updatedSpecs });
  };

  const removeSpecification = (specIndex: number) => {
    const specifications = module.data?.specifications || [];
    const updatedSpecs = specifications.filter((_: any, index: number) => index !== specIndex);
    onUpdateData({ specifications: updatedSpecs });
  };

  const specifications = module.data?.specifications || [
    { id: '1', specification: '', definition: '' },
    { id: '2', specification: '', definition: '' },
    { id: '3', specification: '', definition: '' },
    { id: '4', specification: '', definition: '' }
  ];

  return (
    <div
      ref={ref}
      className={`bg-white rounded-lg border border-gray-200 mb-4 ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      {/* Module Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
            <span className="text-green-600 text-sm font-medium">TS</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Technical specification</h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onToggleExpansion}
            className="text-gray-400 hover:text-gray-600"
          >
            {isExpanded ? expand : collapse}
          </button>
          <button onClick={onRemove} className="text-red-400 hover:text-red-600">
            {deleteIc}
          </button>
        </div>
      </div>

      {/* Module Content */}
      {isExpanded && (
        <div className="p-6 space-y-6 bg-gray-50">
          {/* Headline Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-gray-900 mb-1">Headline</h3>
            </div>
            <Input
              value={module.data?.headline || ''}
              onChangeFunc={(e) => handleTextChange('headline', e.target.value)}
              placeholder="Enter headline"
              className="w-full"
            />
          </div>

          {/* Specification Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-1">
                  Specification (16 max / 4 min)
                </h3>
              </div>
              <div className="flex items-center gap-2">
                {/* List View Button */}
                <button className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
                {/* Grid View Button */}
                <button className="p-2 bg-gray-200 text-gray-600 rounded hover:bg-gray-300">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2h4a1 1 0 000-2H3zM3 7a1 1 0 000 2h4a1 1 0 000-2H3zM3 11a1 1 0 100 2h4a1 1 0 000-2H3zM11 3a1 1 0 000 2h4a1 1 0 000-2h-4zM11 7a1 1 0 000 2h4a1 1 0 000-2h-4zM11 11a1 1 0 100 2h4a1 1 0 000-2h-4z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Specifications List */}
            <div className="space-y-4">
              {specifications.map((spec: any, specIndex: number) => (
                <div key={spec.id || specIndex} className="flex items-start gap-4">
                  {/* Drag Handle */}
                  <div className="flex items-center justify-center w-6 h-10 text-gray-400 cursor-move">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM6 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7zm0 4a1 1 0 100 2h6a1 1 0 100-2H7z" />
                    </svg>
                  </div>

                  {/* Specification Input */}
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Specification <span className="text-red-500">*</span>
                    </label>
                    <Input
                      value={spec.specification || ''}
                      onChangeFunc={(e) => updateSpecification(specIndex, 'specification', e.target.value)}
                      placeholder="Enter specification"
                      className="w-full"
                    />
                  </div>

                  {/* Definition Input */}
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Definition <span className="text-red-500">*</span>
                    </label>
                    <Input
                      value={spec.definition || ''}
                      onChangeFunc={(e) => updateSpecification(specIndex, 'definition', e.target.value)}
                      placeholder="Enter definition"
                      className="w-full"
                    />
                  </div>

                  {/* Delete Button */}
                  <div className="flex items-end h-10">
                    <button
                      onClick={() => removeSpecification(specIndex)}
                      className="p-2 text-gray-400 hover:text-red-600"
                      disabled={specifications.length <= 4}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}

              {/* Add More Button */}
              <button
                onClick={addSpecification}
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                disabled={specifications.length >= 16}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add more
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
