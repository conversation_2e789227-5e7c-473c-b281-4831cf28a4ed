'use client';

import { useAppDispatch } from '@/app/hook';
import { updateModule } from '@/app/store/aplus-content';
import Input from '@/components/Input';
import TextArea from '@/components/TextArea';
import { collapse, deleteIc, expand, imageUpload } from '@/utils/icon';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';

interface ComparisonChartModuleProps {
  module: {
    id: string;
    type: string;
    data?: any;
  };
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onUpdateData: (data: any) => void;
  onRemove: () => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  index: number;
}

export default function ComparisonChartModule({
  module,
  isExpanded,
  onToggleExpansion,
  onUpdateData,
  onRemove,
  onMove,
  index,
}: ComparisonChartModuleProps) {
  const dispatch = useAppDispatch();
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const multipleImageInputRefs = useRef<{
    [key: string]: HTMLInputElement | null;
  }>({});

  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'module',
    item: { index, type: 'module' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'module',
    hover: (item: { index: number; type: string }) => {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      onMove(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  const handleImageUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    imageType: string,
    productIndex?: number
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      if (productIndex !== undefined) {
        // Handle product images
        const products = module.data?.products || [];
        const updatedProducts = [...products];
        if (!updatedProducts[productIndex]) {
          updatedProducts[productIndex] = {};
        }
        updatedProducts[productIndex] = {
          ...updatedProducts[productIndex],
          [imageType]: { file, url, name: file.name },
        };
        onUpdateData({ products: updatedProducts });
      } else {
        // Handle single images
        onUpdateData({
          [imageType]: { file, url, name: file.name },
        });
      }
    }
  };

  const handleTextChange = (
    field: string,
    value: string,
    productIndex?: number
  ) => {
    if (productIndex !== undefined) {
      // Handle product text fields
      const products = module.data?.products || [];
      const updatedProducts = [...products];
      if (!updatedProducts[productIndex]) {
        updatedProducts[productIndex] = {};
      }
      updatedProducts[productIndex] = {
        ...updatedProducts[productIndex],
        [field]: value,
      };
      onUpdateData({ products: updatedProducts });
    } else {
      // Handle regular text fields
      const newData = { [field]: value };
      onUpdateData(newData);
      dispatch(updateModule({ id: module.id, data: newData }));
    }
  };

  const removeImage = (imageType: string, productIndex?: number) => {
    if (productIndex !== undefined) {
      const products = module.data?.products || [];
      const updatedProducts = [...products];
      if (updatedProducts[productIndex]) {
        updatedProducts[productIndex] = {
          ...updatedProducts[productIndex],
          [imageType]: null,
        };
        onUpdateData({ products: updatedProducts });
      }
    } else {
      onUpdateData({ [imageType]: null });
    }
  };

  const addMetric = () => {
    const metrics = module.data?.metrics || [];
    const newMetric = {
      id: Date.now().toString(),
      name: '',
      values: Array(6).fill(''),
    };
    onUpdateData({ metrics: [...metrics, newMetric] });
  };

  const updateMetric = (
    metricIndex: number,
    field: string,
    value: string,
    valueIndex?: number
  ) => {
    const metrics = module.data?.metrics || [];
    const updatedMetrics = [...metrics];
    if (valueIndex !== undefined) {
      updatedMetrics[metricIndex].values[valueIndex] = value;
    } else {
      updatedMetrics[metricIndex][field] = value;
    }
    onUpdateData({ metrics: updatedMetrics });
  };

  const removeMetric = (metricIndex: number) => {
    const metrics = module.data?.metrics || [];
    const updatedMetrics = metrics.filter(
      (_: any, index: number) => index !== metricIndex
    );
    onUpdateData({ metrics: updatedMetrics });
  };

  const toggleHighlight = (productIndex: number) => {
    const products = module.data?.products || [];
    const updatedProducts = [...products];
    if (!updatedProducts[productIndex]) {
      updatedProducts[productIndex] = {};
    }
    updatedProducts[productIndex] = {
      ...updatedProducts[productIndex],
      highlighted: !updatedProducts[productIndex]?.highlighted,
    };
    onUpdateData({ products: updatedProducts });
  };

  const products = module.data?.products || Array(6).fill({});
  const metrics = module.data?.metrics || [];
  const multipleImages = module.data?.multipleImages || Array(4).fill({});

  return (
    <div
      ref={ref}
      className={`bg-white rounded-lg border border-gray-200 mb-4 ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      {/* Module Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
            <span className="text-blue-600 text-sm font-medium">CC</span>
          </div>
          <h3 className="text-sm font-medium text-gray-900">
            Comparison Chart
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onToggleExpansion}
            className="text-gray-400 hover:text-gray-600"
          >
            {isExpanded ? expand : collapse}
          </button>
          <button
            onClick={onRemove}
            className="text-red-400 hover:text-red-600"
          >
            {deleteIc}
          </button>
        </div>
      </div>

      {/* Module Content */}
      {isExpanded && (
        <div className="p-6 space-y-6 bg-gray-50">
          {/* Comparison Products Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                Comparison products
              </h3>
              <p className="text-xs text-gray-500">6 max / 2 min</p>
              <div className="flex items-center gap-4 mt-3">
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm text-gray-700">Show reviews</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm text-gray-700">Show prices</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" className="rounded" defaultChecked />
                  <span className="text-sm text-gray-700">
                    Show Add to cart button
                  </span>
                </label>
              </div>
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-6 gap-4">
              {products.map((product: any, index: number) => (
                <div key={index} className="space-y-3">
                  {/* ASIN Input */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      ASIN {index < 2 ? '*' : ''}
                    </label>
                    <Input
                      value={product?.asin || ''}
                      onChangeFunc={(e) =>
                        handleTextChange('asin', e.target.value, index)
                      }
                      placeholder="Enter ASIN"
                      className="text-xs"
                    />
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Image {index < 2 ? '*' : ''}
                    </label>
                    <div className="border-2 border-dashed border-orange-300 rounded bg-yellow-50 p-4 min-h-[120px] flex items-center justify-center">
                      {product?.image ? (
                        <div className="relative w-full h-full">
                          <img
                            src={product.image.url}
                            alt={`Product ${index + 1}`}
                            className="w-full h-full object-cover rounded"
                          />
                          <button
                            onClick={() => removeImage('image', index)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                          >
                            <svg
                              className="w-3 h-3"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                      ) : (
                        <div className="text-center">
                          <div className="mx-auto w-8 h-8 text-gray-400 mb-2">
                            {imageUpload}
                          </div>
                          <p className="text-xs text-gray-600 mb-1">
                            Add an image
                          </p>
                          <p className="text-xs text-gray-500 mb-2">150×300</p>
                          <button
                            onClick={() =>
                              fileInputRefs.current[`product-${index}`]?.click()
                            }
                            className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                          >
                            Browse files
                          </button>
                        </div>
                      )}
                    </div>
                    <input
                      ref={(el) =>
                        (fileInputRefs.current[`product-${index}`] = el)
                      }
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload(e, 'image', index)}
                      className="hidden"
                    />
                  </div>

                  {/* Title Input */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Title {index < 2 ? '*' : ''}
                    </label>
                    <Input
                      value={product?.title || ''}
                      onChangeFunc={(e) =>
                        handleTextChange('title', e.target.value, index)
                      }
                      placeholder="Enter title"
                      className="text-xs"
                    />
                  </div>

                  {/* Highlight Toggle */}
                  <div className="flex items-center gap-2">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={product?.highlighted || false}
                        onChange={() => toggleHighlight(index)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                    <span className="text-xs text-gray-700">
                      Highlight column
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Comparison Metrics Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-900 mb-1">
                Comparison metrics
              </h3>
              <p className="text-xs text-gray-500">10 max / 1 min</p>
            </div>

            <div className="space-y-3">
              {metrics.map((metric: any, metricIndex: number) => (
                <div
                  key={metric.id}
                  className="grid grid-cols-7 gap-3 items-center"
                >
                  <div>
                    <Input
                      value={metric.name || ''}
                      onChangeFunc={(e) =>
                        updateMetric(metricIndex, 'name', e.target.value)
                      }
                      placeholder="Metric name"
                      className="text-xs"
                    />
                  </div>
                  {Array.from({ length: 6 }).map((_, valueIndex) => (
                    <div key={valueIndex}>
                      <Input
                        value={metric.values?.[valueIndex] || ''}
                        onChangeFunc={(e) =>
                          updateMetric(
                            metricIndex,
                            'values',
                            e.target.value,
                            valueIndex
                          )
                        }
                        placeholder="Value"
                        className="text-xs"
                      />
                    </div>
                  ))}
                  <button
                    onClick={() => removeMetric(metricIndex)}
                    className="text-red-400 hover:text-red-600 p-1"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>
              ))}

              <button
                onClick={addMetric}
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Add metric
              </button>
            </div>
          </div>

          {/* Multiple Image Module Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-900 mb-1">
                Multiple image module A
              </h3>
            </div>

            <div className="flex gap-6">
              {/* Main Image */}
              <div className="w-64">
                <div className="mb-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Image <span className="text-red-500">*</span>
                  </label>
                </div>
                <div className="border-2 border-dashed border-orange-300 rounded-lg bg-yellow-50 p-6 min-h-[200px] flex items-center justify-center">
                  {module.data?.mainMultipleImage ? (
                    <div className="relative w-full h-full">
                      <img
                        src={module.data.mainMultipleImage.url}
                        alt="Main Multiple Image"
                        className="w-full h-full object-cover rounded"
                      />
                      <button
                        onClick={() => removeImage('mainMultipleImage')}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                        {imageUpload}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">Add an image</p>
                      <p className="text-xs text-gray-500 mb-4">300×300</p>
                      <button
                        onClick={() =>
                          fileInputRefs.current['mainMultiple']?.click()
                        }
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Browse files
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={(el) => (fileInputRefs.current['mainMultiple'] = el)}
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, 'mainMultipleImage')}
                  className="hidden"
                />
              </div>

              {/* Text Fields */}
              <div className="flex-1 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Headline
                  </label>
                  <Input
                    value={module.data?.multipleHeadline || ''}
                    onChangeFunc={(e) =>
                      handleTextChange('multipleHeadline', e.target.value)
                    }
                    placeholder="Enter headline"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                    <span className="text-purple-600 ml-2 text-xs">
                      ✨ AI Generate
                    </span>
                  </label>
                  <TextArea
                    value={module.data?.multipleDescription || ''}
                    onChange={(e) =>
                      handleTextChange('multipleDescription', e.target.value)
                    }
                    placeholder="Enter body text"
                    className="min-h-[120px]"
                  />
                </div>
              </div>
            </div>

            {/* Four Images Grid */}
            <div className="mt-6">
              <div className="grid grid-cols-4 gap-4">
                {multipleImages.map((image: any, index: number) => (
                  <div key={index} className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Image
                      </label>
                      <div className="border-2 border-dashed border-orange-300 rounded bg-yellow-50 p-4 min-h-[100px] flex items-center justify-center">
                        {image?.url ? (
                          <div className="relative w-full h-full">
                            <img
                              src={image.url}
                              alt={`Multiple Image ${index + 1}`}
                              className="w-full h-full object-cover rounded"
                            />
                            <button
                              onClick={() => {
                                const updatedImages = [...multipleImages];
                                updatedImages[index] = {};
                                onUpdateData({ multipleImages: updatedImages });
                              }}
                              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <svg
                                className="w-3 h-3"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        ) : (
                          <div className="text-center">
                            <div className="mx-auto w-6 h-6 text-gray-400 mb-2">
                              {imageUpload}
                            </div>
                            <p className="text-xs text-gray-600 mb-1">
                              Add an image
                            </p>
                            <button
                              onClick={() =>
                                multipleImageInputRefs.current[
                                  `multiple-${index}`
                                ]?.click()
                              }
                              className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                            >
                              Browse files
                            </button>
                          </div>
                        )}
                      </div>
                      <input
                        ref={(el) =>
                          (multipleImageInputRefs.current[`multiple-${index}`] =
                            el)
                        }
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const url = URL.createObjectURL(file);
                            const updatedImages = [...multipleImages];
                            updatedImages[index] = {
                              file,
                              url,
                              name: file.name,
                            };
                            onUpdateData({ multipleImages: updatedImages });
                          }
                        }}
                        className="hidden"
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-purple-600 text-xs">
                        ✨ AI Generate
                      </span>
                    </div>

                    <div>
                      <Input
                        value={image?.caption || ''}
                        onChangeFunc={(e) => {
                          const updatedImages = [...multipleImages];
                          updatedImages[index] = {
                            ...updatedImages[index],
                            caption: e.target.value,
                          };
                          onUpdateData({ multipleImages: updatedImages });
                        }}
                        placeholder="Enter caption"
                        className="text-xs"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
