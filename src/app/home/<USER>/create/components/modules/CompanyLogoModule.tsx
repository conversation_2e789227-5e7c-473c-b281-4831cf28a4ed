'use client';

import { useAppDispatch } from '@/app/hook';
import { updateModule } from '@/app/store/aplus-content';
import Input from '@/components/Input';
import TextArea from '@/components/TextArea';
import { collapse, deleteIc, expand, imageUpload } from '@/utils/icon';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';

interface CompanyLogoModuleProps {
  module: any;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onUpdateData: (data: any) => void;
  onRemove: () => void;
  onMove: (fromIndex: number, toIndex: number) => void;
  index: number;
}

export default function CompanyLogoModule({
  module,
  isExpanded,
  onToggleExpansion,
  onUpdateData,
  onRemove,
  onMove,
  index,
}: CompanyLogoModuleProps) {
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const backgroundFileInputRef = useRef<HTMLInputElement>(null);
  const singleFileInputRef = useRef<HTMLInputElement>(null);

  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'module',
    item: { index, type: 'module' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'module',
    hover: (item: any) => {
      if (item.index !== index) {
        onMove(item.index, index);
        item.index = index;
      }
    },
  });

  drag(drop(ref));

  const handleImageUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    imageType: 'main' | 'background' | 'single'
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      const imageKey =
        imageType === 'main'
          ? 'mainImage'
          : imageType === 'background'
            ? 'backgroundImage'
            : 'singleImage';
      onUpdateData({
        [imageKey]: {
          file,
          url,
          name: file.name,
        },
      });
    }
  };

  const handleTextChange = (field: string, value: string) => {
    const newData = { [field]: value };
    onUpdateData(newData);
    // Also update Redux store
    dispatch(updateModule({ id: module.id, data: newData }));
  };

  const removeImage = (imageType: 'main' | 'background' | 'single') => {
    const imageKey =
      imageType === 'main'
        ? 'mainImage'
        : imageType === 'background'
          ? 'backgroundImage'
          : 'singleImage';
    onUpdateData({
      [imageKey]: null,
    });
  };

  return (
    <div
      ref={ref}
      className={`bg-white rounded-lg border border-gray-200 mb-4 ${isDragging ? 'opacity-50' : ''}`}
    >
      {/* Module Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="cursor-move text-gray-400 hover:text-gray-600">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </div>
          <h3 className="text-sm font-medium text-gray-900">Company Logo</h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onToggleExpansion}
            className="text-gray-400 hover:text-gray-600"
          >
            {isExpanded ? collapse : expand}
          </button>
          <button
            onClick={onRemove}
            className="text-gray-400 hover:text-red-600"
          >
            {deleteIc('#6B7280')}
          </button>
        </div>
      </div>

      {/* Module Content */}
      <div className="p-6 space-y-6 bg-gray-50">
        {/* Main Image Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-900 mb-1">Image</h3>
          </div>
          <div className="border-2 border-dashed border-orange-300 rounded-lg bg-yellow-50 p-8 min-h-[200px] flex items-center justify-center">
            {module.data?.mainImage ? (
              <div className="relative w-full">
                <img
                  src={module.data.mainImage.url}
                  alt="Company Logo"
                  className="max-w-full h-auto mx-auto rounded max-h-[150px] object-contain"
                />
                <button
                  onClick={() => removeImage('main')}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ) : (
              <div className="text-center">
                <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                  {imageUpload}
                </div>
                <p className="text-sm text-gray-600 mb-2">Add an Image</p>
                <p className="text-xs text-gray-500 mb-4">600x180</p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  Browse files
                </button>
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Image & dark text overlay
          </p>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleImageUpload(e, 'main')}
            className="hidden"
          />
        </div>

        {/* Background Image Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              Background Image <span className="text-red-500">*</span>
            </h3>
          </div>
          <div className="flex gap-4">
            {/* Background Image Upload */}
            <div className="flex-1">
              <div className="border-2 border-dashed border-orange-300 rounded-lg bg-yellow-50 p-6 min-h-[200px] flex items-center justify-center relative">
                {module.data?.backgroundImage ? (
                  <div className="relative w-full h-full">
                    <img
                      src={module.data.backgroundImage.url}
                      alt="Background"
                      className="w-full h-full object-cover rounded"
                    />
                    <button
                      onClick={() => removeImage('background')}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                      {imageUpload}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Add an Image</p>
                    <p className="text-xs text-gray-500 mb-4">600x180</p>
                    <button
                      onClick={() => backgroundFileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Browse files
                    </button>
                  </div>
                )}
              </div>
              <input
                ref={backgroundFileInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => handleImageUpload(e, 'background')}
                className="hidden"
              />
            </div>

            {/* Overlay Text Inputs */}
            <div className="w-80 bg-gray-800 rounded-lg p-4">
              <div className="mb-4">
                <label className="block text-sm font-medium text-white mb-2">
                  Overlay headline
                </label>
                <input
                  type="text"
                  placeholder="Enter headline"
                  value={module.data?.overlayHeadline || ''}
                  onChange={(e) =>
                    handleTextChange('overlayHeadline', e.target.value)
                  }
                  className="w-full px-3 py-2 bg-white rounded border border-gray-300 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Overlay body text
                  <span className="text-purple-400 ml-2 text-xs">
                    ✨ AI Generate
                  </span>
                </label>
                <textarea
                  placeholder="Enter body text"
                  value={module.data?.overlayBodyText || ''}
                  onChange={(e) =>
                    handleTextChange('overlayBodyText', e.target.value)
                  }
                  className="w-full px-3 py-2 bg-white rounded border border-gray-300 text-sm resize-none"
                  rows={4}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Text Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-900 mb-1">Text</h3>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Headline
              </label>
              <Input
                value={module.data?.headline || ''}
                onChangeFunc={(e) =>
                  handleTextChange('headline', e.target.value)
                }
                placeholder="Enter headline"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Body text <span className="text-red-500">*</span>
                <span className="text-purple-600 ml-2 text-xs">
                  ✨ AI Generate
                </span>
              </label>
              <TextArea
                value={module.data?.bodyText || ''}
                onChange={(e) => handleTextChange('bodyText', e.target.value)}
                placeholder="Enter body text"
                maxLength={1000}
                className="min-h-[120px]"
              />
            </div>
          </div>
        </div>

        {/* Single left image Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              Single left image
            </h3>
          </div>
          <div className="flex gap-4">
            {/* Image Upload */}
            <div className="w-64">
              <div className="mb-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image <span className="text-red-500">*</span>
                </label>
              </div>
              <div className="border-2 border-dashed border-orange-300 rounded-lg bg-yellow-50 p-6 min-h-[200px] flex items-center justify-center">
                {module.data?.singleImage ? (
                  <div className="relative w-full h-full">
                    <img
                      src={module.data.singleImage.url}
                      alt="Single Image"
                      className="w-full h-full object-cover rounded"
                    />
                    <button
                      onClick={() => removeImage('single')}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                      {imageUpload}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Add an image</p>
                    <p className="text-xs text-gray-500 mb-4">300x300</p>
                    <button
                      onClick={() => singleFileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Browse files
                    </button>
                  </div>
                )}
              </div>
              <input
                ref={singleFileInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => handleImageUpload(e, 'single')}
                className="hidden"
              />
            </div>

            {/* Text Fields */}
            <div className="flex-1 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Headline
                </label>
                <Input
                  value={module.data?.singleHeadline || ''}
                  onChangeFunc={(e) =>
                    handleTextChange('singleHeadline', e.target.value)
                  }
                  placeholder="Enter headline"
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Body text <span className="text-red-500">*</span>
                  <span className="text-purple-600 ml-2 text-xs">
                    ✨ AI Generate
                  </span>
                </label>
                <TextArea
                  value={module.data?.singleBodyText || ''}
                  onChange={(e) =>
                    handleTextChange('singleBodyText', e.target.value)
                  }
                  placeholder="Enter body text"
                  maxLength={1000}
                  className="min-h-[120px]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
