'use client';

import Image from 'next/image';
import { useState } from 'react';

interface ModuleTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  category: 'modules' | 'templates';
}

const moduleTemplates: ModuleTemplate[] = [
  {
    id: 'company-logo',
    name: 'Company Logo',
    description: 'Add your company logo with text overlay',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'comparison-chart',
    name: 'Comparison Chart',
    description: 'Compare products with metrics and images',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'technical-specification',
    name: 'Technical Specification',
    description: 'Display product specifications and definitions',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'four-image-text',
    name: 'Four Image & text',
    description: 'Display four images with descriptive text',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'four-image-text-quadrant',
    name: 'Four Image/text quadrant',
    description: 'Four images arranged in quadrant layout',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'image-dark-text-overlay',
    name: 'Image & dark text overlay',
    description: 'Image with dark text overlay',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'image-light-text-overlay',
    name: 'Image & light text overlay',
    description: 'Image with light text overlay',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'image-header-text',
    name: 'Image header with text',
    description: 'Large header image with text content',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'multiple-image-module-a',
    name: 'Multiple Image Module A',
    description: 'Multiple images in layout A',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'product-description-text',
    name: 'Product description text',
    description: 'Rich text product description',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
  {
    id: 'single-image-highlights',
    name: 'Single image & highlights',
    description: 'Single image with feature highlights',
    thumbnail: '/api/placeholder/120/80',
    category: 'modules',
  },
];

interface ModuleTemplatesSidebarProps {
  onAddModule: (moduleType: string) => void;
}

export default function ModuleTemplatesSidebar({
  onAddModule,
}: ModuleTemplatesSidebarProps) {
  const [activeTab, setActiveTab] = useState<'modules' | 'templates'>(
    'modules'
  );
  const [searchQuery, setSearchQuery] = useState('');

  const filteredTemplates = moduleTemplates.filter(
    (template) =>
      template.category === activeTab &&
      template.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('modules')}
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'modules'
              ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Modules
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'templates'
              ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Templates
        </button>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search module"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Module Templates */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {filteredTemplates.map((template) => (
          <div
            key={template.id}
            className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:shadow-sm cursor-pointer transition-all"
            onClick={() => onAddModule(template.id)}
          >
            <div className="flex flex-col items-center text-center">
              <div className="w-full h-20 bg-gray-100 rounded-lg mb-2 flex items-center justify-center overflow-hidden">
                <Image
                  src={template.thumbnail}
                  alt={template.name}
                  width={120}
                  height={80}
                  className="object-cover"
                />
              </div>
              <h3 className="text-sm font-medium text-gray-900 mb-1">
                {template.name}
              </h3>
              <p className="text-xs text-gray-500 leading-tight">
                {template.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
