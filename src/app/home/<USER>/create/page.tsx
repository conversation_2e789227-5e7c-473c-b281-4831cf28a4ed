'use client';

import { useAppDispatch, useAppSelector } from '@/app/hook';
import { setContentName, setModules } from '@/app/store/aplus-content';
import { back } from '@/utils/icon';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import ContentBuilderCanvas from './components/ContentCanvas';
import ModuleTemplatesSidebar from './components/ModuleTemplatesSidebar';

export default function CreateAPlusContentPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { currentContent } = useAppSelector((state) => state.aplusContent);

  // Keep local state for modules to handle drag and drop
  const [selectedModules, setSelectedModules] = useState<any[]>(
    currentContent.modules
  );

  const goBack = () => {
    router.back();
  };

  const handlePreview = () => {
    // Open preview in new tab/modal
    const previewData = {
      ...currentContent,
      modules: selectedModules,
    };
    console.log('Preview content:', previewData);
    // TODO: Implement preview modal or new tab
    alert('Preview functionality will open in a new window');
  };

  const handleSaveAsDraft = async () => {
    try {
      const draftData = {
        ...currentContent,
        modules: selectedModules,
        status: 'draft',
        lastModified: new Date().toISOString(),
      };
      console.log('Saving draft:', draftData);
      // TODO: Implement API call to save draft
      alert('Content saved as draft successfully!');
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Error saving draft. Please try again.');
    }
  };

  const handleNext = () => {
    // Validate required fields before proceeding
    const hasRequiredContent = selectedModules.some((module) => {
      if (module.type === 'company-logo') {
        return module.data?.bodyText && module.data?.backgroundImage;
      }
      return true;
    });

    if (!hasRequiredContent) {
      alert('Please fill in all required fields before proceeding.');
      return;
    }

    // Navigate to next step (e.g., ASIN assignment)
    console.log('Proceeding to next step:', currentContent);
    router.push('/home/<USER>/create/assign-asins');
  };

  const handleAddModule = (moduleType: string) => {
    const newModule = {
      id: Date.now().toString(),
      type: moduleType,
      data: {},
    };
    const updatedModules = [...selectedModules, newModule];
    setSelectedModules(updatedModules);
    // Also update Redux store
    dispatch(setModules(updatedModules));
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="mt-20 min-h-screen bg-gray-50 w-full">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={goBack}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
              >
                {back}
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {currentContent.name}
              </h1>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={handlePreview}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
              >
                Preview
              </button>
              <button
                onClick={handleSaveAsDraft}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
              >
                Save as draft
              </button>
              <button
                onClick={handleNext}
                className="px-4 py-2 bg-yellow-400 text-gray-900 rounded-lg hover:bg-yellow-500 font-medium"
              >
                Next
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex h-[calc(100vh-80px)]">
          {/* Left Sidebar - Module Templates */}
          <ModuleTemplatesSidebar onAddModule={handleAddModule} />

          {/* Main Content Area */}
          <ContentBuilderCanvas
            modules={selectedModules}
            setModules={(modules) => {
              setSelectedModules(modules);
              dispatch(setModules(modules));
            }}
            contentData={currentContent}
            setContentData={(data) => {
              // Update Redux store with content data changes
              Object.keys(data).forEach((key) => {
                if (key === 'name') {
                  dispatch(setContentName(data[key]));
                }
              });
            }}
          />
        </div>
      </div>
    </DndProvider>
  );
}
