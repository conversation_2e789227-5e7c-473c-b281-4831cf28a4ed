'use client';
import { useAppSelector } from '@/app/hook';
import { Button } from '@/components/Button';
import { getListingBuilderData, postListing } from '@/utils/api';
import {
  back,
  checkCircle,
  circleEmpty,
  collapse,
  expand,
  listingSectionLocked,
  savedIcon,
  tipAndTrick,
} from '@/utils/icon';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import BasicInformationForm from './components/basic-information/BasicInformationForm';
import ComplianceSafetyForm from './components/compliance-and-safety/ComplianceSafetyForm';
import ListingContentForm from './components/listing-content/ListingContentForm';
import ListingQuantity from './components/ListingQuantity';
import OfferForm from './components/offer/OfferForm';
import ProductDetailForm from './components/product-detail/ProductDetailForm';
import TableOfContent from './components/TableOfContent';
import TipsAndTricks from './components/TipsAndTricks';

const CreateListing = () => {
  const router = useRouter();
  const [activeSection, setActiveSection] = useState(1);
  const [isSaved, setIsSaved] = useState(false);
  const [unlockedSections, setUnlockedSections] = useState<number[]>([1]);
  const [collapsedSections, setCollapsedSections] = useState<number[]>([]);
  const [tipAndTrickContent, setTipAndTrickContent] = useState({
    title: 'Select an input field',
    content: 'Tips and trick',
  });
  const [showTipAndTrick, setShowTipAndTrick] = useState(true);
  const [basicInformationAPI, setBasicInformationAPI] = useState({} as any);
  const [productDetailAPI, setProductDetailAPI] = useState({} as any);
  const [offerAPI, setOfferAPI] = useState({} as any);
  const [complianceSafetyAPI, setComplianceSafetyAPI] = useState({} as any);

  const goBack = () => {
    router.back();
  };

  const hatForm = useAppSelector((state) => state.productListing.hatForm);

  const handleSave = async () => {
    setIsSaved(true);
    const data = await postListing({
      product_type: 'HAT',
      payload: { ...hatForm },
      sku: 'SC-3-CS-2L-WAC-1',
    });
    // Save logic here
  };

  const handlePreview = () => {
    // Preview logic here
  };

  const handleCustomize = () => {
    // Customize logic here
  };

  const submitHandler = () => {
    if (activeSection === 1) {
      setUnlockedSections([1, 2, 3, 4, 5]);
      setActiveSection(2);
      setCollapsedSections([1, 3, 4, 5]); // Only section 2 is open
    } else {
      setActiveSection(activeSection + 1);
      setUnlockedSections((prev) => [...prev, activeSection + 1]);
      setCollapsedSections((prev) =>
        prev.includes(activeSection)
          ? prev.filter((id) => id !== activeSection)
          : [...prev, activeSection]
      );
    }
  };

  useEffect(() => {
    if (activeSection === 3) {
      setShowTipAndTrick(false);
    }
  }, [activeSection]);

  useEffect(() => {
    async function fetchListingBuilderData() {
      const res = await getListingBuilderData('beanie hat');
      setBasicInformationAPI(res.data.basic_information.properties);
      setProductDetailAPI({
        ...res.data.product_details.properties,
        ...res.data.shipping.properties,
        ...res.data.variations.properties,
      });
      setOfferAPI(res.data.offer.properties);
      setComplianceSafetyAPI(res.data.safety_and_compliance.properties);
    }
    fetchListingBuilderData();
  }, []);

  return (
    <main
      className="mt-20 px-6 w-full flex flex-col gap-4 overflow-scroll"
      aria-label="Create Listing Main Content"
    >
      {/* Header */}
      <header
        className="flex justify-between items-center"
        aria-label="Page Header"
      >
        <div className="flex items-center gap-2">
          <button
            onClick={goBack}
            className="text-gray-700"
            aria-label="Go Back"
          >
            {back}
          </button>
          <h1 className="text-xl font-semibold">Create new listing</h1>
        </div>
        <div className="flex items-center gap-2">
          {isSaved && (
            <div className="flex items-center text-gray-600 mr-2">
              {savedIcon}
              <span className="ml-1">Saved</span>
            </div>
          )}
          <Button
            className="px-4 py-2 text-sm bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 !mt-0"
            onClickFunc={handleCustomize}
          >
            Customize
          </Button>
          <Button
            className="px-4 py-2 text-sm bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 !mt-0"
            onClickFunc={handlePreview}
          >
            Preview
          </Button>
          <div className="relative">
            <Button
              className="px-4 py-2 text-sm bg-yellow-400 hover:bg-yellow-500 text-gray-900 whitespace-nowrap !mt-0"
              onClickFunc={handleSave}
            >
              Save as <span className="ml-1">▼</span>
            </Button>
          </div>
        </div>
      </header>

      <div className="flex gap-4">
        {/* Left sidebar */}
        <aside className="w-72" aria-label="Table of Content Sidebar">
          <TableOfContent
            activeSection={activeSection}
            setActiveSection={setActiveSection}
            setUnlockedSections={setUnlockedSections}
            unlockedSections={unlockedSections}
            setCollapsedSections={setCollapsedSections}
          />

          {/* <Attributes /> */}
        </aside>

        {/* Main form */}
        <section className="flex-1" aria-label="Create Listing Form">
          {/* Locked Sections */}
          {[
            {
              id: 1,
              title: 'Basic Information',
              content: (
                <BasicInformationForm
                  setTipAndTrickContent={setTipAndTrickContent}
                  basicInformationAPI={basicInformationAPI}
                  submitHandlerProps={submitHandler}
                  showActionButtons={activeSection === 1}
                />
              ),
            },
            {
              id: 2,
              title: 'Listing Content',
              content: (
                <ListingContentForm
                  setTipAndTrickContent={setTipAndTrickContent}
                />
              ),
            },
            {
              id: 3,
              title: 'Product Detail',
              content: (
                <ProductDetailForm productDetailAPI={productDetailAPI} />
              ),
            },
            {
              id: 4,
              title: 'Offer',
              content: <OfferForm offerDetailAPI={offerAPI} />,
            },
            {
              id: 5,
              title: 'Compliance and Safety',
              content: (
                <ComplianceSafetyForm
                  complianceSafetyAPI={complianceSafetyAPI}
                />
              ),
            },
          ].map((section) =>
            unlockedSections.includes(section.id) ? (
              <div
                key={section.id}
                className="bg-white rounded-xl shadow-sm mb-4"
              >
                <div
                  className={`p-4 border-[#CBD5E1] flex justify-between items-center gap-6 ${!collapsedSections.includes(section.id) && 'border-b '}`}
                >
                  <h2 className="text-lg font-medium">
                    {section.id}. {section.title}
                  </h2>
                  {section.id === 4 && (
                    <div className="flex flex-1 items-center justify-between gap-4">
                      <a href="#" className="text-blue-700 font-medium text-sm">
                        Apply campaign
                      </a>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="skipAndAddLater"
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <label
                          htmlFor="skipAndAddLater"
                          className="ml-2 text-sm text-gray-700"
                        >
                          Skip and add later
                        </label>
                      </div>
                    </div>
                  )}
                  <button
                    aria-label="Collapse Section"
                    onClick={() => {
                      setCollapsedSections((prev) =>
                        prev.includes(section.id)
                          ? prev.filter((id) => id !== section.id)
                          : [...prev, section.id]
                      );
                    }}
                  >
                    {collapsedSections.includes(section.id) ? expand : collapse}
                  </button>
                </div>
                <div
                  className={`transition-all duration-300 ease-in-out p-4 ${
                    collapsedSections.includes(section.id) && 'hidden'
                    // ? 'max-h-0 opacity-0 p-0'
                    // : 'max-h-[2000px] opacity-100'
                  }`}
                >
                  {section.content}
                </div>
              </div>
            ) : (
              <div
                key={section.id}
                className="bg-white rounded-lg shadow-sm mb-4"
              >
                <div className="p-4 flex justify-between items-center">
                  <h2 className="text-lg font-medium">
                    {section.id}. {section.title}
                  </h2>
                  <div className="flex items-center text-gray-400">
                    <span className="text-sm mr-2">
                      Available only after unlocking this section
                    </span>
                    {listingSectionLocked}
                  </div>
                </div>
              </div>
            )
          )}
        </section>

        {/* Right sidebar */}
        <aside className="w-72" aria-label="Quality and Tips Sidebar">
          <ListingQuantity
            checkCircle={checkCircle}
            circleEmpty={circleEmpty}
          />

          <TipsAndTricks
            showTipAndTrick={showTipAndTrick}
            tipAndTrick={tipAndTrick}
            tipAndTrickContent={tipAndTrickContent}
          />
        </aside>
      </div>
    </main>
  );
};

export default CreateListing;
