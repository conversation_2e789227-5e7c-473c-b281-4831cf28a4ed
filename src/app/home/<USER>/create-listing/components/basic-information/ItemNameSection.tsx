import TextArea from '@/components/TextArea';
import Tooltip from '@/components/Tooltip';
import { info } from '@/utils/icon';
import React from 'react';

interface ItemNameSectionProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  maxLength?: number;
  placeholder?: string;
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
}

const ItemNameSection: React.FC<ItemNameSectionProps> = ({
  value,
  onChange,
  onBlur,
  maxLength = 200,
  placeholder,
  setTipAndTrickContent,
}) => (
  <div className="mb-6">
    <div className="flex items-center mb-1">
      <h3 className="font-medium">Item Name</h3>
      <span className="text-red-500 ml-1">*</span>
      <div className="ml-2 text-gray-500 flex items-center">
        <div
          className="relative inline-block group"
          onClick={() =>
            setTipAndTrickContent({
              title: 'Item Name',
              content:
                'Keep it clear and helpful.\nStart with your brand, describe the product, include size or quantity if needed.\nTry to keep it under 200 characters.',
            })
          }
        >
          {info}
          <Tooltip className="bottom-1/2">
            The official name of the product.
          </Tooltip>
        </div>
      </div>
    </div>
    <TextArea
      value={value}
      onChange={onChange}
      maxLength={maxLength}
      placeholder={placeholder}
      onBlur={onBlur}
    />
  </div>
);

export default ItemNameSection;
