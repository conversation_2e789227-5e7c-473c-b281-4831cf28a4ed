import Tooltip from '@/components/Tooltip';
import { deleteIc, editIc, imageUpload, info } from '@/utils/icon';
import Image from 'next/image';
import React, { useRef } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

interface ProductImagesSectionProps {
  images: { file: File; url: string; name: string; title: string }[];
  setImages: (
    imgs: { file: File; url: string; name: string; title: string }[]
  ) => void;
  handleImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRefs: React.MutableRefObject<any[]>;
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
}

const ItemType = { IMAGE: 'image' };

function DraggableImage({ img, idx, moveImage, onEdit, onRemove }) {
  const ref = useRef(null);
  const [, drop] = useDrop({
    accept: ItemType.IMAGE,
    hover(item: any) {
      if (item.idx === idx) return;
      moveImage(item.idx, idx);
      item.idx = idx;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: ItemType.IMAGE,
    item: { idx },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drag(drop(ref));
  return (
    <div
      ref={ref}
      className="w-30 h-30 rounded-lg overflow-hidden border border-gray-300 flex items-center justify-center relative group bg-white"
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <Image
        src={img.url}
        alt={`preview-${idx}`}
        width={120}
        height={120}
        className="object-cover w-full h-full"
      />
      <div className="absolute left-2 top-2 cursor-move rounded text-white text-xl opacity-0 bg-black bg-opacity-20 group-hover:opacity-60 hover:bg-gray-500">
        ⋮⋮
      </div>
      <div className="absolute left-0 right-0 bottom-0 h-12 bg-black bg-opacity-20 flex items-center justify-around opacity-0 group-hover:opacity-60 transition-opacity">
        <button
          type="button"
          className="rounded flex items-center justify-center w-10 h-10 text-white hover:bg-gray-500 hover:bg-opacity-20"
          onClick={() => onEdit(idx)}
        >
          {editIc('#FFF')}
        </button>
        <button
          type="button"
          className="rounded flex items-center justify-center w-10 h-10 text-white hover:bg-gray-500 hover:bg-opacity-20"
          onClick={() => onRemove(idx)}
        >
          {deleteIc('#FFF')}
        </button>
      </div>
    </div>
  );
}

const ProductImagesSection: React.FC<ProductImagesSectionProps> = ({
  images,
  setImages,
  handleImageChange,
  fileInputRefs,
  setTipAndTrickContent,
}) => {
  // Handlers for edit, remove, replace, drag-and-drop
  const handleRemoveImage = (idx) => {
    const toRemove = images[idx];
    URL.revokeObjectURL(toRemove.url);
    setImages(images.filter((_, i) => i !== idx));
  };
  const handleEditImage = (idx) => {
    if (fileInputRefs.current[idx]) {
      fileInputRefs.current[idx].click();
    }
  };
  const handleReplaceImage = (idx, e) => {
    if (!e.target.files || e.target.files.length === 0) return;
    const file = e.target.files[0];
    const url = URL.createObjectURL(file);
    URL.revokeObjectURL(images[idx].url);
    const newImages = images.slice();
    newImages[idx] = {
      file,
      url,
      name: '',
      title: '',
    };
    setImages(newImages);
  };

  const moveImage = (from, to) => {
    if (from === to) return;
    const updated = images.slice();
    const [moved] = updated.splice(from, 1);
    updated.splice(to, 0, moved);
    setImages(updated);
  };

  return (
    <div className="mb-6">
      <div className="flex items-center mb-1">
        <h3 className="font-medium">Product Images</h3>
        <span className="text-red-500 ml-1">*</span>
        <div className="ml-2 text-gray-500 flex items-center">
          <div
            className="relative inline-block group"
            onClick={() =>
              setTipAndTrickContent({
                title: 'Product Images',
                content:
                  'Use a clear photo, and make sure your product fills most of the frame.\nAvoid text, logos, or extras.\nShow off your product from every angle.',
              })
            }
          >
            {info}
            <Tooltip className="bottom-1/2">
              Visual representations of the product.
            </Tooltip>
          </div>
        </div>
      </div>
      <p className="text-xs text-gray-500 mb-4">
        Maximum 9 images are allowed.
      </p>
      <DndProvider backend={HTML5Backend}>
        <div className="flex gap-2 flex-wrap">
          {images.map((img, idx) => (
            <div key={idx} className="relative">
              <DraggableImage
                img={img}
                idx={idx}
                moveImage={moveImage}
                onEdit={handleEditImage}
                onRemove={handleRemoveImage}
              />
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={(el) => {
                  fileInputRefs.current[idx] = el;
                }}
                onChange={(e) => handleReplaceImage(idx, e)}
              />
            </div>
          ))}
          {images.length < 9 && (
            <label className="border border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center w-30 h-30 hover:bg-[#F3F4F6] cursor-pointer">
              {imageUpload}
              <p className="text-xs font-medium font-sans text-gray-500 whitespace-nowrap py-2">
                Add an image
              </p>
              <input
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleImageChange}
              />
            </label>
          )}
        </div>
      </DndProvider>
    </div>
  );
};

export default ProductImagesSection;
