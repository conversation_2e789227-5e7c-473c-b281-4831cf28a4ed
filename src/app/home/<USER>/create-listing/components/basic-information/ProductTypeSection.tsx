import ProductTypeModal from '@/components/modal/ProductTypeModal';
import Tooltip from '@/components/Tooltip';
import { info } from '@/utils/icon';
import React from 'react';

interface ProductTypeSectionProps {
  productType: string;
  itemName: string;
  modalOpen: boolean;
  setModalOpen: (open: boolean) => void;
  onSelectType: (type: string) => void;
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
}

const ProductTypeSection: React.FC<ProductTypeSectionProps> = ({
  productType,
  itemName,
  modalOpen,
  setModalOpen,
  onSelectType,
  setTipAndTrickContent,
}) => (
  <div className="mb-6">
    <div className="flex items-center mb-1">
      <h3 className="font-medium">Product Type</h3>
      <span className="text-red-500 ml-1">*</span>
      <div
        className="relative inline-block group"
        onClick={() =>
          setTipAndTrickContent({
            title: 'Product Type',
            content:
              'This tells Amazon where your product belongs.\nChoose the type that best matches what you’re selling - it helps customers find you faster!',
          })
        }
      >
        {info}
        <Tooltip className="bottom-1/2">
          The classification that specifies what type of product it is
        </Tooltip>
      </div>
    </div>
    {itemName && (
      <>
        <div className="flex items-center gap-2 rounded-xl mb-1 mt-2">
          <input
            type="text"
            value={productType}
            disabled
            className="flex-1 bg-[#F8FAFC] p-2 border-1 border-[#E2E8F0] text-gray-700 text-base font-medium cursor-not-allowed px-2 py-2 rounded-lg"
          />
          <button
            type="button"
            className="px-4 py-2 rounded-lg bg-white border border-[#CBD5E1] text-gray-700 font-medium hover:bg-gray-100"
            onClick={() => setModalOpen(true)}
          >
            Change
          </button>
        </div>
        <ProductTypeModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          onSelect={onSelectType}
        />
      </>
    )}
    <p className="text-xs text-gray-500 mb-4">
      {itemName
        ? 'Examples include: Wall clocks, Artificial plants, Vases, Tapestries'
        : 'We will recommend based on your item name'}
    </p>
  </div>
);

export default ProductTypeSection;
