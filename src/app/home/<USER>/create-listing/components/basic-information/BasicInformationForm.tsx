import { useAppDispatch, useAppSelector } from '@/app/hook';
import { updateHatForm } from '@/app/store/product-listing';
import { Button } from '@/components/Button';
import { getProductTypesByKeywords } from '@/utils/api';
import React, { useRef, useState } from 'react';
import BrandSection from './BrandSection';
import ItemNameSection from './ItemNameSection';
import ProductIdSection from './ProductIdSection';
import ProductImagesSection from './ProductImagesSection';
import ProductTypeSection from './ProductTypeSection';

interface BasicInformationFormProps {
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
  basicInformationAPI: any;
  submitHandlerProps: () => void;
  showActionButtons?: boolean;
}

const BasicInformationForm: React.FC<BasicInformationFormProps> = ({
  setTipAndTrickContent,
  basicInformationAPI,
  submitHandlerProps,
  showActionButtons = false,
}) => {
  const [itemName, setItemName] = useState('');
  const [images, setImages] = useState<
    { file: File; url: string; name: string; title: string }[]
  >([]);
  const [productType, setProductType] = useState('Home and Kitchen');
  const [productTypeModalOpen, setProductTypeModalOpen] = useState(false);
  const [noBrand, setNoBrand] = useState(true);
  const [brandName, setBrandName] = useState('');
  const [noProductId, setNoProductId] = useState(true);
  const [productIdType, setProductIdType] = useState('Type');
  const [productId, setProductId] = useState('');
  const [showProductIdTypeDropdown, setShowProductIdTypeDropdown] =
    useState(false);
  const fileInputRefs = useRef([]);

  const hatForm = useAppSelector((state) => state.productListing.hatForm);

  const dispatch = useAppDispatch();

  const imagesAPI = basicInformationAPI.images;

  const productIDAPI =
    basicInformationAPI.externally_assigned_product_identifier;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const selectedFiles = Array.from(e.target.files);

    const newImages = [
      ...images,
      ...selectedFiles.slice(0, 9 - images.length).map((file) => ({
        file,
        url: URL.createObjectURL(file),
        name: Object.keys(imagesAPI)[
          images.length + selectedFiles.indexOf(file)
        ],
        title:
          imagesAPI[
            Object.keys(imagesAPI)[images.length + selectedFiles.indexOf(file)]
          ].title,
      })),
    ];

    setImages(newImages);
  };

  const submitHandler = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    submitHandlerProps();

    const newHatForm = { ...hatForm };

    // Include all fields from the schema with their current values or default empty values
    Object.entries(basicInformationAPI).forEach(
      ([key, schema]: [string, any]) => {
        // Skip images as they are handled separately
        if (key === 'images') return;

        // Skip if the field already has a value
        if (newHatForm[key] !== undefined) return;

        // Set default values based on field type
        if (key === 'item_name') {
          newHatForm[key] = [{ value: itemName || '' }];
        } else if (key === 'brand') {
          newHatForm[key] = [{ value: brandName || '' }];
        } else if (key === 'externally_assigned_product_identifier') {
          newHatForm[key] = [
            { type: productIdType },
            { value: productId || '' },
          ];
        } else if (schema.type === 'boolean') {
          newHatForm[key] = [{ value: false }];
        } else if (schema.type === 'number' || schema.type === 'integer') {
          newHatForm[key] = [{ value: 0 }];
        } else if (schema.type === 'array') {
          newHatForm[key] = [];
        } else {
          newHatForm[key] = [{ value: '' }];
        }
      }
    );

    // Handle images separately
    images.forEach((img) => {
      newHatForm[img.name] = [{ value: img.url }];
    });

    dispatch(updateHatForm(newHatForm));
  };

  const itemNameBlurHandler = async (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    try {
      const response = await getProductTypesByKeywords(itemName);
      const data = JSON.parse(response.data);
      setProductType(data.product_type);
    } catch (error) {
      console.error('Error fetching product types:', error);
    }
  };

  return (
    <form
      className="m-0"
      autoComplete="off"
      aria-label="Basic Information Form"
      onSubmit={submitHandler}
    >
      <ProductImagesSection
        images={images}
        setImages={setImages}
        handleImageChange={handleImageChange}
        fileInputRefs={fileInputRefs}
        setTipAndTrickContent={setTipAndTrickContent}
      />
      <ItemNameSection
        value={itemName}
        onChange={(e) => setItemName(e.target.value)}
        onBlur={itemNameBlurHandler}
        maxLength={200}
        placeholder="Enter your item name here"
        setTipAndTrickContent={setTipAndTrickContent}
      />
      <ProductTypeSection
        productType={productType}
        itemName={itemName}
        modalOpen={productTypeModalOpen}
        setModalOpen={setProductTypeModalOpen}
        onSelectType={(cat) => setProductType(cat)}
        setTipAndTrickContent={setTipAndTrickContent}
      />
      <BrandSection
        noBrand={noBrand}
        setNoBrand={setNoBrand}
        brandName={brandName}
        setBrandName={setBrandName}
        setTipAndTrickContent={setTipAndTrickContent}
      />
      <ProductIdSection
        noProductId={noProductId}
        setNoProductId={setNoProductId}
        productIdType={productIdType}
        setProductIdType={setProductIdType}
        productId={productId}
        setProductId={setProductId}
        showDropdown={showProductIdTypeDropdown}
        setShowDropdown={setShowProductIdTypeDropdown}
        setTipAndTrickContent={setTipAndTrickContent}
        productIDAPI={productIDAPI}
      />
      {showActionButtons && (
        <div className="flex justify-end items-center gap-4 mt-4">
          <Button className="!mt-0 !w-auto px-6 bg-white hover:bg-white border-1 border-[#CBD5E1]">
            Cancel
          </Button>
          <Button type="submit" className="!mt-0 !w-auto px-6">
            Submit
          </Button>
        </div>
      )}
    </form>
  );
};

export default BasicInformationForm;
