import { info } from '@/utils/icon';
import React from 'react';

interface BrandSectionProps {
  noBrand: boolean;
  setNoBrand: (v: boolean) => void;
  brandName: string;
  setBrandName: (v: string) => void;
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
}

const BrandSection: React.FC<BrandSectionProps> = ({
  noBrand,
  setNoBrand,
  brandName,
  setBrandName,
  setTipAndTrickContent,
}) => (
  <div className="mb-6">
    <div className="flex items-center mb-2">
      <input
        type="checkbox"
        id="noBrand"
        checked={noBrand}
        onChange={() => setNoBrand(!noBrand)}
        className="w-4 h-4 mr-2"
      />
      <label htmlFor="noBrand" className="text-sm select-none">
        This product has no brand name
      </label>
    </div>
    {!noBrand && (
      <>
        <div className="flex items-center mb-2 justify-between">
          <div
            className="flex items-center gap-1"
            onClick={() =>
              setTipAndTrickContent({
                title: 'Brand Name',
                content:
                  'Type your brand name exactly as it appears on the product or packaging.',
              })
            }
          >
            <span className="font-medium text-sm">Brand Name</span>
            <span className="ml-1">{info}</span>
          </div>
          <a
            href="#"
            className="text-blue-700 text-sm font-medium hover:underline"
            rel="noopener noreferrer"
          >
            Brand name policy
          </a>
        </div>
        <div className="relative">
          <input
            type="text"
            value={brandName}
            onChange={(e) => setBrandName(e.target.value)}
            maxLength={100}
            className="w-full rounded-lg border-2 border-[#CBD5E1] px-4 py-3 text-sm outline-none focus:border-yellow-500"
          />
          <span className="absolute bottom-2 right-4 text-gray-500 text-sm pointer-events-none select-none px-1">
            {brandName.length}/100
          </span>
        </div>
      </>
    )}
  </div>
);

export default BrandSection;
