import Tooltip from '@/components/Tooltip';
import { info } from '@/utils/icon';
import React, { useEffect, useState } from 'react';

interface ProductIdSectionProps {
  noProductId: boolean;
  setNoProductId: (v: boolean) => void;
  productIdType: string;
  setProductIdType: (v: string) => void;
  productId: string;
  setProductId: (v: string) => void;
  showDropdown: boolean;
  setShowDropdown: (v: boolean) => void;
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
  productIDAPI: any;
}

// Validation functions for different product ID types
const validateProductId = (
  type: string,
  value: string
): { isValid: boolean; error?: string } => {
  if (!value) return { isValid: false, error: 'Product ID is required' };

  const trimmedValue = value.trim();

  switch (type) {
    case 'UPC':
      // UPC-A: 12 digits
      if (!/^\d{12}$/.test(trimmedValue)) {
        return {
          isValid: false,
          error: 'UPC must be 12 digits',
        };
      }
      break;

    case 'EAN':
      // EAN-13: 13 digits
      if (!/^\d{13}$/.test(trimmedValue)) {
        return {
          isValid: false,
          error: 'EAN must be 13 digits',
        };
      }
      break;

    case 'ASIN':
      // ASIN: 10 alphanumeric characters
      if (!/^[A-Z0-9]{10}$/i.test(trimmedValue)) {
        return {
          isValid: false,
          error: 'ASIN must be 10 alphanumeric characters',
        };
      }
      break;
  }

  return { isValid: true };
};

const ProductIdSection: React.FC<ProductIdSectionProps> = ({
  noProductId,
  setNoProductId,
  productIdType,
  setProductIdType,
  productId,
  setProductId,
  showDropdown,
  setShowDropdown,
  setTipAndTrickContent,
  productIDAPI,
}) => {
  const [error, setError] = useState<string>('');
  const [isTouched, setIsTouched] = useState(false);
  useEffect(() => {
    if (isTouched) {
      const validation = validateProductId(productIdType, productId);
      setError(validation.error || '');
    }
  }, [productId, productIdType, isTouched]);

  const handleProductIdChange = (value: string) => {
    setProductId(value);
    if (!isTouched) setIsTouched(true);
  };

  const handleProductIdTypeChange = (type: string) => {
    setProductIdType(type);
    if (!isTouched) setIsTouched(true);
    setShowDropdown(false);
  };

  return (
    <div className="mb-6">
      <div className="flex items-center mb-2">
        <input
          type="checkbox"
          id="noProductId"
          checked={noProductId}
          onChange={() => setNoProductId(!noProductId)}
          className="w-4 h-4 mr-2"
        />
        <label htmlFor="noProductId" className="text-sm select-none">
          I have no product ID (UPC/EAN/ASIN)
        </label>
      </div>
      {!noProductId && (
        <>
          <div className="flex items-center mb-2 gap-2">
            <span className="font-medium text-sm">Product ID</span>
            <div className="ml-2 text-gray-500 flex items-center">
              <div className="relative inline-block group">
                {info}
                <Tooltip className="bottom-1/2">
                  Unique identifiers used to track the product.
                </Tooltip>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <div className="relative">
              <button
                type="button"
                className="flex items-center border border-[#CBD5E1] px-4 py-3 rounded-lg text-sm min-w-[90px] focus:border-yellow-500 outline-none"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                {productIdType}
                <svg
                  className="ml-2 w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              {showDropdown && (
                <div className="absolute left-0 mt-1 w-full bg-white border border-[#CBD5E1] rounded-lg shadow z-10">
                  {productIDAPI.items.type.enumNames.map((type) => (
                    <div
                      key={type}
                      className="px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                      onClick={() => {
                        handleProductIdTypeChange(type);
                      }}
                    >
                      {type}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="flex-1">
              <input
                type="text"
                value={productId}
                onChange={(e) => handleProductIdChange(e.target.value)}
                onBlur={() => setIsTouched(true)}
                placeholder={`Enter your ${productIdType} here`}
                className={`w-full rounded-lg border px-4 py-3 text-sm outline-none focus:border-yellow-500 ${
                  error ? 'border-red-500' : 'border-[#CBD5E1]'
                }`}
              />
              {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProductIdSection;
