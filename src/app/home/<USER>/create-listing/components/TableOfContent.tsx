import { useAppSelector } from '@/app/hook';
import { listingSectionLocked } from '@/utils/icon';
import React from 'react';

interface TableOfContentProps {
  activeSection: number;
  setActiveSection: (id: number) => void;
  setUnlockedSections: React.Dispatch<React.SetStateAction<number[]>>;
  unlockedSections: number[];
  setCollapsedSections: React.Dispatch<React.SetStateAction<number[]>>;
}

const sections = [
  { id: 1, title: 'Basic Information' },
  { id: 2, title: 'Listing Content' },
  { id: 3, title: 'Product Detail' },
  { id: 4, title: 'Offer' },
  { id: 5, title: 'Compliance and Safety' },
];

const TableOfContent: React.FC<TableOfContentProps> = ({
  activeSection,
  setActiveSection,
  setUnlockedSections,
  unlockedSections,
  setCollapsedSections,
}) => {
  const hatForm = useAppSelector((state) => state.productListing.hatForm);
  return (
    <div className="bg-white rounded-lg shadow-sm mb-4">
      <div className="p-4 border-b border-[#CBD5E1]">
        <h2 className="font-medium">Table of content</h2>
      </div>
      <div className="py-2">
        <ul className="space-y-2">
          {sections.map((section) => (
            <li
              key={section.id}
              className={`flex gap-2 py-2 px-4 rounded-lg 
              ${unlockedSections.includes(section.id) ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}
              ${activeSection === section.id ? ' text-[#CA8A04] font-medium' : ''}`}
              onClick={() => {
                if (unlockedSections.includes(section.id)) {
                  setActiveSection(section.id);
                  setCollapsedSections(
                    [1, 2, 3, 4, 5].filter((id) => id !== section.id)
                  );
                }
                console.log('hatForm: ', hatForm);
              }}
            >
              {section.id}. {section.title}
              {!unlockedSections.includes(section.id) && (
                <span>{listingSectionLocked}</span>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default TableOfContent;
