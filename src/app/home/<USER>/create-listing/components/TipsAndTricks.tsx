import React from 'react';

interface TipsAndTricksProps {
  showTipAndTrick: boolean;
  tipAndTrick: React.ReactNode;
  tipAndTrickContent: {
    title: string;
    content: string;
  };
}

const TipsAndTricks: React.FC<TipsAndTricksProps> = ({
  showTipAndTrick,
  tipAndTrick,
  tipAndTrickContent,
}) => {
  if (!showTipAndTrick) return null;

  return (
    <div className="bg-blue-50 mt-4">
      <div
        className={`flex items-center justify-between p-4 border border-[#1D4ED8] ${tipAndTrickContent.title === 'Select an input field' ? 'rounded-lg' : 'rounded-t-lg'}`}
      >
        <div>
          <h1 className="text-xs font-normal">Tips and trick</h1>
          <p className="text-blue-800 font-medium mt-2">
            {tipAndTrickContent.title}
          </p>
        </div>
        {tipAndTrick}
      </div>
      {tipAndTrickContent.title !== 'Select an input field' && (
        <div className="bg-white p-4 rounded-b-lg border border-[#1D4ED8]">
          <ul className="list-disc list-inside text-sm text-gray-700 mt-2 flex flex-col gap-2">
            {tipAndTrickContent.content.split('\n').map((item, index) => (
              <li key={index} className="text-sm font-normal text-[#000000]">
                {item}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TipsAndTricks;
