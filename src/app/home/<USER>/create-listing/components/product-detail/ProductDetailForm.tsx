import { useAppDispatch, useAppSelector } from '@/app/hook';
import { updateHatForm } from '@/app/store/product-listing';
import React, { useRef } from 'react';
import {
  formatField,
  getFieldType,
  renderField,
  setValueByPath,
  shouldShowField,
} from '../../utils';

// Define the schema type
type SchemaType = {
  type?: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
  [key: string]: any;
};

const ProductDetailForm: React.FC<{
  productDetailAPI: any;
}> = ({ productDetailAPI }) => {
  // store form values without triggering re-renders on every keystroke
  const formRef = useRef<Record<string, any>>({});

  const attributeFilter = useAppSelector((state) => state.ui.attributeFilter);
  const hatForm = useAppSelector((state) => state.productListing.hatForm);

  const dispatch = useAppDispatch();

  // Path-based handler
  const handleChange = (path: string[], value: any) => {
    formRef.current = setValueByPath(formRef.current, path, value);
  };

  const submitHandler = () => {
    const raw = formRef.current;
    const newHatForm = {
      ...hatForm,
      // Include all fields from the schema with their current values or default empty values
      ...Object.entries<SchemaType>(productDetailAPI).reduce<
        Record<string, any>
      >((acc, [key, schema]) => {
        // If the field has a value, use it, otherwise use the default empty value based on schema type
        const fieldValue =
          raw[key] !== undefined
            ? raw[key]
            : schema?.type === 'boolean'
              ? false
              : schema?.type === 'number' || schema?.type === 'integer'
                ? 0
                : schema?.type === 'array'
                  ? []
                  : '';

        return {
          ...acc,
          [key]: formatField(fieldValue),
        };
      }, {}),
    };

    dispatch(updateHatForm(newHatForm));
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Only run if the blur is caused by clicking outside the form
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      submitHandler();
    }
  };

  return (
    <form
      className="space-y-6"
      autoComplete="off"
      aria-label="Product Detail Form"
      onBlur={handleBlur}
    >
      <div className="flex flex-col">
        {/* General Section for top-level fields */}
        <div className="border-[#E2E8F0] border-b flex py-4 gap-8">
          <div className="w-40">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">General</span>
            </div>
          </div>
          <div className="flex-1 flex-col gap-6 flex mb-4">
            {Object.entries(productDetailAPI).map(([key, schema]: any) => {
              const fieldType = getFieldType(schema);
              // Only render as part of General if not a group/section
              if (
                shouldShowField(schema, attributeFilter) &&
                !(
                  fieldType === 'array' &&
                  schema.items &&
                  typeof schema.items === 'object' &&
                  !Array.isArray(schema.items) &&
                  Object.entries(schema.items).length > 1
                )
              ) {
                return (
                  <div key={key} className="mb-4">
                    {renderField(key, schema, [key], handleChange, formRef)}
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
        {/* Render grouped/sectioned fields */}
        {Object.entries(productDetailAPI).map(([key, schema]: any) => {
          const fieldType = getFieldType(schema);
          if (
            shouldShowField(schema, attributeFilter) &&
            fieldType === 'array' &&
            schema.items &&
            typeof schema.items === 'object' &&
            !Array.isArray(schema.items) &&
            Object.entries(schema.items).length > 1
          ) {
            return renderField(key, schema, [key], handleChange, formRef);
          }
          return null;
        })}
      </div>
    </form>
  );
};

export default ProductDetailForm;
