import React from 'react';

interface ListingQuantityProps {
  checkCircle: React.ReactNode;
  circleEmpty: React.ReactNode;
}

const ListingQuantity: React.FC<ListingQuantityProps> = ({
  checkCircle,
  circleEmpty,
}) => (
  <div className="bg-white rounded-lg shadow-sm p-4">
    <div className="flex justify-between items-center mb-4">
      <div>
        <h2 className="font-normal text-xs text-[#64748B]">Listing quality</h2>
        <span className="text-blue-600 font-medium mr-2">Good</span>
      </div>
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-full border-4 border-blue-600 flex items-center justify-center text-blue-600 font-medium">
          0
        </div>
      </div>
    </div>

    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {checkCircle}
          <span className="ml-2 text-sm mr-3">Add at least 3 images</span>
        </div>
        <span className="text-sm font-medium">6</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {checkCircle}
          <span className="ml-2 text-sm mr-3">Have 5 bullet points</span>
        </div>
        <span className="text-sm font-medium">5</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {checkCircle}
          <span className="ml-2 text-sm mr-3">
            100-200 characters each bullet point
          </span>
        </div>
        <span className="text-sm font-medium text-green-600">160</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {checkCircle}
          <span className="ml-2 text-sm mr-3">
            1000-2000 characters description
          </span>
        </div>
        <span className="text-sm font-medium text-green-600">1298</span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {circleEmpty}
          <span className="ml-2 text-sm">Add a brand name</span>
        </div>
        <span className="text-sm font-medium"></span>
      </div>
    </div>
  </div>
);

export default ListingQuantity;
