import { deleteIc } from '@/utils/icon';
import React, { useEffect, useRef, useState } from 'react';
import {
  allowsFreeTextInput,
  canAddMoreSelects,
  canRemoveSelects,
  getArrayConstraints,
  getEnumOptions,
} from '../utils';
import EnumFieldInput from './EnumFieldInput';

interface DynamicSelectArrayProps {
  schema: any;
  value: Array<{ value: string }>;
  onChange: (value: Array<{ value: string }>) => void;
  title: string;
}

const DynamicSelectArray: React.FC<DynamicSelectArrayProps> = ({
  schema,
  value = [],
  onChange,
  title,
}) => {
  const [selectValues, setSelectValues] = useState<string[]>(
    value.length > 0 ? value.map((item) => item.value) : ['']
  );
  // Only compute constraints once, options/itemSchema per select
  const constraints = getArrayConstraints(schema);

  // Only reset selectValues when the actual values change, not on every render
  const valueString = JSON.stringify(value);

  useEffect(() => {
    const newValues = value.length > 0 ? value.map((item) => item.value) : [''];
    // Only update if the values are actually different
    if (JSON.stringify(selectValues) !== JSON.stringify(newValues)) {
      setSelectValues(newValues);
    }
  }, [valueString]); // Only depend on the stringified value

  useEffect(() => {
    // Initialize with minimum required items if empty
    if (selectValues.length === 0 && constraints.minUniqueItems > 0) {
      const initialValues = Array(constraints.minUniqueItems).fill('');
      setSelectValues(initialValues);
    }
  }, [constraints.minUniqueItems, selectValues.length]);

  // Format values based on schema structure
  const formatValuesForParent = (values: string[]) => {
    const filteredValues = values.filter((val) => val.trim() !== '');

    // Special handling for closure.type structure
    if (
      schema?.items?.type === 'array' &&
      schema.items.items?.value?.type === 'string'
    ) {
      return filteredValues.map((val) => ({
        value: val,
        language_tag: 'en_US',
      }));
    }

    // Default behavior for other fields
    return filteredValues.map((val) => ({ value: val }));
  };

  // Keep track of the last filtered values to prevent unnecessary updates
  const lastFilteredValuesRef = useRef<any[]>([]);

  useEffect(() => {
    const formattedValues = formatValuesForParent(selectValues);

    // Only update parent if values actually changed
    if (
      JSON.stringify(formattedValues) !==
      JSON.stringify(lastFilteredValuesRef.current)
    ) {
      lastFilteredValuesRef.current = formattedValues;
      onChange(formattedValues);
    }
  }, [selectValues, onChange]);

  const handleSelectChange = (index: number, newValue: string) => {
    const newValues = [...selectValues];
    newValues[index] = newValue;
    setSelectValues(newValues);
  };

  const addSelect = () => {
    if (canAddMoreSelects(selectValues.length, schema)) {
      setSelectValues([...selectValues, '']);
    }
  };

  const removeSelect = (index: number) => {
    if (canRemoveSelects(selectValues.length, schema)) {
      const newValues = selectValues.filter((_, i) => i !== index);
      setSelectValues(newValues);
    }
  };

  const renderSelect = (value: string, index: number) => {
    // Support array of schemas for items, or single schema
    const currentItemSchema = Array.isArray(schema.items)
      ? schema.items[index]
      : schema.items?.value || schema.items;
    const currentOptions = getEnumOptions(currentItemSchema);
    const currentAllowsFreeText = allowsFreeTextInput(currentItemSchema);
    return (
      <div key={index} className="flex items-center gap-2 mb-2">
        <div className="relative flex-1">
          <EnumFieldInput
            fieldKey={`${(schema?.title || '').replace(/\s+/g, '-').toLowerCase()}-options-${index}`}
            title={title}
            value={value}
            options={currentOptions}
            allowsFreeText={currentAllowsFreeText}
            onChange={(newValue) => handleSelectChange(index, newValue)}
            className="text-sm w-full border border-[#CBD5E1] rounded-lg py-3 px-4 bg-white focus:border-[#EAB308] focus:border-2 focus:outline-none"
            maxLength={currentItemSchema?.maxLength}
            minLength={currentItemSchema?.minLength}
          />
        </div>
        {/* Remove button - only show if we can remove and it's not the only item */}
        {canRemoveSelects(selectValues.length, schema) &&
          selectValues.length > 1 && (
            <button
              type="button"
              onClick={() => removeSelect(index)}
              className="text-red-500 hover:text-red-700 p-1 rounded"
              title="Remove"
            >
              {deleteIc('')}
            </button>
          )}
      </div>
    );
  };

  return (
    <div className="mb-4">
      {/* Render all select boxes */}
      {selectValues.map((value, index) => renderSelect(value, index))}

      {/* Add button - only show if we can add more */}
      {canAddMoreSelects(selectValues.length, schema) && (
        <button
          type="button"
          onClick={addSelect}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-1"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Add more
        </button>
      )}
    </div>
  );
};

export default DynamicSelectArray;
