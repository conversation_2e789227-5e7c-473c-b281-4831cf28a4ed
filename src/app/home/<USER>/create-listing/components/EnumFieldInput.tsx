import { CustomSelect } from '@/components/CustomSelect';
import Input from '@/components/Input';
import { EnumOption } from '@/types';
import React from 'react';

interface EnumFieldInputProps {
  fieldKey: string;
  title: string;
  value: string;
  options: EnumOption[];
  allowsFreeText: boolean;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  maxLength?: number;
  minLength?: number;
}

const EnumFieldInput: React.FC<EnumFieldInputProps> = ({
  fieldKey,
  title,
  value,
  options,
  allowsFreeText,
  onChange,
  placeholder,
  maxLength,
  minLength,
}) => {
  if (allowsFreeText) {
    return (
      <>
        <Input
          type="text"
          defaultValue={value}
          onChangeFunc={(e) => onChange(e.target.value)}
          list={`${fieldKey}-options`}
          placeholder={placeholder || `Enter or select ${title}`}
          maxLength={maxLength}
          minLength={minLength}
        />
        <datalist id={`${fieldKey}-options`}>
          {options.map((opt) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </datalist>
      </>
    );
  }

  return (
    <>
      <CustomSelect
        // defaultValue={value}
        onChange={(selectedLabel) => {
          const selectedOption = options.find(
            (opt) => opt.label === selectedLabel
          );
          onChange(selectedOption ? selectedOption.value : selectedLabel);
        }}
        options={options}
        placeholder={placeholder || `Select ${title}`}
        multiple={false}
      />
    </>
  );
};

export default EnumFieldInput;
