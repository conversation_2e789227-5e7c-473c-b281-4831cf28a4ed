import { useAppDispatch, useAppSelector } from '@/app/hook';
import { setAttributeFilter } from '@/app/store/ui';
import React from 'react';

const Attributes: React.FC = () => {
  const attributeFilter = useAppSelector((state) => state.ui.attributeFilter);
  const dispatch = useAppDispatch();

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-[#CBD5E1]">
        <h2 className="font-medium">Attributes</h2>
      </div>
      <div className="space-y-3 py-4">
        <div className="flex items-center px-4">
          <input
            type="radio"
            name="attribute-filter"
            checked={attributeFilter === 'required'}
            onChange={() => dispatch(setAttributeFilter('required'))}
            className="w-5 h-5 rounded-full border-2 border-black"
          />
          <span className="ml-2">Required</span>
        </div>
        <div className="flex items-center px-4">
          <input
            type="radio"
            name="attribute-filter"
            checked={attributeFilter === 'recommended'}
            onChange={() => dispatch(setAttributeFilter('recommended'))}
            className="w-5 h-5 rounded-full border-2 border-black"
          />
          <span className="ml-2">Recommended</span>
        </div>
        <div className="flex items-center px-4">
          <input
            type="radio"
            name="attribute-filter"
            checked={attributeFilter === 'all'}
            onChange={() => dispatch(setAttributeFilter('all'))}
            className="w-5 h-5 rounded-full border-2 border-black"
          />
          <span className="ml-2">All attributes</span>
        </div>
      </div>
    </div>
  );
};

export default Attributes;
