import { useAppDispatch, useAppSelector } from '@/app/hook';
import { updateHatForm } from '@/app/store/product-listing';
import Tooltip from '@/components/Tooltip';
import { info } from '@/utils/icon';
import React, { useRef } from 'react';
import {
  formatField,
  getFieldType,
  renderField,
  setValueByPath,
  shouldShowField,
} from '../../utils';

// Types
interface SectionContainerProps {
  title: string;
  tooltip: string;
  children: React.ReactNode;
}

// Reusable Components

const SectionContainer: React.FC<SectionContainerProps> = ({
  title,
  tooltip,
  children,
}) => (
  <div className="border-[#E2E8F0] border-b flex py-4 gap-8">
    <div className="w-30">
      <div className="flex items-center gap-2 mb-2">
        <span className="font-medium">{title}</span>
        <div className="text-gray-500 flex items-center">
          <div className="relative inline-block group">
            {info}
            <Tooltip className="bottom-1/2">{tooltip}</Tooltip>
          </div>
        </div>
      </div>
    </div>
    <div className="flex-1 flex-col gap-6 flex mb-4">{children}</div>
  </div>
);

const ComplianceSafetyForm = ({ complianceSafetyAPI }) => {
  const formRef = useRef<Record<string, any>>({});

  const attributeFilter = useAppSelector((state) => state.ui.attributeFilter);
  const hatForm = useAppSelector((state) => state.productListing.hatForm);

  const dispatch = useAppDispatch();

  const handleChange = (path, value) => {
    formRef.current = setValueByPath(formRef.current, path, value);
  };

  const generalFields = [];
  const groupedFields = [];

  Object.entries(complianceSafetyAPI).forEach(([key, schema]) => {
    if (!shouldShowField(schema, attributeFilter)) return;
    // If not a group/section, add to generalFields
    if (
      getFieldType(schema) !== 'array' ||
      (typeof schema === 'object' &&
        schema !== null &&
        'items' in schema &&
        schema.items &&
        typeof schema.items === 'object' &&
        !('type' in schema.items))
    ) {
      generalFields.push(
        renderField(key, schema, [key], handleChange, formRef)
      );
    } else {
      groupedFields.push(
        renderField(key, schema, [key], handleChange, formRef)
      );
    }
  });

  const submitHandler = () => {
    const newHatForm = {
      ...hatForm,
      // Format each field to include value and language_tag
      ...Object.keys(formRef.current).reduce(
        (acc, key) => ({
          ...acc,
          [key]: formatField(formRef.current[key]),
        }),
        {}
      ),
    };

    dispatch(updateHatForm(newHatForm));
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Only run if the blur is caused by clicking outside the form
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      submitHandler();
    }
  };

  return (
    <form
      className="space-y-6"
      autoComplete="off"
      aria-label="Compliance and Safety Form"
      onBlur={handleBlur}
    >
      <div className="flex flex-col">
        <SectionContainer
          title="General"
          tooltip="General compliance and safety details"
        >
          {generalFields}
        </SectionContainer>
        {groupedFields}
      </div>
    </form>
  );
};

export default ComplianceSafetyForm;
