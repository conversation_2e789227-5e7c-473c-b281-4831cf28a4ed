import { useAppDispatch, useAppSelector } from '@/app/hook';
import { updateHatForm } from '@/app/store/product-listing';
import React, { useRef } from 'react';
import {
  formatField,
  getFieldType,
  renderField,
  setValueByPath,
  shouldShowField,
} from '../../utils';
import { ToolTipContainer } from '../ToolTipContainer';

// Define the schema type
type SchemaType = {
  type?: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
  [key: string]: any;
};

const OfferForm: React.FC<{ offerDetailAPI: any }> = ({ offerDetailAPI }) => {
  const formRef = useRef<Record<string, any>>({});

  const attributeFilter = useAppSelector((state) => state.ui.attributeFilter);
  const hatForm = useAppSelector((state) => state.productListing.hatForm);

  const dispatch = useAppDispatch();

  const handleChange = (path: string[], value: any) => {
    formRef.current = setValueByPath(formRef.current, path, value);
  };

  const submitHandler = () => {
    const raw = formRef.current;
    const newHatForm = {
      ...hatForm,
      // Include all fields from the schema with their current values or default empty values
      ...Object.entries<SchemaType>(offerDetailAPI).reduce<Record<string, any>>(
        (acc, [key, schema]) => {
          // If the field has a value, use it, otherwise use the default empty value based on schema type
          const fieldValue =
            raw[key] !== undefined
              ? raw[key]
              : schema?.type === 'boolean'
                ? false
                : schema?.type === 'number' || schema?.type === 'integer'
                  ? 0
                  : schema?.type === 'array'
                    ? []
                    : '';

          return {
            ...acc,
            [key]: formatField(fieldValue),
          };
        },
        {}
      ),
    };

    dispatch(updateHatForm(newHatForm));
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Only run if the blur is caused by clicking outside the form
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      submitHandler();
    }
  };

  return (
    <form
      className="space-y-6"
      autoComplete="off"
      aria-label="Offer Form"
      onBlur={handleBlur}
    >
      <div className="flex flex-col">
        {/* General Section for top-level fields */}
        <div className="border-[#E2E8F0] border-b flex py-4 gap-8">
          <div className="w-40">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">General</span>
              {ToolTipContainer('General offer details')}
            </div>
          </div>
          <div className="flex-1 flex-col gap-6 flex mb-4">
            {Object.entries(offerDetailAPI).map(([key, schema]: any) => {
              const fieldType = getFieldType(schema);
              if (
                shouldShowField(schema, attributeFilter) &&
                !(
                  fieldType === 'array' &&
                  schema.items &&
                  typeof schema.items === 'object' &&
                  !Array.isArray(schema.items) &&
                  Object.entries(schema.items).length > 1
                )
              ) {
                return (
                  <div key={key} className="mb-4">
                    {renderField(key, schema, [key], handleChange, formRef)}
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
        {/* Render grouped/sectioned fields */}
        {Object.entries(offerDetailAPI).map(([key, schema]: any) => {
          const fieldType = getFieldType(schema);
          if (
            shouldShowField(schema, attributeFilter) &&
            fieldType === 'array' &&
            schema.items &&
            typeof schema.items === 'object' &&
            !Array.isArray(schema.items) &&
            Object.entries(schema.items).length > 1
          ) {
            return renderField(key, schema, [key], handleChange, formRef);
          }
          return null;
        })}
      </div>
    </form>
  );
};

export default OfferForm;
