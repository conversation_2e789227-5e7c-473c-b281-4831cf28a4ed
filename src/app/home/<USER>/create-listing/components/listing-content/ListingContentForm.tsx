import { useAppDispatch, useAppSelector } from '@/app/hook';
import { updateHatForm } from '@/app/store/product-listing';
import TextArea from '@/components/TextArea';
import Tooltip from '@/components/Tooltip';
import { addMore, deleteIc, info } from '@/utils/icon';
import React, { useRef, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

const MAX_DESCRIPTION = 2000;
const MAX_BULLET = 700;
const ItemType = { BULLET: 'bullet' };

function DraggableBullet({ idx, bullet, moveBullet, onChange, onDelete }) {
  const ref = useRef<HTMLDivElement>(null);
  const [, drop] = useDrop({
    accept: ItemType.BULLET,
    hover(item: { idx: number }) {
      if (item.idx === idx) return;
      moveBullet(item.idx, idx);
      item.idx = idx;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: ItemType.BULLET,
    item: { idx },
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });
  drag(drop(ref));
  return (
    <div
      ref={ref}
      className={`relative mb-4 flex items-start group bg-white ${isDragging ? 'opacity-50' : ''}`}
    >
      <div className="flex-1">
        <TextArea
          value={bullet.value}
          onChange={(e) => onChange(idx, e.target.value.slice(0, MAX_BULLET))}
          maxLength={MAX_BULLET}
          placeholder="Add brief descriptive text"
        />
      </div>
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="flex flex-col items-center select-none">
          <span className="cursor-move text-2xl text-[#030712] group-hover:opacity-100">
            ⋮⋮
          </span>
        </div>
        <button
          type="button"
          className="group-hover:opacity-100 rounded p-1"
          aria-label="Delete bullet"
          onClick={() => onDelete(idx)}
          tabIndex={0}
        >
          {deleteIc('#030712')}
        </button>
      </div>
    </div>
  );
}

interface ListingContentFormProps {
  setTipAndTrickContent: React.Dispatch<
    React.SetStateAction<{ title: string; content: string }>
  >;
}

const ListingContentForm: React.FC<ListingContentFormProps> = ({
  setTipAndTrickContent,
}) => {
  const [description, setDescription] = useState('');
  const [bullets, setBullets] = useState<{ value: string }[]>([{ value: '' }]);

  const hatForm = useAppSelector((state) => state.productListing.hatForm);
  const dispatch = useAppDispatch();

  const handleBulletChange = (idx: number, value: string) => {
    setBullets((prev) => prev.map((b, i) => (i === idx ? { value } : b)));
  };

  const addBullet = () => {
    setBullets((prev) => [...prev, { value: '' }]);
  };

  const deleteBullet = (idx: number) => {
    if (bullets.length === 1) return;
    setBullets((prev) => prev.filter((_, i) => i !== idx));
  };

  const moveBullet = (from: number, to: number) => {
    if (from === to) return;
    const updated = bullets.slice();
    const [moved] = updated.splice(from, 1);
    updated.splice(to, 0, moved);
    setBullets(updated);
  };

  const submitHandler = () => {
    const newHatForm = { ...hatForm };
    newHatForm['product_description'] = [{ value: description }];
    newHatForm['bullet_point'] = bullets.map((bullet) => ({
      value: bullet.value,
    }));

    console.log('newHatForm: ', newHatForm);

    dispatch(updateHatForm(newHatForm));
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Only run if the blur is caused by clicking outside the form
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      submitHandler();
    }
  };

  return (
    <form
      className="space-y-6"
      autoComplete="off"
      aria-label="Listing Content Form"
      onBlur={handleBlur}
    >
      {/* Product Description */}
      <div>
        <div className="flex items-center mb-1">
          <h3 className="font-medium">Product Description</h3>
          <span className="text-red-500 ml-1">*</span>
          <div className="ml-2 text-gray-500 flex items-center">
            <div
              className="relative inline-block group"
              onClick={() =>
                setTipAndTrickContent({
                  title: 'Product Description',
                  content:
                    'Include unique product features, product line details, and product specifications.\nDo not use all caps.\nTry to keep it under 2000 characters.',
                })
              }
            >
              {info}
              <Tooltip className="bottom-1/2">
                A detailed description of the product, its features, and
                benefits.
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="relative">
          <TextArea
            value={description}
            onChange={(e) =>
              setDescription(e.target.value.slice(0, MAX_DESCRIPTION))
            }
            maxLength={MAX_DESCRIPTION}
            placeholder="Enter your item name here"
            className="min-h-[200px]"
          />
        </div>
      </div>

      {/* Bullet Points */}
      <div>
        <div className="flex items-center mb-1">
          <h3 className="font-medium">Bullet Points</h3>
          <span className="text-red-500 ml-1">*</span>
          <div className="ml-2 text-gray-500 flex items-center">
            <div
              className="relative inline-block group"
              onClick={() =>
                setTipAndTrickContent({
                  title: 'Bullet points',
                  content:
                    'Include unique product features, product line details, and product specifications.\nDo not use all caps.\nHighlight key benefits and features in a natural, engaging way.',
                })
              }
            >
              {info}
              <Tooltip className="bottom-1/2">
                Key features and highlights presented in short statements to
                assist customers quickly
              </Tooltip>
            </div>
          </div>
        </div>
        <DndProvider backend={HTML5Backend}>
          {bullets.map((bullet, idx) => (
            <DraggableBullet
              key={idx}
              idx={idx}
              bullet={bullet}
              moveBullet={moveBullet}
              onChange={handleBulletChange}
              onDelete={deleteBullet}
            />
          ))}
        </DndProvider>
        {bullets.length < 10 && (
          <button
            type="button"
            className="text-blue-700 font-medium flex items-center gap-2 mt-2"
            onClick={addBullet}
          >
            <span className="text-xl leading-none">{addMore}</span> Add more
          </button>
        )}
      </div>
    </form>
  );
};

export default ListingContentForm;
