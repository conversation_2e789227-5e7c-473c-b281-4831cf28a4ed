import Input from '@/components/Input';
import RadioInput from '@/components/RadioInput';
import DynamicSelectArray from './components/DynamicSelectArray';
import EnumFieldInput from './components/EnumFieldInput';
import { ToolTipContainer } from './components/ToolTipContainer';

export function getFieldType(schema: any) {
  if (schema.enum || schema.enumNames) return 'enum';
  if (schema.anyOf || schema.oneOf) {
    const found = extractEnumFromAnyOf(schema);
    if (found) return 'enum';
    return 'string';
  }
  if (schema.type === 'boolean') return 'boolean';
  if (schema.type === 'number' || schema.type === 'integer') return 'number';
  if (schema.type === 'string') return 'string';
  if (schema.type === 'array') return 'array';
  if (schema.type === 'object') return 'object';
  return 'string';
}

export function isFieldRequired(schema: any, parentSchema?: any) {
  // Check if the field has required property set to true
  if (schema.required === true) return true;

  // Check if the field has required property as a boolean
  if (typeof schema.required === 'boolean') return schema.required;

  // For array fields, check if the nested value schema is required
  if (
    schema.items &&
    schema.items.value &&
    schema.items.value.required === true
  )
    return true;

  // Check parent schema if this is a nested field (like items.value)
  if (parentSchema && parentSchema.required === true) return true;

  return false;
}

export function extractEnumFromAnyOf(schema: any) {
  // Look for enum/enumNames in anyOf/oneOf
  const arr = schema.anyOf || schema.oneOf;
  if (!arr || !Array.isArray(arr)) return null;

  // First, try to find an entry that has both enum and enumNames
  for (const entry of arr) {
    if (entry.enum && entry.enumNames) {
      return {
        enum: entry.enum,
        enumNames: entry.enumNames,
      };
    }
  }

  // If no entry has both, look for one with just enum
  for (const entry of arr) {
    if (entry.enum) {
      return {
        enum: entry.enum,
        enumNames: entry.enumNames || null,
      };
    }
  }

  return null;
}

export function getEnumOptions(schema: any) {
  if (schema.enumNames && schema.enum) {
    return schema.enum.map((value: string, idx: number) => ({
      value,
      label: schema.enumNames[idx] || value,
    }));
  }
  if (schema.enum) {
    return schema.enum.map((value: string) => ({
      value,
      label: value,
    }));
  }
  // Check anyOf/oneOf for enum
  const found = extractEnumFromAnyOf(schema);
  if (found && found.enum) {
    return found.enum.map((value: string, idx: number) => ({
      value,
      label: found.enumNames ? found.enumNames[idx] || value : value,
    }));
  }
  return [];
}

// Restore helpers for nested state
export function getValueByPath(obj: any, path: string[]) {
  return path.reduce(
    (acc, key) => (acc && acc[key] !== undefined ? acc[key] : ''),
    obj
  );
}

export function setValueByPath(obj: any, path: string[], value: any) {
  if (path.length === 0) return value;
  const [first, ...rest] = path;
  return {
    ...obj,
    [first]:
      rest.length === 0 ? value : setValueByPath(obj[first] || {}, rest, value),
  };
}

// Helper to decide if a field should be shown
export function shouldShowField(schema: any, attributeFilter: string) {
  if (attributeFilter === 'required') return schema.required === true;
  if (attributeFilter === 'recommended') return schema.recommended === true;
  return true;
}

// Check if anyOf allows free text input (any schema with anyOf allows free input)
export function allowsFreeTextInput(schema: any) {
  // If schema contains anyOf or oneOf, allow free text input
  return !!(schema.anyOf || schema.oneOf);
}

// Get array constraints for dynamic select boxes
export function getArrayConstraints(schema: any) {
  return {
    minItems: schema.minItems || 0,
    maxItems: schema.maxItems || Infinity,
    minUniqueItems: schema.minUniqueItems || 0,
    maxUniqueItems: schema.maxUniqueItems || Infinity,
    allowMultiple: (schema.maxUniqueItems || Infinity) > 1,
  };
}

// Check if more select boxes can be added
export function canAddMoreSelects(currentCount: number, schema: any) {
  const constraints = getArrayConstraints(schema);
  return currentCount < constraints.maxUniqueItems;
}

// Check if select boxes can be removed
export function canRemoveSelects(currentCount: number, schema: any) {
  const constraints = getArrayConstraints(schema);
  return currentCount > constraints.minUniqueItems;
}

// Get the field type for array items
export function getArrayItemFieldType(schema: any) {
  if (!schema.items) return 'string';

  const itemSchema = schema.items.value || schema.items;

  // Check if it's a select with free text input
  if (allowsFreeTextInput(itemSchema)) {
    return 'select-with-input';
  }

  // Check if it's a regular enum select
  if (
    itemSchema.enum ||
    itemSchema.enumNames ||
    extractEnumFromAnyOf(itemSchema)
  ) {
    return 'select';
  }

  return getFieldType(itemSchema);
}

// Helper to recursively extract the underlying primitive value from arbitrarily nested objects
export function extractPrimitive(val: any) {
  if (Array.isArray(val)) return val;
  if (val && typeof val === 'object') {
    // If the object directly contains `value`, drill deeper
    if ('value' in val) return extractPrimitive(val.value);
    // If the object has exactly one key, keep drilling
    const keys = Object.keys(val);
    if (keys.length === 1) return extractPrimitive(val[keys[0]]);
  }
  // If it's a number and is an integer, return as integer
  if (typeof val === 'number' && Number.isInteger(val)) return val;
  // Primitive (string/number/boolean/etc.) or unexpected shape
  return val;
}

export function formatField(raw: any) {
  // First, collapse any nested `{ value: { value: ... } }` structures
  const value = extractPrimitive(raw);

  // If the value is already an array (e.g., multi-select fields), keep as-is
  if (Array.isArray(value)) {
    return value;
  }

  // Otherwise wrap in the expected object with language tag
  return [
    {
      value: value ?? '',
    },
  ];
}

export function renderField(
  key: string,
  schema: any,
  path: string[] = [],
  handleChange: (path: string[], value: any) => void,
  formRef: React.MutableRefObject<Record<string, any>>,
  parentSchema?: any,
  customValueGetter?: (key: string) => any
) {
  if (schema.hidden) return null;

  const uniqueKey = [...path, key].join('.');

  const fieldType = getFieldType(schema);

  // Helper function to get field value (either from custom getter or default path)
  const getFieldValue = (fieldPath: string[]) => {
    if (customValueGetter && fieldPath.length > 0) {
      // For nested paths like [...path, key, subKey], extract the subKey
      const pathSegments = fieldPath.slice(path.length + 1);
      const subKey = pathSegments[0];
      return customValueGetter(subKey);
    }
    return getValueByPath(formRef.current, fieldPath);
  };

  // For arrays with multiple unique items (like occasion_type)
  if (
    fieldType === 'array' &&
    schema.items &&
    (schema.maxUniqueItems > 1 || schema.maxUniqueItems === undefined) &&
    (schema.items.anyOf ||
      schema.items.enumNames ||
      schema.items.enum ||
      (schema.items.value &&
        (schema.items.value.anyOf ||
          schema.items.value.enumNames ||
          schema.items.value.enum)))
  ) {
    return (
      <div key={uniqueKey} className="mb-4">
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <div className="relative w-full">
          <DynamicSelectArray
            schema={schema}
            value={getFieldValue([...path, key]) || []}
            onChange={(value) => handleChange([...path, key], value)}
            title={schema.title || key}
          />
        </div>
      </div>
    );
  }

  // For color map or similar: array of enumNames with maxItems: 1
  if (
    fieldType === 'array' &&
    schema.items &&
    (schema.items.anyOf ||
      schema.items.enumNames ||
      schema.items.enum ||
      (schema.items.value &&
        (schema.items.value.anyOf ||
          schema.items.value.enumNames ||
          schema.items.value.enum))) &&
    (schema.maxItems === 1 || schema.maxUniqueItems === 1)
  ) {
    // Check if the actual schema is nested under items.value
    const itemSchema = schema.items.value || schema.items;
    const options = getEnumOptions(itemSchema);
    const allowsFreeText = allowsFreeTextInput(itemSchema);

    return (
      <div key={uniqueKey} className="mb-4">
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <div className="relative w-full">
          <EnumFieldInput
            fieldKey={key}
            title={schema.title || key}
            value={getFieldValue([...path, key]) || []}
            options={options}
            allowsFreeText={allowsFreeText}
            onChange={(value) => handleChange([...path, key], value)}
            maxLength={itemSchema.maxLength}
            minLength={itemSchema.minLength}
          />
        </div>
      </div>
    );
  }
  // For array of objects (e.g., { value: { ... } })
  if (fieldType === 'array' && schema.items) {
    // If value is an object with multiple keys, render as a group/section
    if (typeof schema.items === 'object' && !Array.isArray(schema.items)) {
      const entries = Object.entries(schema.items);

      // For closure field or other nested array structures, always use group structure
      const isNestedArray =
        schema.items.type === 'array' &&
        schema.items.items &&
        typeof schema.items.items === 'object';

      const isSingleProperty = entries.length === 1 && schema.items.value;

      // Only use simple field rendering for truly simple fields
      if (isSingleProperty && !isNestedArray) {
        const [subKey, subSchema] = entries[0];
        return renderField(
          subKey,
          subSchema,
          [...path, key, subKey],
          handleChange,
          formRef,
          schema
        );
      }
      // Multiple subfields: render group title and each subfield
      // Create a special handler for grouped fields
      const handleGroupedChange = (fieldPath: string[], value: any) => {
        // Extract the subfield key from the path
        const pathSegments = fieldPath.slice(path.length + 1); // Remove base path
        const subKey = pathSegments[0]; // Get the immediate subfield key (size, type, etc.)
        const propertyKey = pathSegments[1]; // Get the property key (value, unit, etc.) for complex fields

        // Get current group data or initialize as empty object
        const currentGroupData = getValueByPath(formRef.current, [
          ...path,
          key,
        ]) || [{}];
        const groupObject = currentGroupData[0] || {};

        // Check the subfield schema to determine how to store the value
        const subSchema = schema.items[subKey];

        let updatedSubfieldValue;

        // Handle nested array structure (like closure.type)
        if (
          subSchema?.type === 'array' &&
          subSchema.items &&
          typeof subSchema.items === 'object' &&
          !Array.isArray(subSchema.items)
        ) {
          // For nested array structures, keep the array of objects format
          updatedSubfieldValue = Array.isArray(value)
            ? value.map((v: any) => ({
                ...(typeof v === 'object' ? v : { value: v }),
                language_tag: 'en_US',
              }))
            : [];
        }
        // If it's a simple array field with only 'value' property, extract the string value
        else if (
          subSchema?.items?.value &&
          Object.keys(subSchema.items).length === 1 &&
          Array.isArray(value) &&
          value.length > 0 &&
          typeof value[0] === 'object' &&
          value[0].value
        ) {
          updatedSubfieldValue = value[0].value;
        }
        // If it's a complex field with multiple properties (like value + unit)
        else if (subSchema?.items && Object.keys(subSchema.items).length > 1) {
          // For complex fields with value + unit structure, store as a single object
          const currentSubfieldValue = groupObject[subKey];

          // Always ensure we have an object structure for complex fields
          let existingObject = {};
          if (
            currentSubfieldValue &&
            typeof currentSubfieldValue === 'object' &&
            !Array.isArray(currentSubfieldValue)
          ) {
            existingObject = { ...currentSubfieldValue };
          }

          // Merge the new value with existing object
          if (typeof value === 'object' && !Array.isArray(value)) {
            updatedSubfieldValue = {
              ...existingObject,
              ...value,
            };
          } else if (Array.isArray(value) && value.length > 0) {
            // Handle case where value comes as array but should be stored as object
            updatedSubfieldValue = {
              ...existingObject,
              ...value[0],
            };
          } else {
            // For simple values, we need to determine which property this should be
            // Check the subSchema to see what properties are available
            const subSchemaItems = subSchema.items;
            let targetProperty = 'value'; // default

            // If we have a propertyKey and it exists in the schema, use it
            if (propertyKey && subSchemaItems[propertyKey]) {
              targetProperty = propertyKey;
            } else {
              // Try to determine which property this should be based on the schema
              const availableProperties = Object.keys(subSchemaItems);
              if (
                availableProperties.length === 2 &&
                availableProperties.includes('value') &&
                availableProperties.includes('unit')
              ) {
                // This is likely a value/unit pair - determine which one based on the value type
                if (typeof value === 'number' || !isNaN(Number(value))) {
                  targetProperty = 'value';
                } else {
                  targetProperty = 'unit';
                }
              }
            }

            updatedSubfieldValue = {
              ...existingObject,
              [targetProperty]: value,
            };
          }
        } else {
          updatedSubfieldValue = value;
        }

        // Update the specific subfield in the group object
        const updatedGroupObject = {
          ...groupObject,
          [subKey]: updatedSubfieldValue,
        };

        // Update the form with the new grouped data
        handleChange([...path, key], [updatedGroupObject]);
      };

      // Custom value getter for grouped fields
      const getGroupedValue = (subKey: string) => {
        const groupData = getValueByPath(formRef.current, [...path, key]);
        if (Array.isArray(groupData) && groupData.length > 0) {
          const storedValue = groupData[0][subKey];
          const subSchema = schema.items[subKey];

          // Handle nested array structure (like closure.type)
          if (
            subSchema?.type === 'array' &&
            subSchema.items &&
            typeof subSchema.items === 'object' &&
            !Array.isArray(subSchema.items)
          ) {
            if (Array.isArray(storedValue)) {
              return storedValue.map((item: any) => {
                if (typeof item === 'object' && item !== null) {
                  return { ...item };
                }
                return { value: item };
              });
            }
            return [];
          }
          // If it's a simple array field with only 'value' property, wrap the string in array format
          else if (
            subSchema?.items?.value &&
            Object.keys(subSchema.items).length === 1 &&
            typeof storedValue === 'string'
          ) {
            return [{ value: storedValue }];
          }
          // If it's a complex field with multiple properties (like value + unit), return as object
          else if (
            subSchema?.items &&
            Object.keys(subSchema.items).length > 1 &&
            storedValue &&
            typeof storedValue === 'object' &&
            !Array.isArray(storedValue)
          ) {
            return storedValue;
          }

          // For complex fields or already properly formatted data, return as-is
          return storedValue;
        }
        return undefined;
      };

      return (
        <div
          key={uniqueKey}
          className="border-[#E2E8F0] border-b flex py-4 gap-8"
        >
          <div className="w-40">
            <div className="flex items-center gap-2 mb-2">
              <div className="flex gap-2 items-center">
                <span className="font-medium">{schema.title || key}</span>
                {isFieldRequired(schema, parentSchema) && (
                  <span className="text-red-500">*</span>
                )}
                {schema.description && ToolTipContainer(schema.description)}
              </div>
            </div>
          </div>
          <div className="flex-1 flex-col gap-6 flex mb-4">
            {entries.map(([subKey, subSchema]: any) => (
              <div key={[...path, key, subKey].join('.')} className="mb-4">
                {renderField(
                  subKey,
                  subSchema,
                  [...path, key, subKey],
                  handleGroupedChange,
                  formRef,
                  schema,
                  getGroupedValue
                )}
              </div>
            ))}
          </div>
        </div>
      );
    }
    // If value is a single field
    if (schema.items.value) {
      return renderField(key, schema.items.value, path, handleChange, formRef);
    }
  }

  // For nested objects
  if (fieldType === 'object' && schema.items) {
    return Object.entries(schema.items).map(([subKey, subSchema]: any) =>
      renderField(subKey, subSchema, [...path, key], handleChange, formRef)
    );
  }

  if (fieldType === 'object' && !schema.items) {
    return (
      <div key={uniqueKey}>
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <Input
          name={key}
          defaultValue={getFieldValue([...path, key])}
          onChangeFunc={(e) => handleChange([...path, key], e.target.value)}
        />
      </div>
    );
  }

  // For enums
  if (fieldType === 'enum') {
    const options = getEnumOptions(schema);
    const allowsFreeText = allowsFreeTextInput(schema);

    if (options.length > 0) {
      return (
        <div key={uniqueKey} className="flex-1">
          <div className="flex gap-2 items-center">
            <label className="font-medium">{schema.title || key}</label>
            {isFieldRequired(schema, parentSchema) && (
              <span className="text-red-500">*</span>
            )}
            {schema.description && ToolTipContainer(schema.description)}
          </div>
          <div className="relative w-full">
            <EnumFieldInput
              fieldKey={key}
              title={schema.title || key}
              value={getFieldValue([...path, key]) || ''}
              options={options}
              allowsFreeText={allowsFreeText}
              onChange={(value) => handleChange([...path, key], value)}
              placeholder="Select..."
              maxLength={schema.maxLength}
              minLength={schema.minLength}
            />
          </div>
        </div>
      );
    }
  }

  // For booleans
  if (fieldType === 'boolean') {
    return (
      <div key={uniqueKey}>
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <RadioInput
          label="Yes"
          name={key}
          checked={getFieldValue([...path, key]) === true}
          onChangeFunc={() => handleChange([...path, key], true)}
        />
        <RadioInput
          label="No"
          name={key}
          checked={getFieldValue([...path, key]) === false}
          onChangeFunc={() => handleChange([...path, key], false)}
        />
      </div>
    );
  }

  // For numbers
  if (fieldType === 'number') {
    return (
      <div key={uniqueKey}>
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <Input
          name={key}
          type="number"
          defaultValue={getFieldValue([...path, key])}
          onChangeFunc={(e) => handleChange([...path, key], e.target.value)}
        />
      </div>
    );
  }

  // For strings
  if (fieldType === 'string') {
    // Check if this is a date or date-time field
    const isDateField =
      schema.format === 'date' ||
      schema.format === 'date-time' ||
      (schema.oneOf &&
        schema.oneOf.some(
          (item: any) => item.format === 'date' || item.format === 'date-time'
        ));
    return (
      <div key={uniqueKey}>
        <div className="flex gap-2 items-center">
          <label className="font-medium">{schema.title || key}</label>
          {isFieldRequired(schema, parentSchema) && (
            <span className="text-red-500">*</span>
          )}
          {schema.description && ToolTipContainer(schema.description)}
        </div>
        <Input
          name={key}
          type={isDateField ? 'date' : 'text'}
          defaultValue={getFieldValue([...path, key])}
          onChangeFunc={(e) => handleChange([...path, key], e.target.value)}
          maxLength={schema.maxLength}
          minLength={schema.minLength}
        />
      </div>
    );
  }

  return null;
}
