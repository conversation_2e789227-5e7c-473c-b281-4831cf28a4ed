'use client';

import Toggle from '@/components/Toggle';
import {
  aplusContent,
  browser,
  chrome,
  collapse,
  email,
  expand,
  productListing,
  stock,
} from '@/utils/icon';
import { useState } from 'react';
interface NotificationType {
  icon: React.ReactNode;
  title: string;
  allowNotification: boolean;
  isPush: boolean;
  isEmail: boolean;
}

const whatNotificationsMock: NotificationType[] = [
  {
    icon: productListing,
    title: 'Product updates',
    allowNotification: true,
    isPush: true,
    isEmail: true,
  },
  {
    icon: aplusContent,
    title: 'A+ content changes',
    allowNotification: true,
    isPush: true,
    isEmail: false,
  },
  {
    icon: stock,
    title: 'Stock changes',
    allowNotification: false,
    isPush: false,
    isEmail: false,
  },
];

const Notification = () => {
  const [selectedItem, setSelectedItem] = useState<NotificationType | null>(
    null
  );
  const [browserNotification, setBrowserNotification] = useState(true);
  const [emailNotification, setEmailNotification] = useState(true);

  const [whatNotifications, setWhatNotifications] = useState<
    NotificationType[]
  >(whatNotificationsMock);

  const getSubtitle = (notification: NotificationType) => {
    if (!notification.allowNotification) {
      return 'Off';
    }

    let subtitle = '';
    if (notification.isPush) {
      if (notification.isEmail) {
        subtitle = 'Push, Email';
      } else {
        subtitle = 'Push';
      }
    } else {
      if (notification.isEmail) {
        subtitle = 'Email';
      }
    }
    if (!notification.isPush && !notification.isEmail) {
      subtitle = 'Off';
    }
    return subtitle;
  };

  const selectItemHandler = (item: NotificationType | any) => {
    setSelectedItem(selectedItem?.title === item.title ? null : item);
  };

  const allowNotificationHandler = (item: NotificationType) => {
    setWhatNotifications(
      whatNotifications.map((notification) => {
        if (notification.title === item.title) {
          return {
            ...notification,
            allowNotification: !item.allowNotification,
          };
        }
        return notification;
      })
    );
  };

  const enablePushNotification = (item: NotificationType) => {
    setWhatNotifications(
      whatNotifications.map((notification) => {
        if (notification.title === item.title) {
          return {
            ...notification,
            isPush: !item.isPush,
          };
        }
        return notification;
      })
    );
  };

  const enableEmailNotification = (item: NotificationType) => {
    setWhatNotifications(
      whatNotifications.map((notification) => {
        if (notification.title === item.title) {
          return {
            ...notification,
            isEmail: !item.isEmail,
          };
        }
        return notification;
      })
    );
  };

  return (
    <div className="w-full max-w-4xl p-4">
      <h1 className="text-2xl font-semibold ">Notifications</h1>

      {/* What Notifications You Receive */}
      <div className="bg-white shadow-md rounded-xl p-6 mt-4">
        <h2 className="text-base font-semibold text-[#000000] mb-1">
          What Notifications You Receive
        </h2>
        <div className="mt-4">
          {whatNotifications.map((item, index) => (
            <div
              className={`grid grid-cols-12 border border-[#CBD5E1] ${
                index == 0
                  ? 'rounded-t-lg'
                  : index == whatNotifications.length - 1
                    ? 'rounded-b-lg'
                    : ''
              }`}
              key={index}
            >
              <div className="col-span-1 p-4 flex items-center justify-center">
                {item.icon}
              </div>
              <div className="col-span-10 py-4 flex flex-col">
                <p className="text-base font-semibold text-[#0F172A]">
                  {item.title}
                </p>
                <p className="text-sm font-normal text-[#0F172A]">
                  {getSubtitle(item)}
                </p>
              </div>
              <div
                className="col-span-1 p-4 flex items-center justify-center cursor-pointer"
                onClick={() => selectItemHandler(item)}
              >
                {selectedItem?.title === item.title ? collapse : expand}
              </div>
              {selectedItem?.title === item.title && (
                <div className="col-span-12 px-4">
                  <div className="flex justify-between items-center rounded-xl bg-[#F3F4F6] px-6 py-4 mb-4">
                    <h1>Allow notifications</h1>
                    <Toggle
                      checked={item.allowNotification}
                      togglechangeHandler={() => allowNotificationHandler(item)}
                    />
                  </div>
                  {item.allowNotification && (
                    <div className="mb-4">
                      <div className="flex justify-between items-center rounded-t-xl bg-[#F3F4F6] px-6 py-4">
                        <div className="flex gap-4 items-center">
                          {browser}
                          <h1>Push</h1>
                        </div>
                        <Toggle
                          checked={item.isPush}
                          togglechangeHandler={() =>
                            enablePushNotification(item)
                          }
                        />
                      </div>
                      <div className="flex justify-between items-center rounded-b-xl bg-[#F3F4F6] px-6 py-4">
                        <div className="flex gap-4 items-center">
                          {email}
                          <h1>Email</h1>
                        </div>
                        <Toggle
                          checked={item.isEmail}
                          togglechangeHandler={() =>
                            enableEmailNotification(item)
                          }
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Where You Receive Notifications */}
      <div className="bg-white shadow-md rounded-xl p-6 mt-4">
        <h2 className="text-base font-semibold text-[#000000] mb-1">
          Where You Receive Notifications
        </h2>

        <div className="mt-4">
          <div className="grid grid-cols-12 border border-[#CBD5E1] rounded-t-lg">
            <div className="col-span-1 p-4 flex items-center justify-center">
              {browser}
            </div>
            <div className="col-span-10 py-4 flex flex-col">
              <p className="text-base font-semibold text-[#0F172A]">Browser</p>
              <p className="text-sm font-normal text-[#0F172A]">
                Chrome push notification
              </p>
            </div>

            <div
              className="col-span-1 p-4 flex items-center justify-center cursor-pointer"
              onClick={() => selectItemHandler({ title: 'Browser' })}
            >
              {selectedItem?.title === 'Browser' ? collapse : expand}
            </div>
            {selectedItem?.title === 'Browser' && (
              <div className="col-span-12 px-4 mb-4">
                <div className="flex justify-between items-center rounded-xl bg-[#F3F4F6] px-6 py-4">
                  <div className="flex gap-4 items-center">
                    {chrome}
                    <h1>Get notifications in this browser</h1>
                  </div>
                  <Toggle
                    checked={browserNotification}
                    togglechangeHandler={() =>
                      setBrowserNotification(!browserNotification)
                    }
                  />
                </div>
              </div>
            )}
          </div>

          <div className="grid grid-cols-12 border border-[#CBD5E1] rounded-b-lg">
            <div className="col-span-1 p-4 flex items-center justify-center">
              {email}
            </div>
            <div className="col-span-10 py-4 flex flex-col">
              <p className="text-base font-semibold text-[#0F172A]">Email</p>
              <p className="text-sm font-normal text-[#0F172A]">
                <EMAIL>
              </p>
            </div>

            <div
              className="col-span-1 p-4 flex items-center justify-center cursor-pointer"
              onClick={() => selectItemHandler({ title: 'Email' })}
            >
              {selectedItem?.title === 'Email' ? collapse : expand}
            </div>
            {selectedItem?.title === 'Email' && (
              <div className="col-span-12 px-4 mb-4">
                <div className="flex justify-between items-center rounded-xl bg-[#F3F4F6] px-6 py-4">
                  <h1 className="text-sm font-normal text-[#0F172A]">
                    Your emails are sent to{' '}
                    <span className="font-semibold"><EMAIL></span>
                  </h1>
                  <Toggle
                    checked={emailNotification}
                    togglechangeHandler={() =>
                      setEmailNotification(!emailNotification)
                    }
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Notification;
