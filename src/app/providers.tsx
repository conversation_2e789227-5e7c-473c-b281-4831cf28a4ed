'use client'; // Ensure this runs on the client

import { AppProvider, Frame } from '@shopify/polaris';
import enTranslations from '@shopify/polaris/locales/en.json';
import { ReactNode } from 'react';
import { Provider } from 'react-redux';
import { store } from './store/store';

interface ProvidersProps {
  children: ReactNode;
}

const Providers = ({ children }: ProvidersProps) => {
  return (
    <Provider store={store}>
      <AppProvider i18n={enTranslations}>
        <Frame>{children}</Frame>
      </AppProvider>
    </Provider>
  );
};

export default Providers;
