'use client';
import { Button } from '@/components/Button';
import Input from '@/components/Input';
import { testProManHandler } from '@/utils/api';
import { downChevron, hamburger, proman } from '@/utils/icon';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import CountUp from 'react-countup';
import { toast, ToastContainer } from 'react-toastify';

export default function Home() {
  const [company, setCompany] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [country, setCountry] = useState<string>('');

  const submitHandler = async () => {
    try {
      const res = await testProManHandler(firstName, lastName, email);
      console.log(res);
      if (res.status === 201) {
        toast.success('Test account created! Please check your email.');
      } else {
        toast.error('Something went wrong. Please try again.');
      }
    } catch (err) {
      toast.error('Error while submitting form.');
      console.error(err);
    }
  };

  return (
    <div>
      <ToastContainer />
      <div className="relative">
        <input
          type="checkbox"
          id="menu-toggle"
          className="hidden peer absolute"
        />
        <div
          className="fixed inset-0 z-40 bg-[#0C3754]
          flex-col items-start px-6 pt-24 gap-6 text-white text-sm lg:hidden 
          transition-all duration-500 ease-out 
          opacity-0  pointer-events-none flex -translate-y-10
          peer-checked:opacity-100 peer-checked:translate-y-0 peer-checked:pointer-events-auto"
        >
          <span className="text-sm opacity-50">Menu</span>
          <Link
            href="#features"
            className="hover:underline text-base font-medium text-white"
          >
            Product
          </Link>
          <Link
            href="#footer"
            className="hover:underline text-base font-medium text-white"
          >
            Contact Us
          </Link>
        </div>
        <div className="fixed top-0 left-0 w-full z-50 bg-[#082F49] flex justify-between items-center sm:px-12 lg:px-32 py-4 px-4">
          <div className="flex flex-row items-center gap-12">
            <div className=" font-bold  "> {proman} </div>
            <div className="lg:flex  gap-12 hidden">
              <Link href="#features" className="hover:underline text-white">
                Product
              </Link>
            </div>
          </div>
          <div className="flex gap-10 lg:gap-12 items-center">
            <Link
              href="#features"
              className="hover:underline lg:flex hidden text-white"
            >
              Contact Us
            </Link>
            <button className="text-sm lg:text-base bg-transparent border font-semibold border-white rounded-sm py-3 px-4 uppercase text-white">
              Test For Free
            </button>
            <label
              htmlFor="menu-toggle"
              className="cursor-pointer text-white lg:hidden  transition-transform duration-75 active:scale-110"
            >
              {hamburger}
            </label>
          </div>
        </div>
        <section className="bg-[#082F49]  text-white flex justify-center ">
          <div className="flex px-10  lg:px-16 py-16 lg:py-20  flex-col sm:flex-row gap-10  sm:gap-0 max-w-[1280px] flex-1 justify-between  ">
            <div className="flex-1/2 gap-6 flex flex-col  justify-center items-center sm:items-stretch text-center sm:text-left ">
              <div className="flex flex-col gap-3">
                <p className="text-xs text-[#FACC15] font-semibold tracking-wide uppercase ">
                  Amazon Seller Software
                </p>
                <div className="text-2xl sm:text-3xl lg:text-4xl font-semibold leading-tight  sm:max-w-lg font-inter">
                  Optimize Product Listings. Boost Online Sales.
                </div>
              </div>
              <p className=" text-white max-w-xs text-base lg:text-xl">
                Grow your business faster, smarter, and with more confidence.
              </p>
              <Link
                href="/auth/login"
                className="w-fit px-6 py-3 bg-[#FACC15] text-[#0F172A] font-semibold rounded-sm hover:brightness-110 transition"
              >
                GET STARTED
              </Link>
            </div>
            <div className="flex-1/2 flex justify-center ">
              <div className="relative w-full max-w-lg h-96">
                <Image
                  src="/assets/landing/1.png"
                  alt="Landing banner 3"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </section>
      </div>
      <section className="bg-[#F5F5F5] text-center ">
        <div className="flex flex-col gap-14 px-10 py-12 lg:px-32 lg:py-20 text-center sm:text-left">
          <div className="flex flex-col gap-6 items-center">
            <p className="text-xs text-[#FACC15] font-semibold tracking-wide">
              WHY CHOOSE US?
            </p>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl text-[#0F172A] font-semibold leading-tight  ">
              Powerful, High-Converting Listings
            </h2>
            <p className=" lg:max-w-xl text-center max-w-xs text-base  text-[#0F172A] lg:text-xl">
              E-Commerce are complex and dynamic.
              <br />
              Smart optimization and innovation keep you ahead.
            </p>
          </div>
          <div className="flex justify-center gap-12  bg-[#F5F5F5] sm:flex-row flex-col">
            <div className="bg-[#082F49] lg:py-20 py-16 sm:py-14 px-8  lg:px-16 max-w-sm   rounded-xl text-white flex flex-col gap-4 justify-center items-center text-center  ">
              <div className="lg:text-6xl text-5xl  font-bold text-[#FACC15] max-w-sm">
                <CountUp end={3200} duration={2} />+
              </div>
              <p className="lg:text-xl text-base max-w-sm lg:max-w-58 ">
                listings published through PROMAN in just 6 months.
              </p>
            </div>

            <div className="bg-[#082F49]  lg:py-20  py-16 sm:py-14 px-8  lg:px-16  max-w-sm  rounded-xl text-white flex flex-col gap-4 justify-center items-center text-center  ">
              <div className="lg:text-6xl text-5xl font-bold text-[#FACC15] max-w-sm">
                <CountUp end={75} duration={2} suffix="%" />
              </div>
              <p className="lg:text-xl text-base max-w-sm lg:max-w-58  ">
                time saving up compared to manual product uploads.
              </p>
            </div>
            <div className="bg-[#082F49] lg:py-20  py-16 sm:py-14 px-8  lg:px-16 max-w-sm   rounded-xl text-white flex flex-col gap-4 justify-center items-center text-center  ">
              <div className="lg:text-6xl text-5xl font-bold text-[#FACC15] max-w-sm">
                <CountUp end={80} duration={2} suffix="%" />
              </div>
              <p className="lg:text-xl text-base max-w-sm  lg:max-w-58 ">
                listings improved performance within 2 weeks of optimization.
              </p>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-white flex justify-center">
        <div className="flex px-10 sm:px-20 sm:gap-16 gap-6 lg:px-16 py-16 lg:py-20 sm:flex-row flex-col flex-1 max-w-[1280px] justify-between">
          <div className="flex-1/2 gap-7 flex flex-col  justify-center text-center items-center sm:items-stretch sm:text-left ">
            <div className="flex flex-col gap-3">
              <p className="text-xs text-[#FACC15] font-semibold tracking-wide uppercase ">
                Benefits of proman
              </p>
              <div className="text-2xl sm:text-3xl lg:text-4xl font-semibold leading-tight text-[#0F172A] sm:max-w-xs  max-w-3xs">
                Fast & Frictionless Product Listing
              </div>
            </div>
            <p className="  max-w-xs lg:max-w-md  text-base lg:text-xl text-[#0F172A] ">
              Just input your product info - ProMan takes care of the rest.
              Simply re-sync the updates directly into Seller Central.
            </p>
            <button className="w-fit px-6 py-3 bg-[#FACC15] text-[#0F172A] font-semibold rounded-sm hover:brightness-110 transition text-base uppercase">
              Get started
            </button>
          </div>
          <div className="flex-1/2 flex justify-center items-center ">
            <div className="relative w-full max-w-lg h-96">
              <Image
                src="/assets/landing/4.png"
                alt="Landing banner 2"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </section>
      <section className="bg-[#082F49]  text-white flex justify-center ">
        <div className="flex px-10  lg:px-16 sm:py-16 lg:py-20 sm:gap-16 gap-6 sm:flex-row flex-col-reverse py-14 flex-1 max-w-[1280px] justify-between">
          <div className="flex-1/2 flex  justify-center items-center ">
            <div className="relative w-full max-w-lg h-96">
              <Image
                src="/assets/landing/4.png"
                alt="Landing banner 3"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="flex-1/2 gap-6 flex flex-col  justify-center sm:px-16 items-center sm:items-stretch text-center sm:text-left ">
            <div className="flex flex-col gap-3">
              <p className="text-xs text-[#FACC15] font-semibold tracking-wide uppercase ">
                Benefits of proman
              </p>
              <div className="text-2xl lg:text-4xl font-semibold leading-tight sm:max-w-xs   text-white ">
                Optimize Your Listings Overtime
              </div>
            </div>
            <p className=" text-white lg:max-w-md  max-w-xs text-base lg:text-xl ">
              Check how likely your listing is to rank and convert sales with
              ProMan’s Listing Performance Score.
            </p>
            <button className="w-fit px-6 py-3 bg-[#FACC15] text-[#0F172A] font-semibold rounded-sm hover:brightness-110 transition">
              GET STARTED
            </button>
          </div>
        </div>
      </section>
      <section className="bg-white  flex justify-center">
        <div className="flex px-10  lg:px-16 sm:py-16 py-14 lg:py-20 sm:gap-16 gap-6  sm:flex-row flex-col flex-1 max-w-[1280px] justify-between">
          <div className="flex-1/2 gap-7 flex flex-col  justify-center items-center sm:items-stretch text-center sm:text-left">
            <div className="flex flex-col gap-3">
              <p className="text-xs text-[#FACC15] font-semibold tracking-wide uppercase ">
                Benefits of proman
              </p>
              <div className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#0F172A] leading-tight  max-w-xs">
                AI Assistant for Content Creation
              </div>
            </div>
            <p className=" lg:max-w-sm max-w-md text-base lg:text-xl text-[#0F172A]">
              Generate optimized product titles, bullet points, and descriptions
              in seconds.
            </p>
            <button className="w-fit px-6 py-3 bg-[#FACC15] text-[#0F172A] font-semibold rounded-sm hover:brightness-110 transition">
              GET STARTED
            </button>
          </div>
          <div className="flex-1/2 flex justify-center items-center ">
            <div className="relative w-full max-w-lg h-96">
              <Image
                src="/assets/landing/4.png"
                alt="Landing banner 4"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </section>
      <section className="bg-[#F5F5F5] flex justify-center">
        <div className="flex px-10  lg:px-16 pt-12  gap-16 sm:flex-row flex-col flex-1 max-w-[1280px] justify-between">
          <div className="flex-1/2 gap-7 flex flex-col  sm:items-stretch justify-center ">
            <div className="flex flex-col gap-3  ">
              <div className="text-2xl  lg:text-4xl  leading-relaxed  text-black max-w-xs lg:max-w-xl text-center sm:text-left font-inter ">
                “Before ProMan, listinging a product was the most frustrating
                part of selling. Now it’s the easiest. The AI suggestions? Spot
                on.”
              </div>
              <p className="text-xl text-[#0F172A] text-center sm:text-left ">
                Seller name | Business ABC
              </p>
            </div>
          </div>
          <div className="flex-1/2 flex   justify-center">
            <div className="relative w-full max-w-xl sm:h-[500px] h-[250px]">
              <Image
                src="/assets/landing/5.png"
                alt="Landing banner 5"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </section>
      <section className="bg-[#FACC15] flex justify-center">
        <div className="flex lg:flex-row flex-col  px-3 sm:px-10 lg:px-16 sm:py-10 py-10 lg:py-14 gap-16 flex-1 max-w-[1280px] justify-between">
          <div className="flex flex-col sm:gap-8 gap-4 flex-1/2 justify-center">
            <div className="text-2xl sm:text-4xl font-semibold leading-tight text-[#0F172A] max-w-md">
              Test ProMan Now!
            </div>
            <p className="text-base sm:text-xl text-[#0F172A]">
              Experience the full potential of our platform. <br />
              Free for <strong>30 days</strong>. No Fees. No Commitment.
            </p>
            <ul className="list-decimal list-inside text-[#0F172A] text-base sm:text-xl space-y-2 lg:max-w-sm">
              <li>Fill out the form</li>
              <li>Receive your test account via email</li>
              <li>Start exploring – test all features and discover ProMan</li>
            </ul>
            <p className="text-base sm:text-xl font-semibold text-[#0F172A] ">
              Start your free trial now!
            </p>
          </div>
          <form
            onSubmit={submitHandler}
            className="bg-[#FACC15] flex flex-col gap-4 justify-center flex-1/2"
          >
            <Input
              label="Company"
              value={company}
              placeholder="Enter your company name"
              onChangeFunc={(e) => setCompany(e.target.value)}
              inputClassName="text-black"
            />
            <div className="flex gap-4">
              <Input
                label="First name"
                value={firstName}
                placeholder="John"
                onChangeFunc={(e) => setFirstName(e.target.value)}
                className="w-1/2"
                inputClassName="text-black"
              />
              <Input
                label="Last name"
                value={lastName}
                placeholder="Doe"
                onChangeFunc={(e) => setLastName(e.target.value)}
                className="w-1/2"
                inputClassName="text-black"
              />
            </div>
            <div className="flex gap-4">
              <Input
                label="Email"
                value={email}
                placeholder="<EMAIL>"
                onChangeFunc={(e) => setEmail(e.target.value)}
                className="w-1/2"
                inputClassName="text-black"
              />
              <Input
                label="Phone number"
                value={phone}
                placeholder="Enter phone number"
                onChangeFunc={(e) => setPhone(e.target.value)}
                className="w-1/2"
                inputClassName="text-black"
              />
            </div>
            <div className="flex flex-col gap-1">
              <label className="text-black font-semibold">Country</label>
              <div className="relative w-full">
                <select
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                  className="w-full p-3 rounded-lg font-sans text-sm font-medium bg-white focus:outline-none border border-gray-200 focus:border-2 focus:border-[#EAB308] text-gray-500 appearance-none"
                >
                  <option value="" disabled>
                    Choose your country
                  </option>
                  <option value="Vietnam">Vietnam</option>
                  <option value="United States">United States</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Japan">Japan</option>
                </select>
                <div className="absolute right-2 top-1/2">{downChevron}</div>
              </div>
            </div>
            <Button
              isValid
              className="uppercase !bg-[#082F49] !mt-0 !rounded-md"
              onClickFunc={submitHandler}
            >
              <span className="!text-white">submit</span>
            </Button>
          </form>
        </div>
      </section>
      <section className="bg-[#082F49] text-center text-white">
        © 2025 ProMan, All rights reserved.
      </section>
    </div>
  );
}
