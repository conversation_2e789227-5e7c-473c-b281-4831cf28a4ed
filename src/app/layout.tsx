import { GoogleAnalytics } from '@next/third-parties/google';
import { Analytics } from '@vercel/analytics/next';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Metadata } from 'next';
import { SessionProvider } from 'next-auth/react';
import { Geist, Geist_Mono, Inter } from 'next/font/google';
import { Suspense } from 'react';
import './globals.css';
import Loading from './loading';
import Providers from './providers';

export const metadata: Metadata = {
  title: 'e-proMan',
  description: 'e-proMan System',
  icons: {
    icon: '/logo.svg',
  },
};

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased`}
      >
        <SessionProvider>
          <Suspense fallback={<Loading />}>
            <Providers>{children}</Providers>
          </Suspense>
        </SessionProvider>
        <Analytics />
        <SpeedInsights />
        <GoogleAnalytics gaId="G-8QEGNMVV23" />
      </body>
    </html>
  );
}
