import LanguagePicker from '@/components/LanguagePicker';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="h-screen w-screen text-gray-900">
      <div className="h-full m-0 bg-white flex justify-center items-center">
        {/* <div className="bg-[#F8FAFC] bg-contain bg-center bg-no-repeat rounded-[24px]">
          <Image
            width={400}
            height={357.53}
            src="../assets/bg-login.svg"
            alt=""
          ></Image>
        </div> */}
        <LanguagePicker />
        {children}
      </div>
    </div>
  );
}
