'use client';
import { Button } from '@/components/Button';
import PasswordValidation from '@/components/PasswordValidation';
import { resetPassword } from '@/utils/api';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useState } from 'react';
import Input from '../../../components/Input';
import { forwardArrow } from '../../../utils/icon';
export default function CreatePassword() {
  const CreatePasswordContent = () => {
    const router = useRouter();
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [isAlreadyRegistered, setIsAlreadyRegistered] = useState(false);

    const searchParams = useSearchParams();
    const key = Array.from(searchParams.keys())[0];

    const [validation, setValidation] = useState({
      length: false,
      uppercase: false,
      lowercase: false,
      noSpace: false,
      specialChar: false,
    });

    const passwordChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = e.target;
      setPassword(value);
      setValidation({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        lowercase: /[a-z]/.test(value),
        noSpace: !/\s/.test(value) && value !== '',
        specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value),
      });
    };

    const confirmPasswordChangeHandler = (
      e: React.ChangeEvent<HTMLInputElement>
    ) => {
      setError('');
      setConfirmPassword(e.target.value);
    };

    const isPasswordValid = () => {
      return Object.values(validation).every(Boolean);
    };

    const continueHandler = async () => {
      if (password !== confirmPassword) {
        setError('Password does not match.');
      } else if (isPasswordValid()) {
        resetPassword({ key, password })
          .then((response) => {
            console.log(response);
          })
          .catch((error) => {
            if (error.response.status === 401) {
              router.push('/auth/reset-password?status=success');
            }
          });
      }
    };

    const continueCondition = () => {
      return confirmPassword.length > 0 && isPasswordValid();
    };

    return (
      <>
        <div>
          <div className="flex flex-col items-center">
            <div className="w-full flex-1 ">
              <div className="mx-auto max-w-xs">
                <Image
                  width={54}
                  height={36}
                  src="../assets/logo.svg"
                  alt="logo"
                ></Image>
                <h1 className="text-2xl xl:text-3xl font-semibold whitespace-nowrap text-nowrap">
                  Create new password
                </h1>
              </div>
            </div>
            <div className="w-full flex-1 mt-8">
              <div className="mx-auto max-w-xs">
                <Input
                  value={password}
                  onChangeFunc={passwordChangeHandler}
                  label="New Password"
                  type="password"
                />

                <Input
                  className={`mt-5 ${error && '!border-[#EF4444]'}`}
                  value={confirmPassword}
                  onChangeFunc={confirmPasswordChangeHandler}
                  label="Confirm Password"
                  type="password"
                />

                {error && <p className="text-red-700 text-sm mt-2">{error}</p>}

                <PasswordValidation validation={validation} />

                <Button
                  isValid={continueCondition()}
                  onClickFunc={continueHandler}
                >
                  Continue
                </Button>
              </div>
            </div>
          </div>
          <div>
            <p className="flex items-center justify-center mt-6 text-xs text-gray-600 text-center gap-2">
              New to APM?
              <Link
                href="/auth/login"
                className="text-[#1E40AF] text-sm font-medium flex justify-center items-center gap-2"
              >
                Back to log in {forwardArrow}
              </Link>
            </p>
          </div>
        </div>
        {isAlreadyRegistered && (
          <div
            className="relative z-10"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
          >
            <div
              className="fixed inset-0 bg-gray-500/75 transition-opacity"
              aria-hidden="true"
            ></div>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl p-6">
                  <div
                    className="flex justify-end cursor-pointer"
                    onClick={() => setIsAlreadyRegistered(false)}
                  >
                    <svg
                      width="22"
                      height="22"
                      viewBox="0 0 22 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11C20 6.02944 15.9706 2 11 2ZM0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 17.0751 17.0751 22 11 22C4.92487 22 0 17.0751 0 11ZM7.29289 7.29289C7.68342 6.90237 8.31658 6.90237 8.70711 7.29289L11 9.58579L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L12.4142 11L14.7071 13.2929C15.0976 13.6834 15.0976 14.3166 14.7071 14.7071C14.3166 15.0976 13.6834 15.0976 13.2929 14.7071L11 12.4142L8.70711 14.7071C8.31658 15.0976 7.68342 15.0976 7.29289 14.7071C6.90237 14.3166 6.90237 13.6834 7.29289 13.2929L9.58579 11L7.29289 8.70711C6.90237 8.31658 6.90237 7.68342 7.29289 7.29289Z"
                        fill="#030712"
                      />
                    </svg>
                  </div>
                  <div className="bg-white">
                    <div className="sm:flex sm:items-start">
                      <div className="mt-3 text-center sm:mt-0 sm:text-left">
                        <p
                          className="text-lg font-sans font-medium text-[#0F172A]"
                          id="modal-title"
                        >
                          This email account has already been registered.
                        </p>
                        <div className="mt-4">
                          <p className="text-sm font-sans font-normal text-[#1E293B]">
                            Please log in or use a different email account.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="sm:flex sm:flex-row-reverse mt-6">
                    <button
                      onClick={() => {
                        setIsAlreadyRegistered(false);
                      }}
                      type="button"
                      className="inline-flex w-full justify-center rounded-lg bg-[#FACC15] px-6 py-2 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
                    >
                      Try again
                    </button>
                    <button
                      onClick={() => {
                        setIsAlreadyRegistered(false);
                      }}
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-lg bg-white px-6 py-2 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreatePasswordContent />
    </Suspense>
  );
}
