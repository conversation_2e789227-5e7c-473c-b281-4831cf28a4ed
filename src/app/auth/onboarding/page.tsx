'use client';
import { Button } from '@/components/Button';
import AuthenticatorSetup from '@/components/modal/AuthenticatorSetup';
import RecoveryPhoneSetup from '@/components/modal/RecoveryPhoneSetup';
import Toggle from '@/components/Toggle';
import { fetchUser, get2FAQRCode } from '@/utils/api';
import {
  back,
  complete,
  phone,
  qr,
  rightChevron,
  success,
  warning,
} from '@/utils/icon';
import {
  benefits,
  onboardingCategories,
  profiles,
  regions,
} from '@/utils/mock';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import OnboardingWrapper from './components/OnboardingWrapper';
import SelectCategories from './components/SelectCategories';
import SelectProfile from './components/SelectProfile';
import SelectRegions from './components/SelectRegions';

interface User {
  email: string;
  display_name: string | null;
  phone_number: string | null;
  address: string | null;
  two_fa_enabled_date: string | null;
  last_password_changed: string | null;
  date_joined: string | null;
}

const Onboarding = () => {
  const [selectedProfile, setSelectedProfile] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [twoStepEnabled, setTwoStepEnabled] = useState<boolean>(false);
  const [completeAuth, setCompleteAuth] = useState<boolean>(false);
  const [completePhone, setCompletePhone] = useState<boolean>(false);
  const [showModalPhone, setShowModalPhone] = useState<boolean>(false);
  const [showModalAuth, setShowModalAuth] = useState<boolean>(false);
  const [QRCodeURL, setQRCodeURL] = useState('');
  const [step, setStep] = useState<
    'profile' | 'categories' | 'benefits' | 'choose' | '2-step'
  >('profile');
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function get2FAQRCodeHandler() {
      try {
        await get2FAQRCode();
      } catch (error) {
        const response = error.response.data;
        setQRCodeURL(response.meta.totp_url);
      }
    }

    const fetchAccount = async () => {
      const res = await fetchUser();
      if (res && res.data) {
        const userData = res.data;
        setUser(userData);
        if (userData.two_fa_enabled_date) {
          setCompleteAuth(true);
        }
        if (userData.phone_number) {
          setCompletePhone(true);
        }
      }
    };

    get2FAQRCodeHandler();
    fetchAccount();
  }, []);

  const profileToggler = (option: string) => {
    if (selectedProfile.includes(option)) {
      setSelectedProfile(selectedProfile.filter((o) => o !== option));
    } else {
      setSelectedProfile([...selectedProfile, option]);
    }
  };

  const categoryToggler = (option: string) => {
    if (selectedCategories.includes(option)) {
      setSelectedCategories(selectedCategories.filter((o) => o !== option));
    } else {
      setSelectedCategories([...selectedCategories, option]);
    }
  };

  const regionToggler = (id: string) => {
    setSelectedRegion(id);
  };

  const submitProfileHandler = () => {
    setStep('categories');
  };
  const submitCategoriesHandler = () => {
    setStep('benefits');
  };

  const submitBenefitsHandler = () => {
    setStep('choose');
  };

  const submitRegionHandler = () => {
    window.location.href = 'https://sellercentral.amazon.com/signin';
  };

  const submitTwoStepHandler = () => {
    router.push('/home');
  };

  return (
    <div className="bg-[#F3F4F6] min-h-screen flex flex-col items-center justify-center w-[100vw] ">
      {step === 'profile' && (
        <OnboardingWrapper
          title="Which one best describes you?"
          description="We’ll help you get set up based on your business needs."
          setStep={setStep}
        >
          <div className="bg-white rounded-2xl px-4 py-5 shadow-lg flex flex-col items-center gap-6">
            <SelectProfile
              options={profiles}
              selected={selectedProfile}
              onToggle={profileToggler}
            />
            <Button
              isValid={selectedProfile.length > 0}
              onClickFunc={submitProfileHandler}
              className="!mt-0"
            >
              Continue
            </Button>
          </div>
        </OnboardingWrapper>
      )}
      {step === 'categories' && (
        <OnboardingWrapper
          title="What kinds of product are you gonna sell?"
          description="Pick categories you want to start with."
          backStep="profile"
          backIcon={back}
          setStep={setStep}
        >
          <div className="bg-white rounded-2xl px-4 py-5 shadow-lg flex flex-col items-center gap-6">
            <SelectCategories
              options={onboardingCategories}
              selected={selectedCategories}
              onToggle={categoryToggler}
            />
            <Button
              isValid={selectedCategories.length > 0}
              onClickFunc={submitCategoriesHandler}
              className="!mt-0"
            >
              Continue
            </Button>
          </div>
        </OnboardingWrapper>
      )}
      {step === 'benefits' && (
        <OnboardingWrapper
          title="Connect to Amazon Seller Central"
          description="Once connected, you'll be able to:"
          backStep="categories"
          backIcon={back}
          setStep={setStep}
        >
          <div className="bg-white rounded-2xl px-4 py-5 shadow-lg flex flex-col gap-4">
            <div className="bg-[#F3F4F6] flex flex-col gap-6 p-3 rounded-xl">
              {benefits.map((b, index) => (
                <div className="flex items-center gap-3" key={index}>
                  {success}
                  <div>
                    <p className="font-semibold text-[#0F172A]">{b.title}</p>
                    <p className="text-sm text-[#64748B]">{b.description}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button onClickFunc={submitBenefitsHandler} className="!mt-0">
              Continue
            </Button>
            <p
              className="text-center text-sm text-[#0F172A] cursor-pointer font-medium"
              onClick={() => setStep('2-step')}
            >
              I don’t have seller account
            </p>
          </div>
        </OnboardingWrapper>
      )}
      {step === 'choose' && (
        <OnboardingWrapper
          title="Connect to Amazon Seller Central"
          description="Once connected, you'll be able to:"
          backStep="benefits"
          backIcon={back}
          setStep={setStep}
        >
          <div className="bg-white rounded-2xl px-4 py-5 shadow-lg flex flex-col items-center gap-6">
            <SelectRegions
              options={regions}
              selected={selectedRegion}
              onToggle={regionToggler}
            />
            <Button
              isValid={!!selectedRegion}
              onClickFunc={submitRegionHandler}
              className="!mt-0"
            >
              Continue
            </Button>
          </div>
        </OnboardingWrapper>
      )}
      {step === '2-step' && (
        <OnboardingWrapper
          title="Enable 2-Step Authentication"
          description="Protect your account with additional layer of security"
          backStep="benefits"
          backIcon={back}
          setStep={setStep}
        >
          <div className="bg-white rounded-2xl px-4 py-5 shadow-lg flex flex-col items-center gap-4">
            <div className="bg-[#F3F4F6] flex flex-col gap-6 p-3 rounded-xl ">
              <div className="flex items-center justify-between gap-3">
                <div className="flex flex-col gap-2">
                  <p className="font-semibold text-[#0F172A] ">
                    Better security for your business
                  </p>
                  <p className="text-sm text-[#64748B]">
                    Prevent hackers from accessing your account with an
                    additional layer of security.
                  </p>
                </div>
                <Toggle
                  checked={twoStepEnabled}
                  togglechangeHandler={() => {}}
                />
              </div>
            </div>
            {twoStepEnabled && (
              <>
                <div className="flex flex-col divide-y divide-[#CBD5E1] rounded-xl border border-[#CBD5E1] overflow-hidden w-full">
                  <div className="flex items-center justify-between px-4 py-3">
                    <div className="flex items-center gap-2">
                      <span>{qr}</span>
                      <span className="text-sm  text-[#0F172A]">
                        Authenticator
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>{completeAuth ? complete : warning} </span>
                      <button
                        onClick={() => setShowModalAuth(true)}
                        className="text-[#0F172A] text-sm flex items-center gap-2"
                      >
                        {completeAuth
                          ? `Added just now`
                          : `Add authenticator app `}{' '}
                        {rightChevron}
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between px-4 py-3">
                    <div className="flex items-center gap-2">
                      <span>{phone}</span>
                      <span className="text-sm  text-[#0F172A]">
                        Phone number
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>{completePhone ? complete : warning} </span>
                      <button
                        className="text-[#0F172A] text-sm flex items-center gap-6.5"
                        onClick={() => setShowModalPhone(true)}
                      >
                        Add phone number {rightChevron}
                      </button>
                    </div>
                  </div>
                </div>
                <Button
                  isValid={completePhone && completeAuth}
                  onClickFunc={submitTwoStepHandler}
                  className="!mt-0"
                >
                  Done
                </Button>
              </>
            )}
            {!twoStepEnabled && (
              <Button
                isValid={selectedProfile.length > 0}
                onClickFunc={() => setTwoStepEnabled(true)}
                className="!mt-0"
              >
                Turn on 2-Step Authentication
              </Button>
            )}
            <p
              className="text-center text-sm text-[#0F172A]  cursor-pointer font-medium"
              onClick={() => router.push('/home')}
            >
              I&apos;ll set up later
            </p>
          </div>
        </OnboardingWrapper>
      )}
      {showModalAuth && (
        <AuthenticatorSetup
          onClose={() => setShowModalAuth(false)}
          onConfirm={() => {
            setCompleteAuth(true);
            setShowModalAuth(false);
          }}
          // update
          QRCodeURL={QRCodeURL}
        />
      )}
      {showModalPhone && (
        <RecoveryPhoneSetup
          onClose={() => setShowModalPhone(false)}
          onConfirm={() => {
            setCompletePhone(true);
            setShowModalPhone(false);
          }}
        />
      )}
    </div>
  );
};

export default Onboarding;
