interface SelectCategoriesProps {
  options: string[];
  selected: string[];
  onToggle: (value: string) => void;
}

const SelectCategories = ({
  options,
  selected,
  onToggle,
}: SelectCategoriesProps) => {
  return (
    <div className="flex flex-wrap justify-center gap-2">
      {options.map((option) => (
        <button
          key={option}
          onClick={() => onToggle(option)}
          className={`px-4 py-2 rounded-full border transition text-sm font-medium ${
            selected.includes(option)
              ? 'bg-[#1E293B] text-white border-transparent'
              : 'bg-white text-[#0F172A] border-[#CBD5E1]'
          }`}
        >
          {option}
        </button>
      ))}
    </div>
  );
};

export default SelectCategories;
