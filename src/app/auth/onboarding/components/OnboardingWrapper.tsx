import { useRouter } from 'next/navigation';

interface OnboardingProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  backStep?: 'profile' | 'categories' | 'benefits' | 'choose' | '2-step';
  backIcon?: React.ReactNode;
  setStep?: (
    step: 'profile' | 'categories' | 'benefits' | 'choose' | '2-step'
  ) => void;
}

const OnboardingWrapper = ({
  title,
  description,
  children,
  backStep,
  backIcon,
  setStep,
}: OnboardingProps) => {
  const router = useRouter();

  return (
    <div className="max-w-md flex flex-col gap-8">
      <div className="flex flex-col gap-2">
        <div className="text-3xl font-bold text-[#0F172A] text-center">
          {title}
        </div>
        {description && (
          <p className="text-[#64748B] text-center text-sm">{description}</p>
        )}
      </div>
      {children}
      <div
        className={`flex  text-sm w-full ${backIcon ? 'justify-between' : 'justify-center'}`}
      >
        {backStep && setStep && (
          <button
            className="flex items-center gap-1 text-[#0F172A]"
            onClick={() => setStep(backStep)}
          >
            {backIcon} Back
          </button>
        )}
        <button
          className={` text-[#94A3B8] `}
          onClick={() => router.push('/home')}
        >
          Skip for now
        </button>
      </div>
    </div>
  );
};

export default OnboardingWrapper;
