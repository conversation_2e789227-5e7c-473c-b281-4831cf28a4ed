interface ProfileOption {
  id: string;
  title: string;
  description?: string;
  icon?: React.ReactNode;
}

interface SelectProfileProps {
  options: ProfileOption[];
  selected: string[];
  onToggle: (id: string) => void;
}

const SelectProfile = ({ options, selected, onToggle }: SelectProfileProps) => {
  return (
    <div className="flex flex-col w-full gap-3">
      {options.map((opt) => (
        <div
          key={opt.id}
          onClick={() => onToggle(opt.id)}
          className={`rounded-xl p-4 hover:shadow-md cursor-pointer flex flex-row items-center gap-4 transition ${
            selected.includes(opt.id) ? 'bg-[#1E293B]' : 'bg-[#F3F4F6]'
          }`}
        >
          {opt.icon && (
            <div
              className={
                selected.includes(opt.id) ? 'text-white' : 'text-[#0F172A]'
              }
            >
              {opt.icon}
            </div>
          )}

          <div className="flex-1">
            <p
              className={`font-semibold ${
                selected.includes(opt.id) ? 'text-white' : 'text-[#0F172A]'
              }`}
            >
              {opt.title}
            </p>
            {opt.description && (
              <p
                className={`text-sm ${
                  selected.includes(opt.id) ? 'text-white' : 'text-[#64748B]'
                }`}
              >
                {opt.description}
              </p>
            )}
          </div>

          <input
            type="checkbox"
            className="accent-white"
            checked={selected.includes(opt.id)}
            readOnly
          />
        </div>
      ))}
    </div>
  );
};

export default SelectProfile;
