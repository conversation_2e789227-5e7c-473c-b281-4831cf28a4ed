interface Region {
  id: string;
  name: string;
  countries: string;
}

interface Select {
  options: Region[];
  selected: string;
  onToggle: (id: string) => void;
}

const SelectRegions = ({ options, selected, onToggle }: Select) => {
  return (
    <div className="flex flex-col w-full gap-3">
      {options.map((opt) => (
        <div
          key={opt.id}
          onClick={() => onToggle(opt.id)}
          className={`rounded-xl p-4 hover:shadow-md cursor-pointer flex flex-row items-center gap-4 transition ${
            selected.includes(opt.id) ? 'bg-[#1E293B]' : 'bg-[#F3F4F6]'
          }`}
        >
          <div className="flex-1">
            <p
              className={`font-semibold ${
                selected.includes(opt.id) ? 'text-white' : 'text-[#0F172A]'
              }`}
            >
              {opt.name}
            </p>
            {opt.countries && (
              <p
                className={`text-sm ${
                  selected.includes(opt.id) ? 'text-white' : 'text-[#64748B]'
                }`}
              >
                {opt.countries}
              </p>
            )}
          </div>

          <input
            type="checkbox"
            className="accent-white"
            checked={selected.includes(opt.id)}
            readOnly
          />
        </div>
      ))}
    </div>
  );
};

export default SelectRegions;
