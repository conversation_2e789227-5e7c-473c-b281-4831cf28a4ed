'use client';
import { useAppDispatch } from '@/app/hook';
import { setLoading } from '@/app/store/ui';
import { Button } from '@/components/Button';
import { DuplicateMail } from '@/components/modal/DuplicateMail';
import { TermAndPolicy } from '@/components/modal/TermAndPolicy';
import PasswordValidation from '@/components/PasswordValidation';
import { checkEmailExist, signInWithProvider, signup } from '@/utils/api';
import { User } from 'next-auth';
import { signIn, signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Suspense, useCallback, useEffect, useState } from 'react';
import Input from '../../../components/Input';
import { forwardArrow, google } from '../../../utils/icon';
export default function Register() {
  const RegisterContent = () => {
    const router = useRouter();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isContinue, setIsContinue] = useState(false);
    const [isAlreadyRegistered, setIsAlreadyRegistered] = useState(false);
    const [isSuccessful, setIsSuccessful] = useState(false);
    const [isTermAndPolicy, setIsTermAndPolicy] = useState(false);
    const [isGoogleRegisterSuccessfully, setIsGoogleRegisterSuccessfully] =
      useState(false);

    const { data: session, status } = useSession();
    const dispatch = useAppDispatch();

    const [validation, setValidation] = useState({
      length: false,
      uppercase: false,
      lowercase: false,
      noSpace: false,
      specialChar: false,
    });

    const passwordChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = e.target;
      setPassword(value);
      setValidation({
        length: value.length >= 8,
        uppercase: /[A-Z]/.test(value),
        lowercase: /[a-z]/.test(value),
        noSpace: !/\s/.test(value) && value !== '',
        specialChar: /[!@#$%^&*(),.?":{}|<>]/.test(value),
      });
    };

    const isEmailValid = () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    const isPasswordValid = () => {
      return Object.values(validation).every(Boolean);
    };

    const continueHandler = async () => {
      dispatch(setLoading(true));
      if (isGoogleRegisterSuccessfully) {
        validateUser(session?.user!);
        return;
      }
      // if (!continueCondition()) {
      //   return;
      // }
      if (isEmailValid() && isPasswordValid()) {
        try {
          await signup({ email, password });
        } catch (error: any) {
          const { id, is_pending } = error.response.data.data.flows[2];
          if (id === 'verify_email' && is_pending === true) {
            setIsSuccessful(true);
          }
        } finally {
          dispatch(setLoading(false));
        }
      } else if (isEmailValid()) {
        const isEmailExisted = await checkEmailExist(email);
        if (isEmailExisted) {
          setIsAlreadyRegistered(true);
        } else {
          setIsContinue(true);
        }
        dispatch(setLoading(false));
      }
    };

    const continueCondition = () => {
      if (isGoogleRegisterSuccessfully) {
        return true;
      }
      if (isContinue) {
        return isEmailValid() && isPasswordValid();
      } else {
        return isEmailValid();
      }
    };

    const signUpWithGoogle = () => {
      signIn('google');
    };

    const validateUser = useCallback(async (user: User) => {
      try {
        const res = await signInWithProvider(
          user.provider!,
          'login',
          user.idToken!,
          user.accessToken!
        );
        if (res.status === 200) {
          signOut({ callbackUrl: '/account-created' });
        }
      } catch (error) {
        console.log('Validation error:', error);
      }
    }, []);

    useEffect(() => {
      if (status === 'authenticated') {
        // validateUser(session.user);
        setIsGoogleRegisterSuccessfully(true);
        setIsContinue(true);
        setEmail(session.user.email!);
      }
    }, [session, status, validateUser, router]);

    const changeEmailHandler = () => {
      setIsContinue(false);
      setIsGoogleRegisterSuccessfully(false);
    };
    return (
      <>
        <div>
          <div className="flex flex-col items-center">
            <div className="w-full flex-1 ">
              <div className="mx-auto max-w-xs">
                <Image
                  width={54}
                  height={36}
                  src="../assets/logo.svg"
                  alt="logo"
                ></Image>
                <h1 className="mt-3 text-2xl xl:text-3xl font-semibold whitespace-nowrap text-nowrap">
                  {isSuccessful
                    ? 'We have sent an email'
                    : 'Create your account'}
                </h1>
              </div>
            </div>
            {isSuccessful ? (
              <div className="w-full flex-1 mt-3">
                <div className="mx-auto max-w-xs">
                  <p className="text-sm font-sans font-normal text-[#64748B]">
                    An email confirming registration has been sent to&nbsp;
                    <span className="text-[#1E40AF] underline">{email}</span>.
                  </p>
                  <p className="mt-3 text-sm font-sans font-normal text-[#64748B]">
                    Please check your inbox and follow the instructions. Or you
                    can choose to{' '}
                    <span className="text-[#1E40AF] underline">
                      resend the email
                    </span>
                    .
                  </p>
                </div>
              </div>
            ) : (
              <>
                <div className="w-full flex-1 mt-8">
                  <div className="mx-auto max-w-xs">
                    {!isGoogleRegisterSuccessfully ? (
                      <Input
                        value={email}
                        onChangeFunc={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        label="Email"
                        type="email"
                      />
                    ) : (
                      <div className="flex gap-2 items-center">
                        <span className="text-sm font-sans font-medium text-[#0F172A]">
                          {email}
                        </span>
                        <span
                          className="cursor-pointer text-sm font-sans font-medium text-[#1E40AF]"
                          onClick={changeEmailHandler}
                        >
                          Change
                        </span>
                      </div>
                    )}

                    {isContinue && email && !isGoogleRegisterSuccessfully && (
                      <>
                        <Input
                          className="mt-5"
                          value={password}
                          onChangeFunc={passwordChangeHandler}
                          placeholder="Password"
                          label="Password"
                          type="password"
                        />
                        <PasswordValidation validation={validation} />
                      </>
                    )}
                    <p className="mt-5 text-xs text-[#64748B] font-normal text-center">
                      By clicking Continue or Sign up with, you agree to our{' '}
                      <a
                        className="text-[#1E3A8A] cursor-pointer font-medium"
                        onClick={() => setIsTermAndPolicy(true)}
                      >
                        Term and Privacy Policy
                      </a>
                    </p>
                    <Button
                      onClickFunc={continueHandler}
                      isValid={continueCondition()}
                    >
                      Continue
                    </Button>
                  </div>

                  <div className="mx-auto max-w-xs my-6 border-b text-center">
                    <div className="leading-none px-2 inline-block text-sm text-gray-600 tracking-wide font-medium bg-white transform translate-y-1/2">
                      or
                    </div>
                  </div>

                  <div className="flex flex-col items-center">
                    <button
                      onClick={signUpWithGoogle}
                      className="w-full max-w-xs font-bold shadow-sm rounded-lg py-2 bg-[#346EF1] text-gray-800 flex items-center justify-center transition-all duration-300 ease-in-out focus:outline-none hover:shadow focus:shadow-sm focus:shadow-outline mt-5"
                    >
                      <div className="bg-white p-2 rounded-full">{google}</div>
                      <span className="ml-4 text-white">
                        Sign up with Google
                      </span>
                    </button>
                  </div>
                </div>
                <div>
                  <p className="flex items-center justify-center mt-6 text-xs text-gray-600 text-center">
                    Already have an account? &nbsp;
                    <Link
                      href="/auth/login"
                      className="text-[#1E40AF] text-sm font-medium flex justify-center items-center gap-2"
                    >
                      Log in{forwardArrow}
                    </Link>
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
        {isAlreadyRegistered && (
          <DuplicateMail setIsAlreadyRegistered={setIsAlreadyRegistered} />
        )}
        {isTermAndPolicy && (
          <TermAndPolicy setIsTermAndPolicy={setIsTermAndPolicy} />
        )}
      </>
    );
  };

  return (
    <Suspense fallback={<p>Loading...</p>}>
      <RegisterContent />
    </Suspense>
  );
}
