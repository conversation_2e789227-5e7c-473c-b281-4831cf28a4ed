 // const [is2StepVetification, setIs2StepVetification] = useState(false);
  // const [OTP, setOTP] = useState('');
  // const [OTPError, setOTPError] = useState('');

  // const OTPChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   setOTP(e.target.value);
  // };

  // Validate OTP
  // const validateOTP = () => {
  //   if (OTP === '123456') {
  //     router.push('/home');
  //   } else {
  //     setOTPError('Verification code does not match');
  //   }
  // };

  {
    /* Modal Overlay */
  }
  {
    /* {is2StepVetification && (
        <div
          className="relative z-10"
          aria-labelledby="modal-title"
          role="dialog"
          aria-modal="true"
        >
          <div
            className="fixed inset-0 bg-gray-500/75 transition-opacity"
            aria-hidden="true"
          ></div>

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <p
                        className="text-lg font-sans font-medium text-[#0F172A]"
                        id="modal-title"
                      >
                        2-Step Vertication
                      </p>
                      <div className="mt-4">
                        <p className="text-sm font-sans font-normal text-[#1E293B]">
                          Get a verification code from your authentication app
                        </p>
                      </div>
                      <div className="mt-6">
                        <Input value={OTP} onChangeFunc={OTPChangeHandler} />
                      </div>
                      {!!OTPError && (
                        <div className="mt-6">
                          <p className="text-center text-sm font-sans font-normal text-[#B91C1C]">
                            {OTPError}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    onClick={validateOTP}
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-[#FACC15] px-8 py-3 text-sm font-medium text-dark shadow-xs hover:bg-[#FACC15] sm:ml-3 sm:w-auto"
                  >
                    Confirm
                  </button>
                  <button
                    onClick={() => setIs2StepVetification(false)}
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-8 py-3 text-sm font-medium text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div> 
        </div>
      )} */
  }