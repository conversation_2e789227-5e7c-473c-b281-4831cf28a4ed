'use client';
import { useAppDispatch } from '@/app/hook';
import { setLoading } from '@/app/store/ui';
import { Button } from '@/components/Button';
import AuthenticatorSetup from '@/components/modal/AuthenticatorSetup';
import { checkEmailExist, login, signInWithProvider } from '@/utils/api';
import Cookies from 'js-cookie';
import { User } from 'next-auth';
import { signIn, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import Input from '../../../components/Input';
import { forwardArrow, google } from '../../../utils/icon';

export default function Login() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isContinue, setIsContinue] = useState(false);
  const [is2FARequired, setIs2FARequired] = useState(false);
  const [sessionToken, setSessionToken] = useState('');
  const dispatch = useAppDispatch();

  const { data: session, status } = useSession();

  const validateUser = useCallback(
    async (user: User) => {
      try {
        const res = await signInWithProvider(
          user.provider!,
          'login',
          user.idToken!,
          user.accessToken!
        );
        if (res.status === 200) {
          const { session_token } = res.data.meta;
          Cookies.set('session_token', session_token, {
            expires: 1,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'Strict',
          });
          router.push('/home');
        }
      } catch (error) {
        console.log('Validation error:', error);
        // signOut();
      }
    },
    [router]
  );

  useEffect(() => {
    const sessionToken = Cookies.get('session_token');
    if (sessionToken) {
      router.push('/home');
    }
  }, [router]);

  useEffect(() => {
    if (status === 'authenticated') {
      validateUser(session.user);
    }
  }, [session, status, validateUser, router]);

  const isEmailValid = () => {
    return email.length > 0;
  };

  const loginHandler = async () => {
    dispatch(setLoading(true));
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(email)) {
      if (isEmailValid() && isContinue) {
        let response;
        try {
          response = await login({ email, password });
          if (response.status === 200) {
            const { session_token, first_login } = response.data;
            Cookies.set('session_token', session_token, {
              expires: 1,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'Strict',
            });
            if (first_login) {
              router.push('/auth/onboarding');
            } else {
              router.push('/home');
            }
          }
        } catch (error: any) {
          const data = error.response?.data;
          if (
            error.response?.status === 401 &&
            data?.data?.flows?.some(
              (flow: any) => flow.id === 'mfa_authenticate' && flow.is_pending
            ) &&
            data?.meta?.session_token
          ) {
            setSessionToken(data.meta.session_token);
            setIs2FARequired(true);
            return;
          } else if (error.response?.status === 401) {
            setPasswordError('Please activate your account');
          } else if (error.response?.status === 400) {
            setPasswordError('Incorrect password');
          }
        }
      } else {
        const emailExist = await checkEmailExist(email);
        dispatch(setLoading(false));
        if (!emailExist) {
          setEmailError('No account found with that email.');
        } else {
          setIsContinue(true);
        }
      }
    } else {
      setEmailError('Enter a valid email address');
    }
    dispatch(setLoading(false));
  };

  const emailChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  const passwordChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setPasswordError('');
  };

  const googleLoginHandler = () => {
    signIn('google');
  };

  let loginButtonText = 'Continue with email';
  if (isEmailValid() && isContinue) {
    loginButtonText = 'Log in';
  }

  return (
    <div>
      <div className="flex flex-col items-center">
        <div className="w-full flex-1 ">
          <div className="mx-auto max-w-xs space-y-3">
            <Image
              width={54}
              height={36}
              src="../assets/logo.svg"
              alt="logo"
            ></Image>
            <h1 className="text-2xl xl:text-3xl font-semibold whitespace-nowrap text-nowrap">
              Log in to your account
            </h1>
          </div>
        </div>
        <div className="w-full flex-1 mt-8">
          <form
            onSubmit={(e) => e.preventDefault()}
            className="mx-auto max-w-xs"
          >
            {isContinue ? (
              <div className="flex gap-2 items-center">
                <span className="text-sm font-sans font-medium text-[#0F172A]">
                  {email}
                </span>
                <span
                  className="cursor-pointer text-sm font-sans font-medium text-[#1E40AF]"
                  onClick={() => setIsContinue(false)}
                >
                  Change
                </span>
              </div>
            ) : (
              <Input
                value={email}
                onChangeFunc={emailChangeHandler}
                placeholder="<EMAIL>"
                label="Email"
                type="email"
                inputClassName={emailError && '!border-[#EF4444]'}
              />
            )}
            <div>
              {emailError && (
                <div className="text-[#B91C1C] font-sans font-normal text-sm mt-2">
                  {emailError}
                </div>
              )}
            </div>
            {isContinue && email && (
              <>
                <Input
                  className="mt-5"
                  value={password}
                  onChangeFunc={passwordChangeHandler}
                  placeholder="Password"
                  label="Password"
                  type="password"
                />
                <div>
                  {passwordError && (
                    <div className="text-[#B91C1C] font-sans font-normal text-sm mt-2">
                      {passwordError}
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between mt-5">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="remember"
                        aria-describedby="remember"
                        type="checkbox"
                        className="w-4 h-4 rounded accent-[#EAB308]"
                      />
                    </div>
                    <div className="ml-2 text-sm">
                      <label htmlFor="remember" className="text-[#0A0A0A]">
                        Remember me
                      </label>
                    </div>
                  </div>
                  <Link
                    href={`/auth/reset-password?email=${email}`}
                    className="text-sm font-medium text-[#1E40AF] hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
              </>
            )}
            <Button onClickFunc={loginHandler} isValid={isEmailValid()}>
              {loginButtonText}
            </Button>
          </form>

          <div className="mx-auto max-w-xs my-6 border-b text-center">
            <div className="leading-none px-2 inline-block text-sm text-gray-600 tracking-wide font-medium bg-white transform translate-y-1/2">
              or
            </div>
          </div>
          <div className="flex flex-col items-center">
            <button
              onClick={googleLoginHandler}
              className="w-full max-w-xs font-bold shadow-sm rounded-lg py-2 bg-[#346EF1] text-gray-800 flex items-center justify-center transition-all duration-300 ease-in-out focus:outline-none hover:shadow focus:shadow-sm focus:shadow-outline mt-5"
            >
              <div className="bg-white p-2 rounded-full">{google}</div>
              <span className="ml-4 text-white">Continue with Google</span>
            </button>
          </div>
        </div>
      </div>
      <div>
        <p className="flex items-center justify-center mt-6 text-xs text-gray-600 text-center">
          New to APM? &nbsp;
          <Link
            href="/auth/register"
            className="text-[#1E40AF] text-sm font-medium flex justify-center items-center gap-2"
          >
            Register
            {forwardArrow}
          </Link>
        </p>
      </div>
      {is2FARequired && (
        <AuthenticatorSetup
          onClose={() => setIs2FARequired(false)}
          onConfirm={() => {
            setIs2FARequired(false);
            router.push('/home');
          }}
          type="verify"
          has2FA={true}
          sessionToken={sessionToken}
        />
      )}
    </div>
  );
}
