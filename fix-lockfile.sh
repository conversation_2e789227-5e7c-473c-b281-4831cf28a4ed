#!/bin/bash

# <PERSON>ript to fix package-lock.json sync issues

echo "🔧 Fixing package-lock.json sync issues..."

# Remove existing lock file and node_modules
echo "📦 Cleaning existing dependencies..."
rm -rf node_modules
rm -f package-lock.json

# Reinstall dependencies
echo "⬇️  Reinstalling dependencies..."
npm install

echo "✅ Dependencies reinstalled successfully!"
echo "🐳 You can now run Docker build again."

# Optional: Run Docker build
read -p "Do you want to build the Docker image now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🐳 Building Docker image..."
    docker build -t proman-fe .
fi
