# ProMan Frontend

A comprehensive Amazon Product Management platform built with Next.js, providing tools for managing Amazon listings, A+ content, user management, and store operations.

## 🚀 Features

### Core Functionality

- **Product Listing Management** - Create, edit, and manage Amazon product listings
- **A+ Content Management** - Design and manage enhanced brand content
- **User Management** - Role-based access control with permission matrix
- **Store Management** - Comprehensive store operations and analytics
- **Multi-language Support** - Internationalization with language picker

### Authentication & Security

- **Multi-provider Authentication** - Support for Google and Amazon OAuth
- **Two-Factor Authentication** - Enhanced security with 2FA setup
- **Role-based Permissions** - Granular permission system for different user roles
- **Secure Password Management** - Password reset and recovery features

### User Interface

- **Responsive Design** - Mobile-first approach with Tailwind CSS
- **Modern UI Components** - Reusable components with consistent design
- **Interactive Modals** - Portal-based modal system for better UX
- **Real-time Notifications** - Toast notifications and alerts
- **Dark/Light Theme Support** - Theme switching capabilities

## 🛠 Tech Stack

### Frontend Framework

- **Next.js 15.2.2** - React framework with App Router
- **React 18** - Latest React with concurrent features
- **TypeScript** - Type-safe development

### Styling & UI

- **Tailwind CSS 4** - Utility-first CSS framework
- **Shopify Polaris** - Design system components
- **React Icons** - Comprehensive icon library
- **Lottie React** - Animation library

### State Management

- **Redux Toolkit** - Modern Redux with RTK Query
- **React Redux** - React bindings for Redux

### Authentication

- **NextAuth.js 5** - Authentication library with OAuth support
- **JWT Tokens** - Secure token-based authentication

### Development Tools

- **ESLint** - Code linting with custom rules
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── auth/              # Authentication pages
│   │   ├── login/         # Login page
│   │   ├── register/      # Registration page
│   │   ├── onboarding/    # User onboarding
│   │   └── reset-password/ # Password reset
│   ├── home/              # Main application
│   │   ├── aplus-content/ # A+ content management
│   │   ├── setting/       # User settings
│   │   │   ├── general/   # General settings
│   │   │   ├── security/  # Security settings
│   │   │   ├── user-management/ # User management
│   │   │   └── subscription-and-billing/ # Billing
│   │   └── your-store/    # Store management
│   └── api/               # API routes
├── components/            # Reusable UI components
│   ├── modal/            # Modal components
│   ├── modals/           # Specific modal implementations
│   └── filters/          # Filter components
├── utils/                # Utility functions
├── types/                # TypeScript type definitions
└── auth.ts              # Authentication configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd ProMan-FE
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Configure the following environment variables:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret
AUTH_AMAZON_ID=your-amazon-client-id
AUTH_AMAZON_SECRET=your-amazon-client-secret
```

4. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🐳 Docker Support

The project includes Docker support for containerized deployment:

### Development

```bash
docker-compose -f docker-compose.dev.yml up
```

### Production

```bash
docker-compose up
```

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint for code linting
- `npm run format` - Format code with Prettier

## 🔧 Configuration

### Next.js Configuration

The project uses Next.js with standalone output for Docker optimization:

- Standalone output for containerization
- Image optimization enabled
- TypeScript configuration with strict mode

### Tailwind CSS

Custom Tailwind configuration with:

- Custom color palette
- Extended spacing and typography
- Component-specific utilities

## 🏗 Architecture

### Authentication Flow

1. **OAuth Integration** - Google and Amazon provider support
2. **Session Management** - NextAuth.js handles session persistence
3. **Role-based Access** - Different permission levels for users
4. **2FA Support** - Optional two-factor authentication

### State Management

- **Redux Toolkit** for global state management
- **RTK Query** for API data fetching and caching
- **Local state** with React hooks for component-specific state

### Component Architecture

- **Atomic Design** principles with reusable components
- **Portal-based Modals** for better UX and accessibility
- **Responsive Design** with mobile-first approach

## 🎨 UI Components

### Core Components

- **Button** - Customizable button component with variants
- **Input** - Form input with validation support
- **Modal** - Portal-based modal with backdrop and keyboard support
- **Sidebar** - Navigation sidebar with collapsible sections
- **Header** - Application header with user menu
- **Toggle** - Switch component for boolean settings

### Specialized Components

- **PasswordValidation** - Real-time password strength validation
- **OTP** - One-time password input component
- **LanguagePicker** - Multi-language selection component
- **CustomSelect** - Enhanced select dropdown
- **TextArea** - Multi-line text input with auto-resize

### Modal Components

- **InviteUserModal** - User invitation with role selection
- **PermissionMatrixModal** - Role-permission matrix display
- **AuthenticatorSetup** - 2FA setup modal
- **DeleteAccount** - Account deletion confirmation
- **SecurityActivity** - Security activity notifications

## 🔐 User Roles & Permissions

### Role Hierarchy

1. **Super Admin** - Full system access and management
2. **Admin** - Administrative access with some restrictions
3. **Regional Admin** - Regional management capabilities
4. **Support Admin** - Customer support functions
5. **Team Member** - Basic operational access

### Permission Categories

- **Listings Management** - Create, edit, publish, approve, delete listings
- **Content Management** - Manage A+ content and product descriptions
- **Analytics** - View sales data and performance reports
- **User Management** - Invite users and assign roles
- **Billing** - Manage payment methods and subscriptions
- **Audit Logs** - View system activity and user actions

## 🌐 Internationalization

The application supports multiple languages and regions:

- **Language Selection** - Dynamic language switching
- **Localized Content** - Translated UI elements and messages
- **Regional Settings** - Currency and date format preferences
- **RTL Support** - Right-to-left language support

## 📊 Features Overview

### Product Management

- **Listing Creation** - Guided product listing creation
- **Bulk Operations** - Mass edit and update capabilities
- **Image Management** - Upload and organize product images
- **Inventory Tracking** - Stock level monitoring
- **Price Management** - Dynamic pricing strategies

### A+ Content

- **Visual Editor** - Drag-and-drop content builder
- **Template Library** - Pre-designed content templates
- **Preview Mode** - Real-time content preview
- **Version Control** - Content versioning and rollback
- **Approval Workflow** - Content review and approval process

### Analytics & Reporting

- **Sales Dashboard** - Real-time sales metrics
- **Performance Analytics** - Product performance insights
- **Custom Reports** - Configurable reporting system
- **Data Export** - Export data in various formats
- **Trend Analysis** - Historical data analysis

### User Management

- **Role-based Access** - Granular permission control
- **Team Collaboration** - Multi-user workspace
- **Activity Monitoring** - User action tracking
- **Invitation System** - Email-based user invitations
- **Profile Management** - User profile and preferences

## 🔧 Development Guidelines

### Code Style

- **TypeScript** - Strict type checking enabled
- **ESLint** - Custom linting rules for consistency
- **Prettier** - Automated code formatting
- **Import Organization** - Automatic import sorting

### Component Guidelines

- Use functional components with hooks
- Implement proper TypeScript interfaces
- Follow atomic design principles
- Ensure accessibility compliance
- Write comprehensive prop documentation

### State Management

- Use Redux Toolkit for global state
- Implement RTK Query for API calls
- Keep component state local when possible
- Use proper action creators and selectors

## 🚀 Deployment

### Environment Setup

1. **Production Environment Variables**
2. **Database Configuration**
3. **OAuth Provider Setup**
4. **CDN Configuration**

### Build Process

```bash
# Build for production
npm run build

# Start production server
npm run start
```

### Docker Deployment

```bash
# Build Docker image
docker build -t proman-frontend .

# Run container
docker run -p 3000:3000 proman-frontend
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow

- Follow the established code style
- Write tests for new features
- Update documentation as needed
- Ensure all checks pass before submitting PR

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:

- Check the documentation
- Review existing issues
- Contact the development team

---

**Built with ❤️ using Next.js, TypeScript, and Tailwind CSS**
