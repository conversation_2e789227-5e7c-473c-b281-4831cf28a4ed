version: '3.8'

services:
  # Main application service
  proman-fe:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # Add your environment variables here
      # - NEXTAUTH_SECRET=your-secret-here
      # - NEXTAUTH_URL=http://localhost:3000
      # - DATABASE_URL=your-database-url
    # Uncomment if you need to mount volumes for development
    # volumes:
    #   - .:/app
    #   - /app/node_modules
    #   - /app/.next
    restart: unless-stopped
    # Uncomment if you need to connect to other services
    # depends_on:
    #   - database
    #   - redis

  # Example database service (uncomment if needed)
  # database:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: proman
  #     POSTGRES_USER: proman_user
  #     POSTGRES_PASSWORD: proman_password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped

  # Example Redis service (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped

# Uncomment if you're using volumes
# volumes:
#   postgres_data:

networks:
  default:
    name: proman-network
