{"name": "aplmanagement", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@next/third-parties": "^15.4.6", "@reduxjs/toolkit": "^2.6.1", "@shopify/polaris": "^13.9.5", "@types/react-redux": "^7.1.34", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.8.3", "js-cookie": "^3.0.5", "latest": "^0.2.0", "lottie-react": "^2.4.1", "next": "15.2.2", "next-auth": "^5.0.0-beta.25", "next-qrcode": "^2.5.1", "react": "^18.0.0", "react-countup": "^6.5.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "vercel": "^41.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.24.0", "eslint-config-next": "15.2.2", "eslint-config-prettier": "^10.1.1", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "tailwindcss": "^4", "typescript": "^5"}}